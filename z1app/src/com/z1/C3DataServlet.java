/**
 * Copyright 2011 by ZineOne Inc,
 * All rights reserved.
 *
 * This software is the confidential and proprietary information
 * of ZineOne Inc
 */
package com.z1;

import com.z1.Utils.ApiUtils;
import com.z1.handler.DataMonitorHandler;
import com.z1.handler.DataMonitorStatusHandler;
import com.z1.handler.RemoteFilesHandler;
import udichi.core.App;
import udichi.core.UContext;
import udichi.core.UException;
import udichi.core.log.ULogger;
import udichi.core.util.JsonMarshaller;
import udichi.core.util.ServletUtil;
import udichi.core.util.StringUtil;
import z1.audit.ArtifactAudit;
import z1.audit.ArtifactAudit.ItemTypes;
import z1.audit.ArtifactAudit.Operations;
import z1.c3.CustomConfig;
import z1.c3.CustomConfig.Type;
import z1.c3.Journey;
import z1.c3.SystemConfig;
import z1.commons.DataPermission;
import z1.commons.EventLogCollector;
import z1.commons.HandlerUtils;
import z1.commons.ProfileTracer;
import z1.commons.encryption.AesUtil;
import z1.commons.encryption.Encryption;
import z1.core.utils.Misc;
import z1.core.utils.TimeUtils;
import z1.core.utils.Utils;
import z1.users.AccessControl;
import z1.users.User;
import z1.users.User.Fields;
import z1.users.User.Status;

import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.util.*;

/**
 * Launches the runtime environment.
 */
@SuppressWarnings("serial")
public class C3DataServlet extends HttpServlet
{
  private enum Command
  {
    activity,
    task,
    channels,
    goals,
    segments,
    session,
    signals,
    triggers,
    outcomes,
    actions,
    eventinfo,
    feed, // Gets feed for PLP review
    insights,
    registration,
    apikey,
    kb,
    systemconfig,
    deepLinks,
    agent,
    passwordReset,
    css,
    preview, // Returns html to display a message for a device
    payment, // processes payments
    journey,
    campaign,
    c1,
    campaignonce,
    cluster,
    messagepolicy,
    pushconfig,
    androidpushconfig,
    uwppushconfig,
    chromepushconfig,
    webpushconfig,
    eventmapping,
    systemstat,
    profileschema,
    eventpostprocessing,
    livelinks,
    contentcss,
    contentcss2,
    content,
    systemreset,
    testpush,
    uninstall,
    prioritizeCampaign,
    images,
    eventflow, // Returns the event-flow data to show the chart
    customaction,
    pnsentstats,
    log,
    _profiletrace,
    dashboard,
    datasheet,
    cubePermissions,
    groups,
    trustedagentconfig,
    userTask,
    audit,
    googleplaystoreconfig,
    itunesstoreconfig,
    geofence,
    props,
    sdkprops,
    passwordpolicyconfig,
    profile,
    contextattribute,
    eventprocessing,
    remotelinks,
    actionMapping,
    twilioconfig,
    sendgridconfig,
    sendgridcredentialscheck,
    mailgunconfig,
    adobeconfig,
    darkskyconfig,
    openweathermapconfig,
    bqconfig,
    scoretranserconfig,
    query,
    ml,
    streamQuery,
    entity,
    androidMethodsMappingConfig,
    sharedurl,
    testidconfig,
    interactions,
    pmodel, // Predictive models
    export,
    importartifact,
    cube,
    cubequery,
    cubequerydata,
    keepalive,
    wsconnect,
    dryrunconfig,
    modules,
    lop, // List of Properties
    qatestgroup, // QA Test group
    v1tracer,
    profileopt,
    goalStats,
    bx,
    labels,
    incidentalevent,
    dnaevent,
    sslCertUpload, 
    explore,
    sso, 
    tz,
    version,
    datasink,
    monitor,
    monitorstatus,
    conversion,
    successmetric,
    propertymapping,
    incentives,
    parseReportUrl,
    traffictagconfig,
    offers,
    remotefiles,
    notifications,
    eventseqrules,
    commandcenter;
  }

  // ..................................................
  // Gets
  @SuppressWarnings("incomplete-switch")
  public void doGet(HttpServletRequest req, HttpServletResponse resp)
      throws IOException
  {
    resp.setContentType("application/json");
    resp.setCharacterEncoding("UTF-8");

    UContext ctx = UContext.getInstance(req);

    try
    {
      //
      // We'll get the path info of the request. The path info must start with a
      // known artifact type.
      // The structure will be like /c3/data/<artifact type>/*
      // So we are expecting at least 2 parts after splitting, the first one
      // being blank
      String path = req.getPathInfo();
      String[] pathParts = path.split("/");
      if (pathParts.length < 2)
      {
        return;
      }

      // Create an array to pass for the relevant commands
      String[] subParts = new String[pathParts.length - 2];
      int j = 0;
      for (int i = 2; i < pathParts.length; i++, j++)
      {
        subParts[j] = pathParts[i];
      }

      Command cmd = Command.valueOf(pathParts[1]);
      switch (cmd)
      {
        case qatestgroup:
        {
          new com.z1.handler.QaTestGroupHandler().get().handle(ctx, subParts, req,
              resp);
          return;          
        }
        case wsconnect:
        {
          String ref = pathParts[2];
          if (ref.equals("_audience_")) {
            ref = ref.concat(pathParts[3]);
          }

          String ret = HandlerUtils.createWebsocket(ctx, ref);
          if (ret != null) resp.getWriter().print(ret);          
          return;
        }
        case task:
        {
          new com.z1.handler.TaskHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        case channels:
        {
          new com.z1.handler.ChannelsHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        case feed:
        {
          new com.z1.handler.FeedHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        case goals:
        {
          new com.z1.handler.GoalHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        case segments:
        {
          new com.z1.handler.SegmentsHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        case signals:
        {
          new com.z1.handler.SignalsHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        case triggers:
        {
          new com.z1.handler.TriggersHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        case outcomes:
        {
          new com.z1.handler.OutcomesHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        case actions:
        {
          new com.z1.handler.ActionsHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }

        case eventinfo:
        {
          new com.z1.handler.EventInfoHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        case activity:
        {
          new com.z1.handler.ActivityHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        case insights:
        {
          new com.z1.handler.InsightsHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        case registration:
        {
          new com.z1.handler.RegistrationHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }
        case apikey:
        {
          new com.z1.handler.ApikeyHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        case kb:
        {
          new com.z1.handler.KBHandler().get().handle(ctx, subParts, req, resp);
          return;
        }
        case systemconfig:
        {
          new com.z1.handler.SystemConfigHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }
        case deepLinks:
        {
          new com.z1.handler.DeepLinkHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        case agent:
        {
          new com.z1.handler.AgentHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        case css:
        {
          new com.z1.handler.CssHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        // c3/data/payments/clientToken
        case payment:
        {
          new com.z1.handler.PaymentHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        //c3/data/incentives/..
        case incentives:{
          new com.z1.handler.IncentivesHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        // c3/data/preview?device=<device_name>[&url=<url>]&header=<header>&date=<date>
        case preview:
        {
          // send back the html for specified device.
          String device = req.getParameter("device");
          if (device == null)
          {
            device = "iphone5";
          }

          // Get the url
          String url = req.getParameter("url");

          String header = req.getParameter("header");
          String date = req.getParameter("date");

          // Load the resource
          String resPath = "com/z1/resource/preview/" + device + ".html";
          InputStream is = this.getClass().getClassLoader()
              .getResourceAsStream(resPath);
          String payload = Misc.readString(is);

          // Substitute variable the url value
          Map<String, Object> map = new java.util.HashMap<>(4);
          map.put("url", url);
          map.put("header", header);
          map.put("date", date);

          payload = StringUtil.substitueValue(payload, map, "", null);

          // payload = payload.replaceAll("%z1.content.url%", url);

          resp.setContentType("text/html");
          resp.getWriter().print(payload);

          return;
        }
        case journey:
        {
          new com.z1.handler.JourneyHandler(Journey.Type.journey).get()
              .handle(ctx, subParts, req, resp);
          return;
        }
        case campaignonce:
        {
          new com.z1.handler.JourneyHandler(Journey.Type.c1).get().handle(ctx,
              subParts, req, resp);
          return;
        }
        case campaign:
        {
          new com.z1.handler.JourneyHandler(Journey.Type.campaign).get()
              .handle(ctx, subParts, req, resp);
          return;
        }
        case interactions:
        {
          new com.z1.handler.InteractionMetricsHandler().get().handle(ctx,
              subParts, req, resp);
          return;
        }
        case datasink:
        {
          new com.z1.handler.DatasinkHandler().get().handle(ctx,
              subParts, req, resp);
          return;
        }
        // c3/data/messagepolicy
        case messagepolicy:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.messagePolicy, true);
          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        case dryrunconfig:
        {
          new com.z1.handler.DryRunConfigHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }
        
        // c3/data/pushconfig
        case pushconfig:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.ipnCert, true);
          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        
        // c3/data/androidpushconfig
        case androidpushconfig:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.androidPushConfig, true);
          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        
        // c3/data/uwppushconfig
        case uwppushconfig:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.uwpPushConfig, true);
          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        
        // c3/data/chromepushconfig
        case chromepushconfig:
        {
          return;
        }        
        // c3/data/webpushconfig
        case webpushconfig:
        {
          
          new com.z1.handler.WebPushConfigHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }        
        // c3/data/eventmapping
        case eventmapping:
        {          
          new com.z1.handler.EventMappingHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }
        // c3/data/propertymapping
        case propertymapping:
        {
          new com.z1.handler.PropertyMappingHandler().get().handle(ctx, subParts,
                  req, resp);
          return;
        }
        // c3/data/traffictagconfig
        case traffictagconfig:
        {
          new com.z1.handler.TrafficTagConfigHandler().get().handle(ctx, subParts,
                  req, resp);
          return;
        }
        // c3/data/notifications
        case notifications:
        {
          new com.z1.handler.NotificationConfigHandler().get().handle(ctx,
              subParts, req, resp);
          return;
        }
        // c3/data/systemstat
        case systemstat:
        {          
          new com.z1.handler.SystemStatHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }
        
        // c3/data/profileschema
        case profileschema:
        {          
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.profileSchema, true);
          if (ccs == null)
          {
            resp.getWriter().print("[]");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        
        // c3/data/eventpostprocessing
        case eventpostprocessing:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.eventPostProcessing, true);
          if (ccs == null)
          {
            resp.getWriter().print("[]");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        case explore:
        {
          new com.z1.handler.DataExplorerHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }
        case cube: 
        {
          new com.z1.handler.CubeHandler().get().handle(ctx, subParts, req, resp);
          return;
        }
        case cubequery:
        {
          new com.z1.handler.CubeQueryHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        
        // c3/data/livelinks
        case livelinks:
        {
          
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.livelinks, true);
          if (ccs == null)
          {
            resp.getWriter().print("[]");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        // c3/data/contentcss/<os>[?type=<type>]
        case contentcss:
        {
          String payload = null;

          // Get the OS from the request
          String os = null;
          if (subParts.length > 0)
          {
            os = subParts[0];
          }

          String type = req.getParameter("type");
          Type t = null;

          if ("ios".equalsIgnoreCase(os))
          {
            if ("tablet".equalsIgnoreCase(type))
            {
              t = Type.iosTabContentCss;
            }
            else
            {
              t = Type.iosPhoneContentCss;
            }
          }
          else if ("android".equalsIgnoreCase(os))
          {
            if ("tablet".equalsIgnoreCase(type))
            {
              t = Type.androidTabContentCss;
            }
            else
            {
              t = Type.androidPhoneContentCss;
            }
          }

          CustomConfig ccs = CustomConfig.load(ctx, t, true);
          if (ccs == null)
          {
            resp.getWriter().print("");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        case contentcss2:
        {
          new com.z1.handler.ContentCssHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }
        case systemreset:
        {
          // c3/data/systemreset
          new com.z1.handler.SystemResetHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }
        case prioritizeCampaign:
        {
          // c3/data/livelinks
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.prioritizeCampaign, true);
          if (ccs == null)
          {
            resp.getWriter().print("[]");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        case images:
        {
          new com.z1.handler.ImageDataHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        case eventflow:
        {
          new com.z1.handler.EventFlowHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }

        case customaction:
        {
          new com.z1.handler.CustomActionHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }

        case pnsentstats:
        {
          new com.z1.handler.PNSentStatsHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }

        // c3/data/log/start OR c3/data/log/stop
        case log:
        {
          if (subParts.length < 1) return;
          String subCmd = subParts[0];

          if ("start".equalsIgnoreCase(subCmd))
          {
            String query = ctx.getNamespace();
            Map<String, String[]> reqMap = req.getParameterMap();
            if (reqMap != null)
            {

              query = reqMap.entrySet().stream()
                  .filter(k -> !k.getKey().equals("namespace")).limit(1)
                  .map(p -> p.getKey() + "=" + p.getValue()[0])
                  .reduce((p1, p2) -> p1 + "&" + p2)
                  .map(s -> ctx.getNamespace() + "?" + s)
                  .orElse(ctx.getNamespace());
            }
            // expected query in format with filter:
            // <namespace>?<eventName|deviceId|..>=<eventName|deviceId|...>
            // no filter: <namespace>
            EventLogCollector.start(query);
            ArtifactAudit.newInstance(ctx, ItemTypes.eventLogger,
                "Event Logger", "Event Logger", Operations.start).save();
          }
          else if ("stop".equalsIgnoreCase(subCmd))
          {
            EventLogCollector.stop(ctx.getNamespace());
            ArtifactAudit.newInstance(ctx, ItemTypes.eventLogger,
                "Event Logger", "Event Logger", Operations.stop).save();
          }

          return;
        }

        // c3/data/_profiletrace/<profile-id>
        case _profiletrace:
        {
          if (subParts.length < 1) return;
          String profileId = subParts[0];

          String ret = null;

          if ("null".equalsIgnoreCase(profileId))
          {
            ProfileTracer.endTraceProfile(ctx);
            ret = "Profile trace is disabled";
          }
          else
          {
            ProfileTracer.startTraceProfile(ctx, profileId);
            InetAddress ip = InetAddress.getLocalHost();
            ret = String.format("Profile trace initiated on: %s [%s]",
                ip.getHostName(), ip.getHostAddress());
          }

          resp.getWriter().print(ret);
          return;
        }

        // c3/data/dashboard/<ootb | shared>
        case dashboard:
        {
          // Returns the list of published custom dashboards - name, desc etc
          // But not the payload. Payload will be loaded from the definition name
          // itself.
          
          new com.z1.handler.DashboardHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }
        case datasheet:
        {
          new com.z1.handler.DatasheetHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }
        // c3/data/cubePermissions
        case cubePermissions:
        {
          // check if for this cube and Namespace, permission is allowed to
          // record data
          Map<String, Object> payload = new DataPermission(ctx)
              .getAllCubesPermission();
          Map<String, Object> map = new HashMap<>(1);

          map.put("permissions", payload);
          String retPayload = new JsonMarshaller().serialize(map);
          resp.getWriter().print(retPayload);
          return;
        }
        // c3/data/groups
        case groups:
        {
          new com.z1.handler.GroupsHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        // c3/data/trustedagentconfig
        case trustedagentconfig:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.trustedAgentConfig, true);
          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        // c3/data/userTask/all
        case userTask:
        {

          new com.z1.handler.UserTaskHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }

        case audit:
        {
          new com.z1.handler.AuditHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        
        // c3/data/googleplaystoreconfig
        case googleplaystoreconfig:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.googlePlayStoreConfig, true);
          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }

        // c3/data/itunesstoreconfig
        case itunesstoreconfig:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.iTunesStoreConfig, true);
          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }

        case geofence:
        {
          new com.z1.handler.GeofenceHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }

        case props:
        {
          String payload = null;

          // Get the deviceType and os from the QS param and convert it to
          // initCap
          String deviceType = req.getParameter("devicetype");
          String os = req.getParameter("os");
          deviceType = deviceType.substring(0, 1).toUpperCase()
              + deviceType.substring(1).toLowerCase();
          os = os.substring(0, 1).toUpperCase() + os.substring(1).toLowerCase();

          String type = "props" + os + deviceType;

          CustomConfig ccs = CustomConfig.load(ctx, Type.valueOf(type), true);
          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            Map<String,Object> out = new JsonMarshaller().readAsMap(payload);
            
            String key =  os.toLowerCase()+"."+ deviceType.toLowerCase() + ".tts.value"; 
            @SuppressWarnings("unchecked")
            Map<String,Object> map = (Map<String, Object>) SystemConfig.getValue(ctx,key);
              if(map!=null && map.containsKey("props")) {
                out.put("tts",map.get("props"));
            }

            String ttlkey = os.toLowerCase() + "." + deviceType.toLowerCase()
                + ".ttl.value";

            String ttl = (String) SystemConfig.getValue(ctx, ttlkey);
            if (ttl != null && !ttl.isEmpty())
            {
              out.put("ttl", ttl);
            }
          
            resp.getWriter().print(new JsonMarshaller().serialize(out));
          }
          return;
        }

        case sdkprops:
        {
          String payload = null;

          String deviceType = req.getParameter("devicetype");
          String os = req.getParameter("os");
          deviceType = deviceType.substring(0, 1).toUpperCase()
              + deviceType.substring(1).toLowerCase();
          os = os.substring(0, 1).toUpperCase() + os.substring(1).toLowerCase();

          String type = "sdkprops" + os + deviceType;

          CustomConfig ccs = CustomConfig.load(ctx, Type.valueOf(type), true);
          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            Map<String,Object> out = new JsonMarshaller().readAsMap(payload);
            String key =  os.toLowerCase()+"."+ deviceType.toLowerCase() + ".tts.value";
            @SuppressWarnings("unchecked")
            Map<String,Object> map = (Map<String, Object>) SystemConfig.getValue(ctx,key);
              if(map!=null && map.containsKey("sdkprops")) {
                out.put("tts",map.get("sdkprops"));
            }
                      
            resp.getWriter().print(new JsonMarshaller().serialize(out));
          }
          return;
        }

        case passwordpolicyconfig:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.passwordPolicyConfig, true);
          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }

        case profile:
        {
          new com.z1.handler.ProfileHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        // c3/data/contextattribute
        case contextattribute:
        {
          new com.z1.handler.ContextAttributeHandler().get().handle(ctx,
              subParts, req, resp);
          return;
        }
        // c3/data/eventprocessing
        case eventprocessing:
        {
          new com.z1.handler.EventPostProcessingHandler().get().handle(ctx,
              subParts, req, resp);
          return;
        }
        case remotelinks:
        {
          new com.z1.handler.RemoteLinkHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }
        // c3/data/actionMapping/
        case actionMapping:
        {
          new com.z1.handler.ActionMappingHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }

        case twilioconfig:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.twilioConfig);
          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        case sendgridconfig:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.sendGridConfig, true);

          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        case mailgunconfig:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.mailGunConfig, true);

          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        case adobeconfig:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.adobeConfig, true);

          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        case darkskyconfig:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.darkSkyConfig, true);

          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        case bqconfig:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.bqConfig, true);
          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        case scoretranserconfig:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.scoreTransferConfig, true);
          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        case openweathermapconfig:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.openWeatherMapConfig, true);

          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        // c3/data/contextformula
        case streamQuery:
        {
          new com.z1.handler.StreamQueryHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }
        // c3/data/entity
        case entity:
        {
          new com.z1.handler.EntityHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        case query:
        {
          new com.z1.handler.QueryDataHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        case ml:
        {
          new com.z1.handler.MLHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }
        case androidMethodsMappingConfig:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx,
              Type.androidMethodsMappingConfig, true);
          if (ccs == null)
          {
            resp.getWriter().print("{}");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        // c3/data/sharedurl
        case sharedurl:
        {
          new com.z1.handler.SharedUrlHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        // c3/data/testidconfig
        case testidconfig:
        {
          String payload = null;
          CustomConfig ccs = CustomConfig.load(ctx, Type.testIdConfig, true);
          if (ccs == null)
          {
            resp.getWriter().print("[]");
          }
          else
          {
            payload = ccs.getPayload();
            resp.getWriter().print(payload);
          }
          return;
        }
        case pmodel:
        {
          new com.z1.handler.PModelHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }

        case export:
        {
          new com.z1.handler.ArtifactPackageHandler().get().handle(ctx,
              subParts, req, resp);
          return;
        }

        case keepalive:
        {
          resp.getWriter().print("{\"status\": \"success\"}");
          return;
        }

        case uninstall:
        {
          new com.z1.handler.UninstallHandler().get().handle(ctx, subParts, req,
              resp);
        }
        case modules:
        {
          new com.z1.handler.ModulesHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        
        case lop:
        {
          new com.z1.handler.LOPHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        
        case goalStats:
        {
          if (!z1.commons.Utils.isMarketPlace)
          {
            new com.z1.handler.GoalStatsHandler().get().handle(ctx, subParts, req,
              resp);
          }
          return;
        }
        case bx:
        {
          new com.z1.handler.BXHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        // c3/data/labels/*
        case labels:
        {
          new com.z1.handler.LabelsHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        // c3/data/incidentalevent/*
        case incidentalevent:
        {
          new com.z1.handler.IncidentalEventHandler().get().handle(ctx, subParts, req,
              resp);
          return;
        }
        // c3/data/dnaevent/*
        case dnaevent:
        {
          new com.z1.handler.DNAEventHandler().get().handle(ctx, subParts, req, resp);
          return;        
        }
        // c3/data/sslCertUpload/*
        case sslCertUpload:
        {
          new com.z1.handler.SSLCertificateHandler().get().handle(ctx, subParts,
              req, resp);
          return;
        }
        // c3/data/sso/*
        case sso:
        {
          new com.z1.handler.SSOHandler().get().handle(ctx, subParts, req, resp);
          return;
        }
        // c3/data/cluster
        case cluster:
        {
          String serverName = req.getServerName();
          String domainName = ".zineone.com";
          String cluster = "zb"; //this is for test environment to work
          if(serverName.indexOf(domainName)!=-1)
          {
            cluster = serverName.substring(0, serverName.indexOf(domainName));
          }
          Map<String, Object> map = new java.util.HashMap<>();
          map.put("cluster", cluster);
          
          String payload = new JsonMarshaller().serializeMap(map);
          
          resp.getWriter().print(payload);
          return;
        }

        case version:
        {
          String platformVersion = ApiUtils.getPlatformVersion();
          resp.getWriter()
              .print("{\"platformVersion\":\"" + platformVersion + "\"}");
          return;
        }

        case successmetric:
        {
          new com.z1.handler.SuccessMetricHandler().get().handle(ctx, subParts, req, resp);
          return;
        }

        case monitor:
        {
          new DataMonitorHandler().get().handle(ctx,
              subParts, req, resp);
          return;
        }

        case monitorstatus:
        {
          new DataMonitorStatusHandler().get().handle(ctx,
              subParts, req, resp);
          return;
        }

        // c3/data/offers/*
        case offers:
        {
          new com.z1.handler.OfferHandler().get().handle(ctx, subParts, req, resp);
          return;
        }
        // c3/data/remotefiles/*
        case remotefiles:
        {
          new RemoteFilesHandler().get().handle(ctx, subParts, req, resp);
          return;
        }
        // c3/data/commandcenter
        case commandcenter:
        {
          new com.z1.handler.CommandCenterHandler().get().handle(ctx, subParts, req, resp);
          return;
        }
      }
    }
    catch (IllegalAccessError e)
    {
      resp.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
    }
    catch (IllegalArgumentException e) {
      resp.sendError(HttpServletResponse.SC_NOT_FOUND);
    }
    catch (Throwable e)
    {
      z1.commons.Utils.showStackTraceIfEnable(e, null);

      ULogger logger = ctx.getLogger(getClass());
      if (logger.canLog())
      {
        logger.log(e.getMessage());
      }

      resp.getWriter().println("{");
      ServletUtil.printFailure(resp.getWriter(), "Server side error.");
      resp.getWriter().println("}");
    }

  }

  // ..................................................
  @SuppressWarnings("incomplete-switch")
  public void doPost(HttpServletRequest req, HttpServletResponse resp)
      throws IOException
  {
    resp.setContentType("application/json");
    resp.setCharacterEncoding("UTF-8");

    UContext ctx = UContext.getInstance(req);

    try
    {
      //
      // We'll get the path info of the request. The path info must start with a
      // known artifact type.
      // The structure will be like /c3/data/<artifact type>/*
      // So we are expecting 5 parts after splitting, the first one being blank
      String path = req.getPathInfo();
      String[] pathParts = path.split("/");
      if (pathParts.length < 2)
      {
        return;
      }

      // Create an array to pass for the relevant commands
      String[] subParts = new String[pathParts.length - 2];
      int j = 0;
      for (int i = 2; i < pathParts.length; i++, j++)
      {
        subParts[j] = pathParts[i];
      }

      Command cmd = Command.valueOf(pathParts[1]);
      switch (cmd)
      {
        case qatestgroup:
        {
          new com.z1.handler.QaTestGroupHandler().post().handle(ctx, subParts, req,
              resp);
          return;          
        }
        case task:
        {
          new com.z1.handler.TaskHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        // c3/data/feed/...
        case feed:
        {
          new com.z1.handler.FeedHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        // c3/data/goals/...
        case goals:
        {
          new com.z1.handler.GoalHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        case segments:
        {
          new com.z1.handler.SegmentsHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        case session:
        {
          new com.z1.handler.SessionHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        case signals:
        {
          new com.z1.handler.SignalsHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        case triggers:
        {
          new com.z1.handler.TriggersHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        case outcomes:
        {
          new com.z1.handler.OutcomesHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        case actions:
        {
          new com.z1.handler.ActionsHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        case insights:
        {
          new com.z1.handler.InsightsHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        case datasink:
        {
          new com.z1.handler.DatasinkHandler().post().handle(ctx,
              subParts, req, resp);
          return;
        }
        case channels:
        {
          new com.z1.handler.ChannelsHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        case apikey:
        {
          new com.z1.handler.ApikeyHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        case kb:
        {
          new com.z1.handler.KBHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        case systemconfig:
        {
          new com.z1.handler.SystemConfigHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }
        case deepLinks:
        {
          new com.z1.handler.DeepLinkHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        case agent:
        {
          new com.z1.handler.AgentHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        //c3/data/incentives/..
        case incentives:{
          new com.z1.handler.IncentivesHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        // c3/data/passwordReset
        case passwordReset:
        {
          String payload = ServletUtil.getPayload(req);
          JsonMarshaller jm = new JsonMarshaller();
          Map<String, Object> m = jm.readAsMap(payload);
          String email = (String) m.get("email");
          String ns = Utils.generateNameSpaceFromEmail(email);
          AccessControl acl = new AccessControl(ctx);
          if (!acl.belongToNameSpace(ns)) {
            resp.sendError(HttpServletResponse.SC_FORBIDDEN);
            return;
          }
          String currentPwd = Encryption
              .decodeBase64((String) m.get("currentPassword"));
          if (currentPwd == null) currentPwd = "";
          String newPwd = Encryption
              .decodeBase64((String) m.get("newPassword"));

          User u = User.load(ctx, email);

          if (u != null)
          {
            if (((String) u.getValues().get(Fields.status.name()))
                .equals(Status.active.name()))
            {

              User.PasswordCheckingResult passwordMatchingStatus = u.validatePassword(currentPwd, false);
              if (passwordMatchingStatus == User.PasswordCheckingResult.FAIL)
              {
                passwordMatchingStatus = u.validatePassword(currentPwd, true);
              }

              if (passwordMatchingStatus == User.PasswordCheckingResult.SUCCESS
                  || passwordMatchingStatus == User.PasswordCheckingResult.SUCCESS_NEED_PASSWORD_CHANGE)
              {
                // Clear tmppwd since main pwd is set.
                u.getValues().put(Fields.tempPwd.name(), "");

                // Set new password.
                boolean successfullySetPassword = u.setPassword(newPwd);
                if (!successfullySetPassword)
                {
                  resp.getWriter().print(
                      "{\"result\":\"Unable to set password\"}");
                  return;
                }
                u.save();

                resp.getWriter().print(jm.serialize(u));
              }
              else
              {
                resp.getWriter()
                  .print("{\"result\":\"User authentication failed\"}");
                return;
              }
            }
            else
            {
              resp.getWriter().print(
                  "{\"result\":\"Account is not Active, Please activate the account before resetting your password.\"}");
            }
          }
          else
          {
            resp.getWriter().print("{\"result\":\"Invalid entries\"}");
          }
          return;
        }
        case css:
        {
          new com.z1.handler.CssHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        case payment:
        {
          new com.z1.handler.PaymentHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        case journey:
        {
          new com.z1.handler.JourneyHandler(Journey.Type.journey).post()
              .handle(ctx, subParts, req, resp);
          return;
        }
        case campaign:
        {
          new com.z1.handler.JourneyHandler(Journey.Type.campaign).post()
              .handle(ctx, subParts, req, resp);
          return;
        }
        case c1:
        {
          new com.z1.handler.JourneyHandler(Journey.Type.c1).post().handle(ctx,
              subParts, req, resp);
          return;
        }
        case campaignonce:
        {
          new com.z1.handler.JourneyHandler(Journey.Type.c1).post().handle(ctx,
              subParts, req, resp);
          return;
        }

        // c3/data/dashboard/publish
        case dashboard:
        {
          new com.z1.handler.DashboardHandler().post().handle(ctx,
              subParts, req, resp);
          return;
        }

        // c3/data/messagePolicy/...
        case messagepolicy:
        {

          // Create a channel by loading the payload from the request
          String payload = ServletUtil.getPayload(req);

          CustomConfig cc = CustomConfig.load(ctx, Type.messagePolicy, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.messagePolicy);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.messagePolicy.name());
          return;
        }
        // c3/data/dryrunconfig
        case dryrunconfig:
        {
          new com.z1.handler.DryRunConfigHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }
        // c3/data/pushconfig/...
        case pushconfig:
        {
          // call the uploadHandler to upload the ios certificate and save
          // credential
          new com.z1.handler.UploadHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        // c3/data/androidpushconfig
        case androidpushconfig:
        {
          // Store the GCMServerKey to mongo
          String payload = ServletUtil.getPayload(req);

          // if no key exists create a new one otherwise fetch the record from
          // mongo and update it.
          CustomConfig cc = CustomConfig.load(ctx, Type.androidPushConfig, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.androidPushConfig);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.androidPushConfig.name());
          return;
        }
        // c3/data/uwppushconfig
        case uwppushconfig:
        {
          // Store the Package SID and client secret to mongo
          String payload = ServletUtil.getPayload(req);

          // if no key exists create a new one otherwise fetch the record from
          // mongo and update it.
          CustomConfig cc = CustomConfig.load(ctx, Type.uwpPushConfig, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.uwpPushConfig);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.uwpPushConfig.name());
          return;
        }
        // c3/data/chromepushconfig
        case chromepushconfig:
        {
          return;
        }
        // c3/data/webpushconfig
        case webpushconfig:
        {
          new com.z1.handler.WebPushConfigHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }
        // c3/data/eventmapping
        case eventmapping:
        {
          new com.z1.handler.EventMappingHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }
        // c3/data/propertymapping
        case propertymapping:
        {
          new com.z1.handler.PropertyMappingHandler().post().handle(ctx, subParts,
                  req, resp);
          return;
        }
        // c3/data/traffictagconfig
        case traffictagconfig:
        {
          new com.z1.handler.TrafficTagConfigHandler().post().handle(ctx,
              subParts, req, resp);
          return;
        }
        // c3/data/notifications
        case notifications:
        {
          new com.z1.handler.NotificationConfigHandler().post().handle(ctx,
              subParts, req, resp);
          return;
        }
        // c3/data/profileschema/...
        case profileschema:
        {
          // Create a channel by loading the payload from the request
          String payload = ServletUtil.getPayload(req);

          CustomConfig cc = CustomConfig.load(ctx, Type.profileSchema, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.profileSchema);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.profileSchema.name());
          return;
        }
        // c3/data/eventpostprocessing/...
        case eventpostprocessing:
        {
          // Create a channel by loading the payload from the request
          String payload = ServletUtil.getPayload(req);

          CustomConfig cc = CustomConfig.load(ctx, Type.eventPostProcessing, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.eventPostProcessing);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.eventPostProcessing.name());
          return;
        }
        case explore:
        {
          new com.z1.handler.DataExplorerHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }
        case cube: 
        {
          new com.z1.handler.CubeHandler().post().handle(ctx, subParts, req, resp);
          return;
        }
        case cubequery:
        {
          new com.z1.handler.CubeQueryHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }

        // c3/data/livelinks/...
        case livelinks:
        {
          // Create a channel by loading the payload from the request
          String payload = ServletUtil.getPayload(req);

          CustomConfig cc = CustomConfig.load(ctx, Type.livelinks, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.livelinks);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.livelinks.name());
          return;
        }
        case contentcss:
        {
          // Get the OS from the request
          String os = null;
          if (subParts.length > 0)
          {
            os = subParts[0];
          }

          Type t = null;
          String type = req.getParameter("type");
          if ("ios".equalsIgnoreCase(os))
          {
            if ("tablet".equalsIgnoreCase(type))
            {
              t = Type.iosTabContentCss;
            }
            else
            {
              t = Type.iosPhoneContentCss;
            }
          }
          else if ("android".equalsIgnoreCase(os))
          {
            if ("tablet".equalsIgnoreCase(type))
            {
              t = Type.androidTabContentCss;
            }
            else
            {
              t = Type.androidPhoneContentCss;
            }
          }

          CustomConfig ccs = CustomConfig.load(ctx, t, true);
          if (ccs == null)
          {
            ccs = CustomConfig.create(ctx, t);
          }

          String payload = ServletUtil.getPayload(req);

          ccs.setPayload(payload);
          ccs.save();
          App.notifyClearCache(ctx.getNamespace(), t.name());
          return;
        }
        case contentcss2:
        {
          new com.z1.handler.ContentCssHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }

        case content:
        {
          boolean isWrapHtml = ((subParts.length > 0)
              && ("wraphtml".equalsIgnoreCase(subParts[0]))) ? true : false;
          if (isWrapHtml)
          {
            new com.z1.handler.ContentWrapHtmlHandler().post().handle(ctx,
                subParts, req, resp);
          }
          return;
        }
        case systemreset:
        {
          // c3/data/systemreset
          new com.z1.handler.SystemResetHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }
        case testpush:
        {
          new com.z1.handler.TestPushHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        case uninstall:
        {
          new com.z1.handler.UninstallHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }
        case prioritizeCampaign:
        {
          // c3/data/prioritizeCampaign
          String payload = ServletUtil.getPayload(req);

          CustomConfig cc = CustomConfig.load(ctx, Type.prioritizeCampaign, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.prioritizeCampaign);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.prioritizeCampaign.name());
          
          // Audit trail for TI Priority Page
          if (payload != null && !payload.isEmpty())
          {
            String ids = payload.replaceAll("[^a-zA-Z0-9,-]", "");
            List<String> campaignIds = Arrays.asList(ids.split(","));
            List<String> names = new ArrayList<>();
            for (String cid : campaignIds)
            {
              Journey journey = Journey.loadDef(ctx, cid, Journey.Type.campaign);
              if (journey != null) 
              {
                names.add(journey.getName());
              }
            }
            ArtifactAudit.newInstance(ctx, ItemTypes.prioritizeCampaign,
                campaignIds.toString(), names.toString(), Operations.prioritized).save();
          }
          return;
        }

        case images:
        {
          new com.z1.handler.ImageDataHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }

        case eventinfo:
        {
          new com.z1.handler.EventInfoHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }

        case customaction:
        {
          new com.z1.handler.CustomActionHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }

        // c3/data/trustedagentconfig
        case trustedagentconfig:
        {
          // Store the key to mongo
          String payload = ServletUtil.getPayload(req);

          // if no key exists create a new one otherwise fetch the record from
          // mongo and update it.
          CustomConfig cc = CustomConfig.load(ctx, Type.trustedAgentConfig, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.trustedAgentConfig);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.trustedAgentConfig.name());
          return;
        }
        case googleplaystoreconfig:
        {
          // Store the GCMServerKey to mongo
          String payload = ServletUtil.getPayload(req);
          payload = new AesUtil().decryptWithSessionSalt(ctx, payload);

          // if no key exists create a new one otherwise fetch the record from
          // mongo and update it.
          CustomConfig cc = CustomConfig.load(ctx, Type.googlePlayStoreConfig, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.googlePlayStoreConfig);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.googlePlayStoreConfig.name());
          return;
        }
        case itunesstoreconfig:
        {
          // Store the GCMServerKey to mongo
          String payload = ServletUtil.getPayload(req);
          payload = new AesUtil().decryptWithSessionSalt(ctx, payload);

          // if no key exists create a new one otherwise fetch the record from
          // mongo and update it.
          CustomConfig cc = CustomConfig.load(ctx, Type.iTunesStoreConfig, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.iTunesStoreConfig);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.iTunesStoreConfig.name());
          return;
        }
        case geofence:
        {
          new com.z1.handler.GeofenceHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        
        // c3/data/props?devicetype=<device-type>&os=<os>
        case props:
        {
          // Create props by loading the payload from the request
          String payload = ServletUtil.getPayload(req);

          // Get the deviceType and os from the QS param and convert it to
          // initCap
          String deviceType = req.getParameter("devicetype");
          String os = req.getParameter("os");
          deviceType = deviceType.substring(0, 1).toUpperCase()
              + deviceType.substring(1).toLowerCase();
          os = os.substring(0, 1).toUpperCase() + os.substring(1).toLowerCase();
          String type = "props" + os + deviceType;

          CustomConfig cc = CustomConfig.load(ctx, Type.valueOf(type), true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.valueOf(type));
          }
          cc.setPayload(payload);
          cc.save();

          // TTS config
          String key = os.toLowerCase() + "." + deviceType.toLowerCase()
              + ".tts.value";

          Map<String, Object> map = new HashMap<>();
          @SuppressWarnings("unchecked")
          Map<String, Object> ttsValue = (Map<String, Object>) SystemConfig
              .getValue(ctx, key);
          if (ttsValue != null && !ttsValue.isEmpty())
          {
            map.putAll(ttsValue);
          }

          Map<String, Object> out = new HashMap<>();
          Map<String, Object> res = new HashMap<>();
          TimeUtils tm = new TimeUtils("UTC");
          out.put("utcTime", tm.parseTime(tm.getTime()));
          String publisherName = (String) ctx.getUser().getValues()
              .get(User.FNAME);
          if (publisherName == null || publisherName.isEmpty())
          {
            publisherName = "Unknown";
          }

          String publisherEmail = ctx.getUser().getId();
          if (publisherEmail == null || publisherEmail.isEmpty())
          {
            publisherEmail = "Unknown";
          }

          out.put("publishedByName", publisherName);
          out.put("publishedByEmail", publisherEmail);
          map.put("props", out);
          res.put(key, map);
          SystemConfig.saveAll(ctx, new JsonMarshaller().serializeMap(res));

          App.notifyClearCache(ctx.getNamespace(), type);
          return;
        }
        
        // c3/data/sdkprops?devicetype=<device-type>&os=<os>
        case sdkprops:
        {
          // Create props by loading the payload from the request
          String payload = ServletUtil.getPayload(req);

          // Get the deviceType and os from the QS param and convert it to
          // initCap
          String deviceType = req.getParameter("devicetype");
          String os = req.getParameter("os");
          deviceType = deviceType.substring(0, 1).toUpperCase()
              + deviceType.substring(1).toLowerCase();
          os = os.substring(0, 1).toUpperCase() + os.substring(1).toLowerCase();
          String type = "sdkprops" + os + deviceType;

          CustomConfig cc = CustomConfig.load(ctx, Type.valueOf(type), true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.valueOf(type));
          }
          cc.setPayload(payload);
          cc.save();

          SystemConfig.updateTTS(ctx, os, deviceType, "sdkprops");
          
          App.notifyClearCache(ctx.getNamespace(), type);
          return;
        }
        
        // c3/data/passwordpolicyconfig 
        case passwordpolicyconfig:
        {
          // Store the GCMServerKey to mongo
          String payload = ServletUtil.getPayload(req);

          // if no key exists create a new one otherwise fetch the record from
          // mongo and update it.
          CustomConfig cc = CustomConfig.load(ctx, Type.passwordPolicyConfig, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.passwordPolicyConfig);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.passwordPolicyConfig.name());
          return;
        }
        case contextattribute:
        {
          new com.z1.handler.ContextAttributeHandler().post().handle(ctx,
              subParts, req, resp);
          return;
        }
        case eventprocessing:
        {
          new com.z1.handler.EventPostProcessingHandler().post().handle(ctx,
              subParts, req, resp);
          return;
        }
        case remotelinks:
        {
          new com.z1.handler.RemoteLinkHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }
        // c3/data/actionMapping/...
        case actionMapping:
        {
            new com.z1.handler.ActionMappingHandler().post().handle(ctx, subParts,
                    req, resp);
          // Create an actionMapping by loading the payload from the request
          /*String payload = ServletUtil.getPayload(req);
          String type = req.getParameter("type");

          CustomConfig.__addUnique_(Type.valueOf(type));
          CustomConfig cc = CustomConfig.load(ctx, Type.valueOf(type));
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.valueOf(type));
          }
          cc.setPayload(payload);
          // cc.setName(id);
          cc.save();*/

          return;
        }
        // c3/data/twilioconfig 
        case twilioconfig:
        {
          // Store the config to mongo
          String payload = ServletUtil.getPayload(req);
          payload = new AesUtil().decryptWithSessionSalt(ctx, payload);

          // if no key exists create a new one otherwise fetch the record from
          // mongo and update it.
          CustomConfig cc = CustomConfig.load(ctx, Type.twilioConfig, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.twilioConfig);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.twilioConfig.name());
          return;
        }
        // c3/data/sendgridconfig
        case sendgridconfig:
        {
          // Store the config to mongo
          String payload = ServletUtil.getPayload(req);
          payload = new AesUtil().decryptWithSessionSalt(ctx, payload);

          // if no key exists create a new one otherwise fetch the record from
          // mongo and update it.
          CustomConfig cc = CustomConfig.load(ctx, Type.sendGridConfig, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.sendGridConfig);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.sendGridConfig.name());
          return;
        }
        
        // c3/data/mailgunconfig
        case mailgunconfig:
        {
          // Store the config to mongo
          String payload = ServletUtil.getPayload(req);
          payload = new AesUtil().decryptWithSessionSalt(ctx, payload);

          // if no key exists create a new one otherwise fetch the record from
          // mongo and update it.
          CustomConfig cc = CustomConfig.load(ctx, Type.mailGunConfig, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.mailGunConfig);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.mailGunConfig.name());
          return;
        }
        
        // c3/data/sendgridcredentialscheck
        case sendgridcredentialscheck:
        {
          new com.z1.handler.SendGridHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        
        // c3/data/adobeconfig
        case adobeconfig:
        {
          // Store the config to mongo
          String payload = ServletUtil.getPayload(req);
          payload = new AesUtil().decryptWithSessionSalt(ctx, payload); 

          // if no key exists create a new one otherwise fetch the record from
          // mongo and update it.
          CustomConfig cc = CustomConfig.load(ctx, Type.adobeConfig);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.adobeConfig);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.adobeConfig.name());
          return;
        }
        
        // c3/data/darkskyconfig
        case darkskyconfig:
        {
          // Store the config to mongo
          String payload = ServletUtil.getPayload(req);
          payload = new AesUtil().decryptWithSessionSalt(ctx, payload);

          // if no key exists create a new one otherwise fetch the record from
          // mongo and update it.
          CustomConfig cc = CustomConfig.load(ctx, Type.darkSkyConfig, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.darkSkyConfig);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.darkSkyConfig.name());
          return;
        }
        // c3/data/bqconfig
        case bqconfig:
        {
          // Store the config to mongo
          String payload = ServletUtil.getPayload(req);
          payload = new AesUtil().decryptWithSessionSalt(ctx, payload);

          // if no key exists create a new one otherwise fetch the record from
          // mongo and update it.
          CustomConfig cc = CustomConfig.load(ctx, Type.bqConfig, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.bqConfig);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.bqConfig.name());
          return;
        }

        // c3/data/scoretranserconfig
        case scoretranserconfig:
        {
          // Store the config to mongo
          String payload = ServletUtil.getPayload(req);
          // payload = new AesUtil().decryptWithSessionSalt(ctx, payload);

          // if no key exists create a new one otherwise fetch the record from
          // mongo and update it.
          CustomConfig cc = CustomConfig.load(ctx, Type.scoreTransferConfig, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.scoreTransferConfig);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.scoreTransferConfig.name());
          return;
        }


        // c3/data/openweathermapconfig
        case openweathermapconfig:
        {
          // Store the config to mongo
          String payload = ServletUtil.getPayload(req);
          payload = new AesUtil().decryptWithSessionSalt(ctx, payload);

          // if no key exists create a new one otherwise fetch the record from
          // mongo and update it.
          CustomConfig cc = CustomConfig.load(ctx, Type.openWeatherMapConfig, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.openWeatherMapConfig);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.openWeatherMapConfig.name());
          return;
        }
        case query:
        {
          new com.z1.handler.QueryDataHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }
        case ml:
        {
          new com.z1.handler.MLHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }
        case streamQuery:
        {
          new com.z1.handler.StreamQueryHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }
        case entity:
        {
          new com.z1.handler.EntityHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        case profile:
        {
          new com.z1.handler.ProfileHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        // c3/data/androidMethodsMappingConfig
        case androidMethodsMappingConfig:
        {
          // Store the GCMServerKey to mongo
          String payload = ServletUtil.getPayload(req);

          // if no key exists create a new one otherwise fetch the record from
          // mongo and update it.
          CustomConfig cc = CustomConfig.load(ctx,
              Type.androidMethodsMappingConfig, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.androidMethodsMappingConfig);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.androidMethodsMappingConfig.name());
          return;
        }
        case sharedurl:
        {
          new com.z1.handler.SharedUrlHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }
        case testidconfig:
        {
          // c3/data/testidconfig
          String payload = ServletUtil.getPayload(req);

          CustomConfig cc = CustomConfig.load(ctx, Type.testIdConfig, true);
          if (cc == null)
          {
            cc = CustomConfig.create(ctx, Type.testIdConfig);
          }
          cc.setPayload(payload);
          cc.save();
          App.notifyClearCache(ctx.getNamespace(), Type.testIdConfig.name());
          return;
        }
        case importartifact:
        {
          new com.z1.handler.ArtifactPackageHandler().post().handle(ctx,
              subParts, req, resp);
          return;
        }
        // c3/data/modules/*
        case modules:
        {
          new com.z1.handler.ModulesHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        // c3/data/bx/*
        case bx:
        {
          new com.z1.handler.BXHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        case lop:
        {
          new com.z1.handler.LOPHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        // c3/data/v1tracer/*
        case v1tracer:
        {
          new com.z1.handler.V1TracerHandler().post().handle(ctx, subParts, req, resp);
          break;
        }
        case profileopt:
        {
          new com.z1.handler.ProfileOptHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        //  c3/data/labels/*
        case labels:
        {
          new com.z1.handler.LabelsHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        // c3/data/incidentalevent/*
        case incidentalevent:
        {
          new com.z1.handler.IncidentalEventHandler().post().handle(ctx, subParts, req,
              resp);
          return;
        }
        // c3/data/dnaevent/*
        case dnaevent:
        {
          new com.z1.handler.DNAEventHandler().post().handle(ctx, subParts, req, resp);
          return;
        }
        // c3/data/sslCertUpload/*
        case sslCertUpload:
        {
          new com.z1.handler.SSLCertificateHandler().post().handle(ctx,
              subParts, req, resp);
          return;
        }
        // c3/data/sso/*
        case sso:
        {
          new com.z1.handler.SSOHandler().post().handle(ctx, subParts, req, resp);
          return;
        }
        case datasheet:
        {
          new com.z1.handler.DatasheetHandler().post().handle(ctx, subParts,
              req, resp);
          return;
        }

        case successmetric:
        {
          new com.z1.handler.SuccessMetricHandler().post().handle(ctx, subParts, req, resp);
          return;
        }

        case monitor:
        {
          new DataMonitorHandler().post().handle(ctx, subParts, req, resp);
          return;
        }

        // c3/data/offers/*
        case offers:
        {
          new com.z1.handler.OfferHandler().post().handle(ctx, subParts, req, resp);
          return;
        }
        // c3/data/remotefiles/*
        case remotefiles:
        {
          new RemoteFilesHandler().post().handle(ctx, subParts, req, resp);
          return;
        }
        // c3/data/parseReportUrl
        case parseReportUrl:
        {
          String payload = ServletUtil.getPayload(req);
          JsonMarshaller jm = new JsonMarshaller();
          Map<String, Object> m = jm.readAsMap(payload);
          String encrypted = (String) m.get("source");
          String decryptedUrl = new AesUtil().decryptReportURL(encrypted, ctx);
          if (decryptedUrl == null)
          {
            throw new UException("Failed to decrypt the given url!");
          }
          Map<String, Object> res = new HashMap<>();
          res.put("result", decryptedUrl);
          resp.getWriter().print(new JsonMarshaller().serializeMap(res));
          return;
        }

        // c3/data/commandcenter
        case commandcenter:
        {
          new com.z1.handler.CommandCenterHandler().post().handle(ctx, subParts, req, resp);
          return;
        }
      }
    }
    catch (IllegalAccessError e)
    {
      resp.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
    }
    catch (IllegalArgumentException e) {
      resp.sendError(HttpServletResponse.SC_NOT_FOUND);
    }
    catch (Throwable e)
    {
      z1.commons.Utils.showStackTraceIfEnable(e, null);

      ULogger logger = ctx.getLogger(getClass());
      if (logger.canLog())
      {
        logger.log(e.getMessage());
      }

      resp.getWriter().println("{");
      ServletUtil.printFailure(resp.getWriter(), "Server side error.");
      resp.getWriter().println("}");
    }

  }

}
