define(["dojo/_base/declare","udc/udc","dojo/_base/lang","dojo/dom-attr","udc/core/util/CommonUtils"],function(f,e,g,h,k){return f(null,{constructor:function(){this._contentNode=null;this._regHandlers={}},registerEvent:function(b,a){this._regHandlers[b]=a},destroy:function(){if(this._curView){var b=this._curView._container_;b&&(b.destroy(),this._curView._container_=null);this._curView.destroy();this._curView=null}},resize:function(){if(this._curView){var b=this._curView._container_;b&&b.resize();
this._curView.resize()}},createWidget:function(b){},handleEvent:function(b){var a=this._curView;if(a&&a._forEvent_===b.getName()&&!this.allowDuplicateEvent)return!0;var d=this._regHandlers[b.getName()];if(!d)return!1;var c=null;if(d.isContainer&&(c=d,c.handleEvent(b)))return!0;if(a){if(!a.prepareExit(b))return!0;a._container_&&(a._container_.destroy(),a._container_=null);a.destroy();this._curView=null}if(!this._contentNode)throw e.exception("Content node is not created for a container before creating the view.");
a=k.createElement("div",this._contentNode);h.set(a,"style","width: 100%; height: 100%");this._curView={_forEvent_:b.getName()};d.createWidget(a,b).then(g.hitch(this,function(a){this._curView=a;if(!a)return e.log("View is not created for event "+b.getName()),!1;c&&(c._contentNode=a.content);a._forEvent_=b.getName();a._container_=c;a.startup()}));return!0},_end:0})});
