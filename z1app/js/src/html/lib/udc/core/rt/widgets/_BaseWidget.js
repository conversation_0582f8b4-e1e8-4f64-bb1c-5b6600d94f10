define("dojo/parser dojo/_base/declare dojo/on dojo/dom-style dojo/dom-construct dijit/_WidgetBase dijit/_TemplatedMixin dijit/_OnDijitClickMixin udc/core/util/CommonUtils".split(" "),function(m,e,f,b,n,g,h,k,l){return e([g,h,k],{templateString:"",constructor:function(){this._handlers=[]},showIt:function(){b.set(this.domNode,"display","block");this.resize()},hideIt:function(){b.set(this.domNode,"display","none")},resize:function(){this.inherited(arguments)},destroy:function(){this.inherited(arguments);
for(var a=0;a<this._handlers.length;a++)this._handlers[a].remove();this._handlers=[]},prepareExit:function(a){return!0},_handle:function(a,c,d){this._handlers.push(f(a,c,d))},_createElement:function(a,c,d,b){return l.createElement(a,c,d,b)},_end:0})});
