<!DOCTYPE html>
<html lang="en">
<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta http-equiv="Content-Type" content="text/html;charset=utf-8" >
	<style type="text/css">
		html,
		body {
			width: 100%;
			height: 100%;
			margin: 0;
			padding: 0;
			overflow: hidden;
		}
	</style>
<link rel="stylesheet" type="text/css" href="/common.css" />
<!-- <link rel="stylesheet" type="text/css" href="/c3.css" /> -->
<style type="text/css">
.c3_mncoC {
	border: 1px solid rgb(222, 222, 222);
}
.c3_vis0 {
	visibility: hidden;
}
</style>
</head>
<body>
	<div class="c3_mncoC c3_cols_nowrap c3_cols_spacing"
		 style="width:100%;height:100%">
		<div id="container" class="c3_flex_1"></div>
	</div>

<script src="/lib/monaco-editor-0.19.2/min/vs/loader.js"></script>
<script>
require.config({ paths: { 'vs': "../lib/monaco-editor-0.19.2/min/vs" }});

// global
let _idSfx;
let parentLocation = "*";
let editor;
let m;
let isEditorCreated = false;

// ........................................
function init (ctx = {}) {
	_idSfx = ctx._idSfx;
	if (ctx.parentLocation) parentLocation = ctx.parentLocation;
}

// ........................................
function createEditor (cfg = {}) {
	require(['vs/editor/editor.main'], function() {
		// console.log("creating editor");

		let eCfg = {
			// default language
			language: 'javascript'
		};
		Object.assign(eCfg, cfg);

		m = monaco;

		// theme for code hint section
		monaco.editor.defineTheme('codeHintTheme', {
			base: 'vs',
			inherit: true,
			rules: [{ background: 'EDF9FA' }],
			colors: {
				'editor.background': '#f7f7f7'
			}
		});

		monaco.editor.onDidCreateEditor((e) => {
			// editor is ready
			isEditorCreated = true;
			window.parent.postMessage({
				_idSfx: _idSfx,
				msg: "editorCreated"
			}, parentLocation);
		});

		editor = monaco.editor.create(document.getElementById('container'), eCfg);

		// key up
		editor.onDidChangeModelContent((e) => {
			//console.log("onDidChangeModelContent");
			//console.log(e);
			//console.log(editor.getValue());
		});

		// onChange
		editor.onDidBlurEditorWidget(() => {
			//console.log("onDidBlurEditorWidget " + editor.getValue());
			window.parent.postMessage({
				_idSfx: _idSfx,
				msg: "blurEditor"
			}, parentLocation);
		});
		//editor.onDidBlurEditor(() => {
		//	console.log("onDidBlurEditor " + editor.getValue());
		//});
		//editor.onDidBlurEditorText(() => {
		//	console.log("onDidBlurEditorText " + editor.getValue());
		//});

		// on selection
		editor.onDidChangeCursorSelection(() => {
			//console.log("onDidBlurEditorWidget " + editor.getValue());
			window.parent.postMessage({
				_idSfx: _idSfx,
				msg: "onSelection"
			}, parentLocation);
		});

		// resize
		window.onresize = function() {
			editor.layout();
		}
	});
}

// ........................................
function format () {
	if (!editor) return;
	let a = editor.getAction('editor.action.formatDocument');
	if (a)
	{
		a.run()
		.then(() => {
			// console.log('finished formatting');
			window.parent.postMessage({
				_idSfx: _idSfx,
				msg: "formattingComplete"
			}, parentLocation);
		});
	}
}

// ........................................
function insertTextAtCursor (txt) {
	editor.trigger('keyboard', 'type', {text: txt});
	editor.focus();
}

//........................................
function openSearch () {
	editor.trigger('keyboard', 'actions.find');
	//editor.trigger('', 'actions.find');
}

//........................................
function closeSearch () {
	editor.trigger('keyboard', 'closeFindWidget');
}

// ........................................
function setLanguage (language) {
	m.editor.setModelLanguage(editor.getModel(), language);
}

// ........................................
function setTheme (theme = "vs-dark") {
	// m.editor.setTheme("vs-dark");
	m.editor.setTheme(theme);
	//m.editor.updateOptions({theme: theme});
}

// ........................................
function focus () {
	if(editor) editor.focus();
}

// ........................................
function getSelection () {
	return editor.getModel().getValueInRange(editor.getSelection());
}

// ........................................
function getValue () {
	if (!editor) return;
	return editor.getValue();
}

// ........................................
function setValue (code) {
	if (!editor) return;
	editor.setValue(code);
}
</script>
</body>
</html>