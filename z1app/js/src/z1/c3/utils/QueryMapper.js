define([
  "udc/udc",
  "dojo/_base/lang",
  "z1/common/FFUtil",
  ], function(udc, lang, FFUtil)
{
  /**
   * Query Mapper
   * Converts old subsystem queries to new subsystem queries
   * @toBeDeprecated - #qns
   */
  var QueryMapper = {

    newSubsystem: null,

    idMapper: {
      "compareSameMetricPerDay": "compareSameMetricPerDayV2",
      "CVR_ns_avg": "CVR_nsV2",
      "RPS_ns_avg": "RPS_nsV2",
      "BP_exp_liftAllConvRate": "ConversionRateLiftAll"
    },

    metricMapper: {
      aov: "averageOrderValue",
      totalPurchases: "totalOrderCount",
      aovAdjusted: "averageOrderValueAdjusted",
      sessionViewedProdOrSKU: "totalSessionsViewedProdOrSKU",
      sessionSearches: "totalSessionSearched",
      revenueAdjusted: "totalRevenueAdjusted",
      discountPurchase: "totalOrderCountWithDiscount",
      avgDiscount: "averageDiscount",
      totalRevenueCG: "totalRevenue",
      totalItemsOrderedCG: "totalItemsOrdered",
      totalItemsRevenueCG: "totalItemsRevenue",
      averageItemsPerOrderCG: "averageItemsPerOrder",
      averageItemPriceCG: "averageItemPrice",
      aovCG: "averageOrderValue",
      sessionAddToCartCG: "sessionAddToCart",
      sessionCartValueCG: "sessionCartValue",
      sessionStartCheckoutCG: "sessionStartCheckout",
      sessionOfferAppliedCG: "sessionOfferApplied",
      sessionOfferRedeemedCG: "sessionOfferRedeemed",
      sessionViewedProdOrSKUCG: "totalSessionsViewedProdOrSKU",
      sessionSearchesCG: "totalSessionSearched",
      totalProductViewCG: "totalProductView",
      totalSKUViewCG: "totalSKUView",
      totalSearchedCG: "totalSearched",
      revenueAdjustedCG: "totalRevenueAdjusted",
      aovAdjustedCG: "averageOrderValueAdjusted",
      totalDiscountCG: "totalDiscount",
      discountPurchaseCG: "totalOrderCountWithDiscount",
      avgDiscountCG: "averageDiscount",
      aovLift: "averageOrderValueLift",
      aovAdjustedLift: "averageOrderValueAdjustedLift"
    },

    /**
     * Given that the feature flag for the new subsystem is set to true
     * This converts the targetUrl and payload to the format that adheres
     * to the new subsystem query framework
     *
     * Usage:
     *      QueryMapper.migrateQuery(targetUrl, payload, options);
     *      targetUrl OR payload [required]: At least one of these is required for the method
     *      options [optional]: Additional information for special handling of the targetUrl and payload
     *          {
     *              cmd: "querySankey" // a reference to a method
     *          }
     * Returns:
     *      { targetUrl: "...", payload: {payload: {...}}, options: ...}
     *
     * Note: the return object returns payload: {payload: {...} } because loader expects the format
     *       loader.POST(targetUrl, {payload: {...}})
     */
    migrateQuery: function (targetUrl = "", payload = {}, options = {})
    {
      let newTargetUrl = targetUrl;
      let newPayload = payload;

      // expecting a boolean
      //this.newSubsystem = FFUtil.getFeatureFlag(FFUtil.FLAG.NEW_SUBSYSTEM) != void(0) ? Boolean(FFUtil.getFeatureFlag(FFUtil.FLAG.NEW_SUBSYSTEM)) : false;
      this.newSubsystem = FFUtil.getBooleanFeatureFlag(FFUtil.FLAG.NEW_SUBSYSTEM);
      
      if (this.newSubsystem == true)
      {
        // deep copy
        let uri = new String(targetUrl);
        newPayload = JSON.parse(JSON.stringify(payload));

        newTargetUrl = this._processURL(uri, options);
        newPayload = this._processPayload(newPayload, options);

        // return the same object structure with the updated uri and payload
      }

      return {
        "targetUrl": newTargetUrl,
        "payload": newPayload,
        "options": options
      }
    },

    // changes to targetUrl are special handling since it is page specific
     /**
      * if options has a cmd then use the postProcessor method with that cmd
      * else call postProcessor with the default cmd
      */
     /**
      urlA + cmd --> urlB
      _urlProcessor() (switch case) || _pageSpecificProcessor()
        case 1: "querySankey || querySankeyFlow"
          --> just add the feature flag to the URL and return // &qns=true return
          --> we do this because Backend handles all the logic for this endpoint
          --> and they need to know if the flag is true or false, flag is false by default.
        case 2: c3/data/analyticquery/id?id=blahblah (named query)
          --> mapping old Id to new ID return URL
          --> new ID == oldID + "V2"
        case 3: c3/analytics/query?tabular=true&join... << in this case only the PAYLOAD needs to change
          --> no change to URL return
        case 4: Biz queries
          --> special handling
              c3/data/analyticquery/id?namespace=ajlocal_com&id=RPS_ns_avg cmd= identifies the biz query
        _payloadProcessor() (default) || _pageSpecificProcessor() (custom)
       */
    _processURL: function (uri, options)
    {
      const id = options?.id || "";
      // NOTE:
      // if the uri starts with c3/analytics/query?<tabular=boolean&joinOnGroupBy=boolean>
      // then it does not need to be processed
      switch(id)
      {
        case "compareSameMetricPerDay": {
          // take the URL, change the ID, remove anything we don't want
          // change compareSameMetricPerDay --> compareSameMetricPerDayV2
          // change URL remove ss=... and other things that need to be removed
          uri = this._mapToNewID(uri, id);
          uri = this._removeParamFromUri(uri, "ss=");
          uri = this._insertFeatureFlagToUri(uri);

          return uri;
        }
        case "BP_exp_liftAllConvRate": {
          uri = uri.replace("BP_exp_liftAll", "ConversionRateLiftAll");
          uri = this._replaceUriQueryPath(uri);
          uri = this._insertFeatureFlagToUri(uri);
          
          return uri;
        }
        case "CVR_RPS_AOV_exp_tg":
        case "BP_exp_liftAll": {
          uri = uri.replace(id, `${id}V2`);
          uri = this._replaceUriQueryPath(uri);
          uri = this._insertFeatureFlagToUri(uri);
          
          return uri;
        }
        default: {
          // handle querySankey and querySankeyFlow
          if (uri.toLowerCase().includes("querysankey"))
          {
            // append the feature flag and return it
            let prefix = "?";
            if (uri.trim().indexOf("?") != -1)
            {
              prefix = "&";
            }
            uri += `${prefix}${FFUtil.FLAG.NEW_SUBSYSTEM}=${this.newSubsystem}`;
            return uri;
          }

          // handle all general mapping
          uri = this._mapToNewID(uri, id);
          uri = this._removeParamFromUri(uri, "ss=");
          uri = this._insertFeatureFlagToUri(uri);

          return uri;
        }
      }
    },

    /**
     * Accepts one payload object and removes subsystem and aggregateAs
     * Can be cleaned up if this is easier to do inline
     */
    _cleanUpPlObj: function (plObj)
    {
      delete plObj.subsystem;
      delete plObj.aggregateAs;
    },

    /**
     * Accepts a list of objects which it then compresses
     * Any objects that share fromDate, toDate, timeUnit, groupBy and filter will be compressed into a single object
     *
     * Sample Input: [{"metric":"discountPurchaseCG","fromDate":"20230607","toDate":"20230829","timeUnit":"day","groupBy":["actionName"], datasetName: dpcg,
                        "filter":[{"attr":"journeyId","op":"=","val":"307344fa-e890-4b78-b9c4-505270d0601d"},{"attr":"actionName","op":"=","val":"z1Control"}]},
                      {"metric":"avgDiscount","fromDate":"20230607","toDate":"20230829","timeUnit":"day","groupBy":["actionName"], datasetName: ad,
                        "filter":[{"attr":"journeyId","op":"=","val":"307344fa-e890-4b78-b9c4-505270d0601d"},{"attr":"actionName","op":"!=","val":"z1TG"},{"attr":"actionName","op":"!=","val":"z1Control"}]},
                      {"metric":"avgDiscountCG","fromDate":"20230607","toDate":"20230829","timeUnit":"day","groupBy":["actionName"], datasetName: adcg,
                        "filter":[{"attr":"journeyId","op":"=","val":"307344fa-e890-4b78-b9c4-505270d0601d"},{"attr":"actionName","op":"=","val":"z1Control"}]}]
     * Sample Output: [{"metric":[{"source":"discountPurchaseCG","name":"discountPurchaseCG"},{"source":"avgDiscountCG","name":"avgDiscountCG"}],"datasetName":"ds0",
                            "fromDate":"20230607","toDate":"20230829","timeUnit":"day","groupBy":["actionName"],
                            "filter":[{"attr":"journeyId","op":"=","val":"307344fa-e890-4b78-b9c4-505270d0601d"},{"attr":"actionName","op":"=","val":"z1Control"}]},
                       {"metric":[{"source":"avgDiscount","name":"avgDiscount"}],"datasetName":"ds1",
                            "fromDate":"20230607","toDate":"20230829","timeUnit":"day","groupBy":["actionName"],
                            "filter":[{"attr":"journeyId","op":"=","val":"307344fa-e890-4b78-b9c4-505270d0601d"},{"attr":"actionName","op":"!=","val":"z1TG"},{"attr":"actionName","op":"!=","val":"z1Control"}]}]

      * options: { object }
      *   - keepDsName: keep datasetname as is. DO NOT REMOVE IT
      *   - dsUseAsMetricName: assign datasetName value to metric.name
      *   - metric {string}: a metric name to be set to the source property if you want it to be
      *     different from name property. if this option is not given, than the
      *     "source" property will be same as "name" 
      *     Ex. options.metric = "conversionRateNS" ===>  { source: ${metric}, name: default }
      *     Note: if more than one metrics, use options.metrics below
      *   - metrics {array of strings}: more than one metrics to be set.
      *     metric names to be to array of 
      *     Ex. options.metrics = [ "conversionRateNS", "revenuePerSessionNS", ... ] ===>
      *     [ { source: ${metric[index], name: default }, {}... ]}
      *     see querySubsystem() in SegmentsData.js for example
     */
    _genPayloadProcessor: function (payload, options)
    {
      if (!payload.entries) return;

      let collection = new Map();
      for (const [index, element] of payload.entries())
      {
        this._cleanUpPlObj(element);

        let key = {};
        if (element?.fromDate) key["fromDate"] = element.fromDate;
        if (element?.toDate) key["toDate"] = element.toDate;
        if (element?.timeUnit) key["timeUnit"] = element.timeUnit;
        // cumulative no longer exists, the queries themselves would express this with the group by...setting default to day
        if (key["timeUnit"] == "cumulative") key["timeUnit"] = "day";
        if (element?.groupBy && element.groupBy.length > 0) key["groupBy"] = element.groupBy;
        if (element?.filter && element.filter.length > 0 ) key["filter"] = element.filter;
        if (element?.datasetName && options?.["keepDsName"])
        {
          key["datasetName"] = element.datasetName;
        }

        key = JSON.stringify(key);

        let metric = {
          "source": options?.metric || options?.metrics?.[index] || element.metric,
          "name": element.metric
        };
        if (collection.has(key))
        {
          let metrics = collection.get(key);
          metrics.push(metric);
          collection.set(key, metrics);
        }
        else
        {
          collection.set(key,[metric]);
        }
      }

      // recreate the payload from the map
      let pl = [];
      let count = 0;
      collection.forEach((value, key, map) => {
        key = JSON.parse(key);
        let plItem = {
          metric: value,
          datasetName: `ds${count++}`,
          ...key
        }

        // convert attr name "jType" to "journeyType"
        key?.filter && key.filter.forEach(f => {
          if (f.attr === "jtype") f.attr = "journeyType"
        });
        
        pl.push(plItem);
      });

      return pl
    },

    _processPayload: function (payload, options)
    {
      const id = options?.id || "";
      switch(id)
      {
        case "incomingevents": {
          // page specific handling here
          // Loop through payload and remove subsystem and aggregateAs
          return payload;
        }
        case "aovNS": {
          payload[0].metric = "aovNS";
          payload[0].timeUnit = "day";
          delete payload[0].groupBy;
          delete payload[0].subsystem;
          delete payload[0].aggregateAs;
          options.metrics = [ "averageOrderValue" ];

          return this._genPayloadProcessor(payload, options);
        }
        case "totalEventKeepDatasetName": {
          return this._genPayloadProcessor(payload, { keepDsName: true });
        }
        case "_getBizTGTtlMetrics":
        case "_getActionBasedMetrics": {
          options.metrics = [];
          payload.forEach(p => {
            let mMapper = this.metricMapper[p.metric]
            if (mMapper)
              options.metrics.push(mMapper);
            else
              options.metrics.push(p.metric);
          });

          return this._genPayloadProcessor(payload, options);
        }
        default: {
          return this._genPayloadProcessor(payload, options);
        }
      }
    },

    _removeParamFromUri: function(uri, paramName)
    {
      let newUri;

      let startNdx = uri.indexOf(paramName);
      if (startNdx >= 0)
      {
        let endNdx = uri.substring(startNdx).indexOf("&");
        newUri = uri.substring(0,startNdx).concat(uri.substring(endNdx));
      }

      return newUri || uri;
    },

    _replaceUriQueryPath: function(uri)
    {
      const oldPath = "/data/analyticquery/id";
      const newPath = "/analytics/query";

      return uri.replace(oldPath, newPath);
    },

    /**
     * replace oldId with oldId + "V2"
     * @param {*} uri 
     * @param {*} oldId 
     */
    _mapToNewID: function (uri, oldId = "")
    {
      const replaceUriWithNewId = () => {
        if (uri.indexOf("id=") < 0) return uri;

        if (this.idMapper?.[oldId])
          return uri.replace(`${oldId}`, `${this.idMapper[oldId]}`);
        
        return uri.replace(`${oldId}`, `${oldId}V2`);
      }

      ////////////////////////////////////////////////
      uri = this._replaceUriQueryPath(uri);
      
      return (oldId && oldId !== "" && replaceUriWithNewId()) 
      || uri;
    },

    /**
     * set datasheet chart custom params
     * 
     * @param {Object} customParams 
     * @param {Object} options - additional uriId key/value pairs
     *    if feature flag is true, insert "V2" to uriId.value
     */
    _setDataSheetCustomParams: function(customParams, options)
    {
      const featureFlag = FFUtil.getBooleanFeatureFlag(FFUtil.FLAG.NEW_SUBSYSTEM);

      const v2 = featureFlag ? "V2" : "";
      if (options?.uriIds && options.uriIds.length > 0)
      {
        options.uriIds.forEach(uriId => {
          customParams[uriId.key] = uriId.value + v2;
        });
      }

      return (featureFlag) ?
      {
        ...customParams,
        metric: customParams?.metric?.mV2 || customParams?.metric || undefined,
        metricCG: customParams?.metricCG?.mV2 || customParams?.metricCG || undefined,
        uriId: options?.id ? options.id + "V2" : undefined,
        uriPath: options?.path ? "analytics/query" : undefined,
        uriSs: options?.ss ? "" : undefined,
        uriFFlag: "&qns=true"
      } :
      {
        ...customParams,
        metric: customParams?.metric?.m || customParams?.metric || undefined,
        metricCG: customParams?.metricCG?.m || customParams?.metricCT || undefined,
        uriId: options?.id || undefined,
        uriPath: options?.path || undefined,
        uriSs: options?.ss || undefined,
        uriFFlag: ""
      }
    },

    _insertFeatureFlagToUri: function(uri)
    {
      const insertFlag = (notation) => {
        return `${uri}${notation}${FFUtil.FLAG.NEW_SUBSYSTEM}=true`;
      }

      ///////////////////////////////////////////
      let notation = uri?.indexOf("?") > 0 ? "&" : "?";
      return insertFlag(notation);
    },

    /**
     * Get specify data values from respData.
     * 
     * example: respData = [{
     *    "datasetName": "ds0"
          "values": [
              {
                  "journeyId": "1c71324d-86f5-4441-8e32-78dc486d026e",
                  "conversionRateLiftAll": 0
              }
          ]},
          {
            "datasetName": "ds1"
            "values": [
              ...
            ]
          },
          ...
      ]

     * @param {Object} respData 
     * @param {string} ds (dataset name) - get data values for this ds from the respData.
     * @param {*} id - get data value for very unique to the id specified to this function.
     * 
     * @returns {Object} return data values
     * 
     * Note: if id is provided ds is optional or set it to ""
     */
    _getResponseValues: function(respData, ds, id="")
    {
      
      if (!FFUtil.getBooleanFeatureFlag(FFUtil.FLAG.NEW_SUBSYSTEM))
        return respData;

      switch(id)
      {
        case "CVR_ns_avg": {
          let value = respData?.[0]?.values?.[0]?.conversionRateNS || "";
          return { CVR_ns_avg: value }
        }
        case "RPS_ns_avg": {
          let value = respData?.[0]?.values?.[0]?.revenuePerSessionNS || "";
          return { RPS_ns_avg: value }
        }
        case "aovNS": {
          let value = respData[0].averageOrderValueNS;
          return [ { aovNS: value }]
        }
        case "eppData": {
          let rData = respData?.map && respData.map(r => {
            if (r?.metadata) {
              return {
                ...r, ...r.metadata
              }
            }
            return r;
          })

          return rData;
        }
        case "promotional_segment":
        case "promotional_summary":
          if (!Array.isArray(respData) || respData.length === 0) return respData;

          if (!Array.isArray(respData[0].values)) return [];

          const all = respData[0].values.find(value => value.eppLabel === 'z1EppLabelAll');

          const na = "N/A";
          respData[0].values.forEach(value => {
            if (value.promotionalYieldNS === undefined)
              value.promotionalYieldNS = na;
            if (value.promotionalAvgDiscountRateNS === undefined)
              value.promotionalAvgDiscountRateNS = na;
            if (value.promotionalCVRNS === undefined)
              value.promotionalCVRNS = na;
            if (value.promotionalTotalDiscountNS === undefined)
              value.promotionalTotalDiscountSecondary = 0;
            if (value.promotionalTotalRevenueNS === undefined)
              value.promotionalTotalRevenueSecondary = 0;
            if (value.promotionalTotalSessionsNS === undefined)
              value.promotionalTotalSessionsSecondary = 0;

            if (all === undefined) return;

            value.promotionalTotalDiscountSecondary = value.promotionalTotalDiscountNS * 100 / all.promotionalTotalDiscountNS;
            value.promotionalTotalRevenueSecondary = value.promotionalTotalRevenueNS * 100 / all.promotionalTotalRevenueNS;
            value.promotionalTotalSessionsSecondary = value.promotionalTotalSessionsNS * 100 / all.promotionalTotalSessionsNS;
          })

          return respData[0].values;
        case "promotional_time_series":
          if (!Array.isArray(respData) || respData.length === 0) return respData;

          if (!Array.isArray(respData[0].values)) return [];

          return respData[0].values;
        default:
          let values = respData?.find && respData.find(data => 
            data?.datasetName === ds
          )
    
          return values?.values || []
      }
    },

    _end: 0
  };

  return QueryMapper;
});

