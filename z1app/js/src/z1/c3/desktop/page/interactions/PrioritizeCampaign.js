/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define(["dojo/_base/declare", "dojo/_base/lang", "dojo/on", "dojo/aspect", "udc/udc",
  "z1/c3/desktop/start", "udc/core/rt/widgets/_BaseWidget",
  "dojo/text!z1/c3/desktop/page/templates/interactions/PrioritizeCampaign.html",
  "dojo/i18n!z1/c3/desktop/page/nls/PrioritizeCampaignRes",
  "z1/c3/desktop/page/filtercontrol/PrioritizeCpgnFilterCtrl",
  "z1/c3/desktop/page/util/C3Table2",
  "z1/c3/desktop/data/labels/LabelsData",
  "z1/c3/desktop/data/modules/ModulesData",
  "z1/c3/desktop/data/campaigns/CpgnData",
  "z1/c3/desktop/page/auditTrail/AuditTrailDef",
  "z1/common/C3Util",
  "dojo/dom-class",
  "udc/core/commons/loader",
  "dojo/dnd/Source",
  "dojo/topic",
  "dojo/dom-construct",
  "dojo/Deferred",
  "dojo/promise/all",
  "z1/c3/utils/Permissions"
],
function(declare, lang, on, aspect, udc, z1, BaseWidget, templateStr, Res,
  PrioritizeCpgnFilterCtrl, C3Table2, LabelsData, ModulesData,
  CpgnData, AuditTrailDef, C3Util,
  domClass, loader, Source, topic, domConstruct,
  Deferred, all, UserACL
)
{

  /**
   * FAQ list page.
   */
  return declare(BaseWidget, {
    templateString: templateStr,
    Res: Res,
    _list: null,
    _dndSrc: null,
    _pList: null,


    /**
     * Creates an instance of this page.
     * 
     * @param {Object}
     * @param {Object}
     *          parentNode
     */
    constructor: function(params, parentNode)
    {
      this.inherited(arguments);
      this._parent = parentNode;
      this._DnDEvent = false;
      
      this._title = this.isTypeExperienceChannel(params.type) ? Res.expTitle : Res.pTitle;
      this._desc = this.isTypeExperienceChannel(params.type) ?  Res.expDesc : Res.desc;
      // fetch requests deferred references
      params._dfrList= [];
    },

    // ////////////////////////////////////////////////////////////////////////////
    // Implementations

    postCreate: function()
    {
      this.inherited(arguments);
    },

    /**
     * Set the click handlers for all the tabs on insights Fire the event to select "Channel
     * Settings" by default and display the page.
     */
    startup: function()
    {
      this.inherited(arguments);
      this._subscribe();
      this._createFilter();
      this._init();
      // Audit Trail prioritization for campaign
      AuditTrailDef.showAuditTrailBtn({
        _parent: this,
        _usedIn: "prioritizeCampaign",
        _type: "type",
        showArtifactName: (aCtx) => {
          if(aCtx._cData && aCtx._cData.artifactName)
          {
            // show priority list delta
            var priorityExpList = aCtx._cData.artifactName.replace("[", "");
            priorityExpList = priorityExpList.replace("]", "");
            
            var dv = document.createElement("div");
            dv.classList.add(...["c3_priAuTrl","c3_cols_nowrap"]);
            var dvTxt = document.createElement("div");
            dvTxt.classList.add(...["c3_priAuTrlC","c3_clamp","c3_3l"]);
            dvTxt.textContent = priorityExpList;
            this.own(
              on(dvTxt, "click", () => {
                if (dvTxt.classList.contains("c3_clamp"))
                {
                  dvTxt.classList.remove("c3_clamp");
                }
                else
                {
                  dvTxt.classList.add("c3_clamp");
                }
              })
            );
            dv.append(dvTxt);
            aCtx._auditTCont.append(dv);
          }
        }
      });
      this._handleDnDCancel = topic.subscribe("/dnd/cancel", lang.hitch(this, function(source, nodes,
        copy)
      {
        this._onDnDCancel();
      }));
      this.own(this._handleDnDCancel);

      this._setEvents();
    },
    
    _init: function ()
    {
      this.initData().then(lang.hitch(this, function (){
        // got prioritized list
        // get all TI list
        this._getAllData();
      }));
    },

    //................................................
    initData: function ()
    {
      var deferr = new Deferred();

      var pCtx = {
        //cacheOk: true
      };
      pCtx.abortCtx = {abortIt: false};
      udc.showBusy(this._cont);
      var df = all([
        this._fetchPriorityList()
      ]);
      df.then(lang.hitch(this, function (data){
        udc.hideBusy();
        deferr.resolve(data);
      }), lang.hitch(this, function (err){
        udc.hideBusy();
        udc.log(err);
        if (err === "cancelled")
        {
          //deferr.resolve(true);
        }
        else
        {
          C3Util.handleServerError(err);
          //deferr.reject(err);
        }
        deferr.reject(err);
      }));
      this._dfrList.push({k: "_allPriorityList", deferr: df, rqCtx: pCtx});
      //C3Util.reqMgr.add({k: "_allPriorityList", deferr: df, rqCtx: pCtx}, this._dfrList);

      return deferr.promise;
    },

    //......................................
    _fetchPriorityList: function (ctx = {})
    {
      var deferr = new Deferred();

      var pCtx = {
        //cacheOk: true
      };
      pCtx.abortCtx = {abortIt: false};
      var df = CpgnData.api.fetchPrioritizedTIs(pCtx);
      df.then(lang.hitch(this, function (data){
        if (Array.isArray(data))
          this.setPriorityData(data);
        deferr.resolve(data);
      }), lang.hitch(this, function (err){
        udc.log(err);
        if (err === "cancelled")
        {
          //deferr.resolve(true);
        }
        else
        {
          C3Util.handleServerError(err);
          //if (err && (err.message || err.reason)) udc.info(err.message || err.reason);
          //deferr.reject(err);
        }
        deferr.reject(err);
      }));
      this._dfrList.push({k: "_fetchPriorityList", deferr: df, rqCtx: pCtx});
      //C3Util.reqMgr.add({k: "_fetchPriorityList", deferr: df, rqCtx: pCtx}, this._dfrList);

      return deferr.promise;
    },

    /**
     * Fetch one time campaign, scheduled campaign and
     * journeys existing in the system and save it
     * to one single array.
     */
    _getAllData: function(ctx = {})
    {
      var deferr = new Deferred();

      let payload = {};
      if (this._filter)
      {
        if (this._filter.page && this._filter.page.length)
        {
          payload.page = this._filter.page;
          if (this._filter.position && this._filter.position.length)
          {
            payload.position = this._filter.position.split(",");
          }
        }
      }
      if (this.filter)
      {
        payload = lang.mixin(payload, this.filter);
      }
      var pCtx = {};
      pCtx.abortCtx = {abortIt: false};

      udc.showBusy(this._cont);
      // if (this.isTypeExperienceChannel(this.type))
      var df = loader.POST("/c3/data/modules/prioritizeCampaign", { payload: payload });
      df.then(lang.hitch(this, (data) => {
        udc.hideBusy();
        //udc.log("/modules/prioritizeCampaign ", data);
        if (Array.isArray(data))
        {
          // remove id prefix campaign+
          data.forEach((d, i) => {
            if (d.id.startsWith("campaign+")) data[i].id = d.id.replace("campaign+", "");
          });
        }
        if (this._cont) this._parseGetAllData(data);
        deferr.resolve(data);
      }), lang.hitch(this, function(err)
      {
        udc.hideBusy();
        udc.log(err);
        if (err === "cancelled")
        {
          deferr.resolve(true);
        }
        else
        {
          C3Util.handleServerError(err);
          //if (err && (err.message || err.reason)) udc.info(err.message || err.reason);
          deferr.reject(err);
        }
      }));
      this._dfrList.push({k: "prioritizeCampaign", deferr: df, rqCtx: pCtx});
      //C3Util.reqMgr.add({k: "prioritizeCampaign", deferr: df, rqCtx: pCtx}, this._dfrList);

      return deferr.promise;
    },

    //........................................
    isTypeExperienceChannel: function(type)
    {
      return type && type === 'Experience Channel';
    },

    //........................................
    _parseGetAllData: function(data)
    {
      if (data)
      {
        if(data.status && data.status === "fail")
        {
          // empty list
        }
        else if (Array.isArray(data))
        {
          let pType = this.isTypeExperienceChannel(this.type) ? 'trigExp' : 'scheduled';
          for(var i=0 ; i<data.length ;i++)
          {
            data[i].pType = pType;
          }
          var cList = data.filter((cpgn) => {
            // show only published interactions
            // in priority list
            return (cpgn.state === "published");
          });
          this.setData(cList);
        }
        this._createTable();
      }
    },

    /**
    * The priority list to be shown in the table
    *
    */
    _getOrderedData: function()
    {
      //if there is no priority set and no campaign/journey data then return an empty list
      if(this.getPriorityData() && this.getPriorityData().length == 0  &&
        this.getData().length == 0 )
      {
        return new Array();
      }
      //if there is no priority data but campaign/journey exists then show that list
      else if(this.getPriorityData() && this.getPriorityData().length == 0 &&
        this.getData().length != 0)
      {
        return this.getData();
      }
      // if there is priority data but all campaigns/journeys have been deleted since then,
      // so dont show anything and re-save the now empty priority list to backend
      else if(this.getPriorityData() && this.getPriorityData().length != 0 &&
        this.getData().length == 0)
      {
        this.setPriorityData(new Array());
        this._savePriorityList();
        return new Array();
      }
      //if both the data exist then first place the priority list data followed by other data
      else if(this.getPriorityData() && this.getPriorityData().length != 0 &&
        this.getData().length != 0)
      {
        var tmpArray = new Array();
        var modifyPList = false;
        var len =  this.getPriorityData().length;
        for (var i = 0; i < len ; i++)
        {
          var nodeId = this.getPriorityData()[i];
          if (nodeId)
          {
            // if data exists no longer but pList exists that means the campaign/journey is deleted. remove
            // the item from plist
            if(this.getData().length == 0 )
            {
              modifyPList = true;
              this.getPriorityData().splice(i,1);
              break;
            }
            else
            {
              // find the plist item in general data and start pushing to tmpArr
              var len1 = this.getData().length;
              for (var j = 0; j < len1; j++)
              {
                var item = this.getData()[j];
                if (item.id === nodeId )
                {
                  // Mark item as having in priority list. Marked with value
                  // greater than 0. UI side attribute only.
                  // Item with no-priority will have ._priority null/undefined
                  item._priority = i + 1;
                  //tmpArray[i] = item;
                  tmpArray.push(item);
                  this.getData().splice(j,1);
                  break;
                }
                
              }
            }
            
          }
        }
        var newArr = (this.getData().length > 0) ? tmpArray.concat(this.getData()) : tmpArray;
        this.setData(newArr);
        if(modifyPList) 
        {
          this.setPriorityData(new Array());
          this._savePriorityList();
        }
        return newArr;
      }
    },

    //.....................................
    _savePriorityList: function()
    {
      var lst = this.getPriorityData();
      var targetUrl = "/c3/data/prioritizeCampaign";
      udc.showBusy(this._cont);
      loader.POST(targetUrl, {
        payload: lst
      }).then(lang.hitch(this, function(data)
      {
        // go back to the content dashboard page
        udc.hideBusy();
        udc.alert(Res.onSaveMessagePrioritySavedForAll, "success");
        // reload list

        if (!this._saveCount) this._saveCount = 1;
        if (this._saveCount < 5)
        {
          this._saveCount++;
          this._createTable();
          // Stay on the page to keep UI snappy.
          // Re-render page after 5 rows have been moved
          // to keep data in sync with BE
          return;
        }
        var pgCtx = {};
        if (this._filter && Object.keys(this._filter).length)
        {
          pgCtx._filter = JSON.parse(JSON.stringify(this._filter));
        }
        if (this.isTypeExperienceChannel(this.type))
        {
          // if (typeof filter === "object") pgCtx.filter = JSON.parse(JSON.stringify(this.filter));
          pgCtx.type = 'Experience Channel';
          z1.fireEvent(z1.Events.showModInstPriority, pgCtx);
          return;
        }
        z1.fireEvent(z1.Events.showPriority, pgCtx);
      }), lang.hitch(this, function(error)
      {
        C3Util.handleServerError(error);
        udc.hideBusy();
      }));
    },
    
    _showIcon: function(type)
    {
      switch (type.toLowerCase())
      {
        case "once":
          return "ti-announcement";
        break;
        case "scheduled":
        case "trigger":
          return "z1i-trigger";
        break;
        case "journey":
          return "ti-direction-alt";
        break;
        case "trigexp":
          return "z1i-experiences";
        break;
        default:
          return "";
        break;
      }
    },

    _showTooltip: function(type)
    {
      switch (type.toLowerCase())
      {
        case "once":
          return Res.once;
        break;
        case "scheduled":
          return Res.scheduled;
        break;
        case "journey":
          return Res.journey;
        break;
        case "trigexp":
          return Res.trigExp;
        break;
        default:
          return "";
        break;
      }
    },

    //...........................................
    // Table
    //...........................................

    _createTable: function (ctx = {})
    {
      this._mData = this._getOrderedData();
      let isFilterSelected = this.isFilterSelected();

      this.typeMap = {
        "_grab": {
          "type": "grab",
          "name_of_col": "Grab",
          "cls": "c3_txt_c",
          "clsD": "c3_flex_center_1,dojoDndHandle,col_move,c3_move_handle",
          "align": "center",
          "emptyHead": true
        },
        "_priority": {
          "type": "text_s",
          "name_of_col": Res.hPriority || "Priority",
          "cls": "c3_txt_c",
          "clsD": "c3_flex_center_1",
          "align": "center",
          "isSortable": false
        },
        "name": {
          "type": "text_xl",
          "name_of_col": Res.hName || "Experience",
          "clsD": "c3_lvTtl,c3_t4",
          "align": "left",
          "isSortable": false
        },
        "page": {
          "type": "text_l",
          "name_of_col": Res.hPage || "Pages",
          "clsD": "c3_lvPrioPage",
          "align": "left",
          "isSortable": false
        },
        "position": {
          "type": "text_m",
          "name_of_col": Res.hPosition || "Positions",
          "clsD": "c3_lvPrioPos",
          "align": "left",
          "isSortable": false
        },
        "description": {
          //"type": "text_desc",
          // 'text_fx1m' is used with 'x':1
          "type": "text_fx1m",
          "x": 1,
          "name_of_col": Res.hDescription || "Description",
          "cls": "",
          "clsD": "c3_clamp,c3_2l",
          "isSortable": false
        },
        "__c3btns": {
          "type": "btns_m",
          "name_of_col": "__c3btns",
          "cls": "c3_align_end",
          "clsD": "c3_flex_center_1",
          "align": "end",
          "emptyHead": true
        }
      };

      if (UserACL.checkUsrType("analyst"))
      {
        delete this.typeMap["_grab"];
        this.showMsg.classList.add("c3_hide");
      }

      if (this.priTable && typeof this.priTable.destroy === "function")
        this.priTable.destroy();

      var glryCtx = {
        _parent: this,
        cls: "c3_prioTILv",
        _mData: this._mData, //JSON.parse(JSON.stringify(this._mData))
        __grabSlot: (d, el) => {
          el.title = "drag and drop to set or change priority";
        },
        __prioritySlot: (d, el) => {
          el.textContent = d._priority;
          if(!isNaN(d._priority))
          {
            //el.textContent = priorityCount;
            el.textContent = d._priority;
            //priorityCount++;
            //el.parentNode.classList.add("c3_c3_prirtyCpgnROn");
          }
          else
          {
            el.textContent = Res.priorityNotSetCellLabel;
            el.title = Res.hintNotPrioritizedItem;
            //el.parentNode.classList.add("c3_c3_prirtyCpgnROff");
          }
        },
        _nameSlot: (d, el) => {
          el.title = d.name;
          el.textContent = d.name;
        },
        _pageSlot: (d, el) => {
          if (d.page && Array.isArray(d.page))
          {
            el.classList.add(...["c3_axnLblsBx", "c3_rows_wrap", "c3_gpC", "c3_align_center"]);
            let lst = d.page;
            lst.forEach((tg, i) => {
              let dv = document.createElement("div");
              dv.classList.add(...["c3_badge", "c3_badge-axnLbl", "c3_badge-page", "c3_axnLblpage", "c3_rows_nowrap_center"]);
              dv.title = "page: " + tg;
              let txt = document.createElement("div");
              txt.classList.add(...["c3_badgeTxt", "c3_wordBreak"]);
              txt.textContent = tg;
              dv.append(txt);
              el.append(dv);
            });
          }
        },
        _positionSlot: (d, el) => {
          el.title = d.name;
          if (d.position && Array.isArray(d.position))
          {
            el.classList.add(...["c3_axnLblsBx", "c3_rows_wrap", "c3_gpC", "c3_align_center"]);
            let lst = d.position;
            lst.forEach((tg, i) => {
              let dv = document.createElement("div");
              dv.classList.add(...["c3_badge", "c3_badge-axnLbl", "c3_badge-position", "c3_axnLblposition", "c3_rows_nowrap_center"]);
              dv.title = "position: " + tg;
              let txt = document.createElement("div");
              txt.classList.add(...["c3_badgeTxt", "c3_wordBreak"]);
              txt.textContent = tg;
              dv.append(txt);
              el.append(dv);
            });
          }
        },
        _descriptionSlot: (d, el, cont) => {
          // description
          el.textContent = d.description;
          el.title = d.description;
        },
        ___c3btnsSlot: (d, el) => {
          let dv = document.createElement("div");
          dv.classList.add(...["c3_prioRBtnC", "c3_rows_nowrap", "c3_rows_spacing_x", "c3_align_center"]);
          // expand
          let expandBtn = document.createElement("div");
          expandBtn.classList.add(...["c3_prioRBtn", "c3_btn", "ti-angle-down"]);
          expandBtn.title = "See details";
          on(expandBtn, "click", lang.hitch(this, function(evt) {
            udc.log(d);
            this.content.querySelectorAll(".c3_lvRwXpdH").forEach((xr) => xr.classList.remove("c3_lvRwXpdH"));
            this.content.querySelectorAll(".c3_lvRwXpdB").forEach((xr) => xr.remove());
            if (expandBtn.classList.contains("ti-angle-up"))
            {
              expandBtn.classList.add("ti-angle-down");
              expandBtn.classList.remove("ti-angle-up");
              return;
            }

            this.content.querySelectorAll(".c3_prioRBtn.ti-angle-up").forEach((xr) => {
              xr.classList.add("ti-angle-down");
              xr.classList.remove("ti-angle-up");
            });


            let cpgnId = d.id;
            if (d.id.startsWith("campaign+")) cpgnId = cpgnId.replace("campaign+", "");
            // fetch and show campaign details
            ModulesData.api.fetchPriorityCpgnInfo({id: cpgnId})
            .then(lang.hitch(this, function (cData){
              expandBtn.classList.remove("ti-angle-down");
              expandBtn.classList.add("ti-angle-up");
              let rwDv = evt.target.closest(".c3_lvRw");
              if (rwDv) rwDv.classList.add("c3_lvRwXpdH");

              let xpd = document.createElement("div");
              xpd.classList.add(...["c3_lvRwXpdB", "c3_cols_nowrap", "c3_gpR10", "c3_pd_20"]);
              //xpd.textContent = JSON.stringify(cData);
              this._showCpgnDetail({
                data: cData,
                dv: xpd
              });
              el.parentNode.after(xpd);
            }));
          }))
          dv.appendChild(expandBtn);
          el.appendChild(dv);
        },
        postCreateRowTbl: (rCtx) => {
          rCtx.node.classList.add(...["dojoDndItem", "c3_prioRw", "c3_posR"]);
          rCtx.node.dataset.itemId = rCtx.data.id;
          if(!isNaN(rCtx.data._priority))
          {
            rCtx.node.classList.add(...["c3_prioOn", "c3_cpgn_priority_item"]);
          }
          else
          {
            rCtx.node.classList.add(...["c3_prioOff", "c3_cpgn_not_priority_item"]);
          }
          if (isFilterSelected && rCtx.data && rCtx.data.matched !== "true")
          {
            rCtx.node.classList.add(...["c3_hide"]);
          }
          // drop target cue
          let beforeDv = document.createElement("div");
          beforeDv.classList.add(...["c3_DnDCue", "c3_lvDnDCue", "c3_DnDCueBf", "c3_rows_nowrap", "c3_rows_spacing", "c3_align_center"]);
          let bIcn = document.createElement("i");
          bIcn.classList.add(...["c3_DnDCueIcn", "ti-arrow-up"]);
          beforeDv.append(bIcn);
          let bTxt = document.createElement("div");
          bTxt.classList.add(...["c3_DnDCueTxt"]);
          bTxt.textContent = Res.beforeDnDCue;
          beforeDv.append(bTxt);
          rCtx.node.append(beforeDv);
          let afterDv = document.createElement("div");
          afterDv.classList.add(...["c3_DnDCue", "c3_lvDnDCue", "c3_DnDCueAf", "c3_rows_nowrap", "c3_rows_spacing", "c3_align_center"]);
          let aIcn = document.createElement("i");
          aIcn.classList.add(...["c3_DnDCueIcn", "ti-arrow-down"]);
          afterDv.append(aIcn);
          let aTxt = document.createElement("div");
          aTxt.classList.add(...["c3_DnDCueTxt"]);
          aTxt.textContent = Res.afterDnDCue;
          afterDv.append(aTxt);
          rCtx.node.append(afterDv);
        },
        postCreateGallery: (tCtx) => {
          this._createDndSource({container: tCtx.galleryContainer});
        }
      };
      if (this.typeMap) glryCtx.typeMap = this.typeMap;
      if (this._filter) glryCtx._filter = this._filter;
      if (this._glryMix) lang.mixin(glryCtx, this._glryMix);
      this.priTable = new C3Table2(glryCtx);
      this.priTable.startup();
      if (this.content) domConstruct.place(this.priTable.domNode, this.content, "last");
      this.own(this.priTable);

      //setTimeout(() => {
      //  this._createDndSource();
      //}, 750);
    },

    _createDndSource: function(ctx)
    {
      if (this._dndSrc)
      {
        this._dndSrc.destroy();
        this._dndSrc = null;
      }

      // Create the DnD source from the root div that can drop onto itself so that we can reorder.
      //this._dndSrc = new Source(this.priTable.domNode, {
      this._dndSrc = new Source(ctx.container, {
        copyOnly: true,
        selfAccept: true,
        withHandles:true
      });

      // Handle the drop event
      aspect.after(this._dndSrc, "onDrop", lang.hitch(this, "_arrangeDataAfterDrop"));
      
      //DnD start
      this._handleDnDStart = topic.subscribe("/dnd/start", lang.hitch(this, function(source, nodes,
        copy)
      {
        this._highlightTarget({
          source: source,
          nodes: nodes,
          copy: copy
        });
      }));
    },

    _highlightTarget: function(ctx)
    {
      var dragItem;
      //hightlight the dragged Item
      for (var i = 0; i < ctx.nodes.length; i++)
      {
        // Here, only one node is dragged.
        dragItem = document.getElementById(ctx.nodes[i].id);
        domClass.add(dragItem,"c3_tr_selected");
      }
      
      //show the target areas by highlighting them in a different way
      for(var j=0; j< ctx.source.parent.childNodes.length;j ++){
        var childNode = ctx.source.parent.childNodes[j];
        if (childNode && childNode.className
          && childNode.className.indexOf("c3_tr_selected") == -1)
        {
          domClass.add(childNode, "c3_tr_drop_target");
        }
      }
    },

    _onDnDCancel: function(ctx)
    {
      this._removeHighlight(ctx);
      this._unselectDraggedItem();
      this._DnDEvent = true;
      setTimeout(lang.hitch(this, function(){
        this._DnDEvent = false;
      }),1500);
    },

    _removeHighlight: function()
    {
      var nodeList = this._dndSrc.getAllNodes();
      for (var i = 0; i < nodeList.length; i++)
      {
        var node = nodeList[i];
        if (node && node.className && node.className.indexOf("c3_tr_selected") == -1)
        {
          domClass.remove(node, "c3_tr_drop_target");
        }
        else 
        {
          domClass.remove(node, "c3_tr_selected");
        }
      }
    },

    //.........................................
    _unselectDraggedItem: function()
    {
      this._dndSrc.selectNone();
    },

    //.........................................
    _arrangeDataAfterDrop: function()
    {
      this._DnDEvent = true;
      setTimeout(lang.hitch(this, function(){
        this._DnDEvent = false;
      }),1500);

      this._removeHighlight();
      // Keep moved row highlighted after drop.
      //this._unselectDraggedItem();
      var newArray = [];
      var nodeList = this._dndSrc.getAllNodes();
      for (var i = 0; i < nodeList.length; i++)
      {
        //var nodeId = nodeList[i].id ;
        // In C3Table2 the id get's
        // overwritten by widget id, so use
        // dataset property
        var nodeId = nodeList[i].dataset.itemId;
        // make sure contentId is there

        if (nodeId)
        {
          var len2 = this.getData().length;
          for (var j = 0; j < len2; j++)
          {
  
            var item = this.getData()[j];
            if (item.id === nodeId )
            {
              newArray[i] = item.id;
            }
          }
        }
      }
      this.setPriorityData(newArray);
      this._savePriorityList();
    },

    //...............................................
    _showCpgnDetail: function (ctx = {})
    {
      if (!ctx.data) return;
      // description
      if (ctx.data.description && ctx.data.description.length)
      {
        let elDescr = document.createElement("div");
        elDescr.classList.add(...["c3_prioDtlI", "c3_prioDtlDescr"]);
        elDescr.textContent = ctx.data.description;
        ctx.dv.append(elDescr);
      }
      // triggers
      if (Array.isArray(ctx.data.triggers))
      {
        let dv = document.createElement("div");
        dv.classList.add(...["c3_prioDtlIC", "c3_prioDtlTrgC", "c3_cols_nowrap"]);
        let elH = document.createElement("div");
        elH.classList.add(...["c3_prioDtlIV", "c3_prioDtlIH", "c3_prioDtlTrg"]);
        elH.textContent = "Triggers";
        dv.append(elH);
        let elD = document.createElement("div");
        elD.classList.add(...["c3_prioDtlIV", "c3_prioDtlID", "c3_prioDtlTrg"]);
        elD.textContent = ctx.data.triggers.join(", ");
        dv.append(elD);
        ctx.dv.append(dv);
      }
      // matched actions
      let ma = ctx.data.matchedActions;
      if (Array.isArray(ma) && ma.length)
      {
        let elMa = document.createElement("div");
        elMa.classList.add(...["c3_prioDtlI", "c3_prioDtlMA", "c3_cols_nowrap"]);
        ctx.dv.append(elMa);

        this._createMActionsHead(elMa);
        ma.forEach((a) => {
          this._createMActionsRow(elMa, a);
        });
      }
    },

    //........................................
    _createMActionsHead: function (elMa)
    {
      let aDv = document.createElement("div");
      aDv.classList.add(...["c3_prioDtlMAR", "c3_prioDtlMARH", "c3_rows_wrap"]);
      // if's are just for scope limit
      if ("a.name")
      {
        let dv = document.createElement("div");
        dv.classList.add(...["c3_prioDtlMAI", "c3_prioDtlMAName", "c3_lv_text_m"]);
        dv.textContent = Res.maName;
        aDv.append(dv);
      }
      if ("a.type")
      {
        let dv = document.createElement("div");
        dv.classList.add(...["c3_prioDtlMAI", "c3_prioDtlMAType", "c3_lv_text_m"]);
        dv.textContent = Res.maType;
        aDv.append(dv);
      }
      if ("a.page")
      {
        let dv = document.createElement("div");
        dv.classList.add(...["c3_prioDtlMAI", "c3_prioDtlMAPage", "c3_axnLblsBx", "c3_rows_wrap", "c3_gpC", "c3_lv_text_m"]);
        dv.textContent = Res.maPage;
        aDv.append(dv);
      }
      if ("a.position")
      {
        let dv = document.createElement("div");
        dv.classList.add(...["c3_prioDtlMAI", "c3_prioDtlMAPosition", "c3_axnLblsBx", "c3_rows_wrap", "c3_gpC", "c3_lv_text_m"]);
        dv.textContent = Res.maPosition;
        aDv.append(dv);
      }
      elMa.append(aDv);
    },

    //........................................
    _createMActionsRow: function (elMa, a)
    {
      let aDv = document.createElement("div");
      aDv.classList.add(...["c3_prioDtlMAR", "c3_prioDtlMARD", "c3_rows_wrap"]);
      // if's are just for scope limit
      if ("a.name")
      {
        let dv = document.createElement("div");
        dv.classList.add(...["c3_prioDtlMAI", "c3_prioDtlMAName", "c3_lv_text_m"]);
        dv.textContent = a.name || "";
        aDv.append(dv);
      }
      if ("a.type")
      {
        let dv = document.createElement("div");
        dv.classList.add(...["c3_prioDtlMAI", "c3_prioDtlMAType", "c3_lv_text_m"]);
        dv.textContent = a.type;
        aDv.append(dv);
      }
      if ("a.page")
      {
        let dv = document.createElement("div");
        dv.classList.add(...["c3_prioDtlMAI", "c3_prioDtlMAPage", "c3_axnLblsBx", "c3_rows_wrap", "c3_gpC", "c3_align_center", "c3_lv_text_m"]);
        a.page.forEach((pLbl, iP) => {
          let dvLbl = document.createElement("div");
          dvLbl.classList.add(...["c3_badge", "c3_badge-page", "c3_badge-axnLbl", "c3_axnLblpage", "c3_rows_nowrap_center"]);
          let dvLblTxt = document.createElement("div");
          dvLblTxt.classList.add(...["c3_badgeTxt", "c3_badge-axnLblTxt", "c3_wordBreak"]);
          dvLblTxt.textContent = pLbl;
          dvLbl.append(dvLblTxt);
          dv.append(dvLbl)
        });
        aDv.append(dv);
      }
      if ("a.position")
      {
        let dv = document.createElement("div");
        dv.classList.add(...["c3_prioDtlMAI", "c3_prioDtlMAPosition", "c3_axnLblsBx", "c3_rows_wrap", "c3_gpC", "c3_align_center", "c3_lv_text_m"]);
        a.position.forEach((pLbl, iP) => {
          let dvLbl = document.createElement("div");
          dvLbl.classList.add(...["c3_badge", "c3_badge-position", "c3_badge-axnLbl", "c3_axnLblposition", "c3_rows_nowrap_center"]);
          let dvLblTxt = document.createElement("div");
          dvLblTxt.classList.add(...["c3_badgeTxt", "c3_badge-axnLblTxt", "c3_wordBreak"]);
          dvLblTxt.textContent = pLbl;
          dvLbl.append(dvLblTxt);
          dv.append(dvLbl)
        });
        aDv.append(dv);
      }
      elMa.append(aDv);
    },

    //...........................................
    // end - Table
    //...........................................

    //...........................................
    // Filter
    //...........................................

    //..........................................
    _createFilter: function ()
    {
      var fData = [];

      this._filterList = (Array.isArray(fData)) ? fData : [];

      this.filterCtrl = new PrioritizeCpgnFilterCtrl({
        _parent: this,
        cls: "c3_priCpgnFltr",
        hideSort: true,
        _fltrBtnsContainer: this._fltrBtns,
        _fltrBtnsContainerPos: "last",
        _fltrSelItemsCont: this._selFltrCont,
        //type: fCtx.type,
        _filter: (this._filter) ? JSON.parse(JSON.stringify(this._filter)) : void(0),
        filterList: this._filterList,
        // callback
        onChange: (f = {}) => {
          udc.log(f);
          this._filter = (f) ? JSON.parse(JSON.stringify(f)) : {};
          // process filter change
          //this._reset();
          this._getAllData();
        }
      });
      this.filterCtrl.startup();
      if (this._fltrCont) domConstruct.place(this.filterCtrl.domNode, this._fltrCont);
      this.own(this.filterCtrl);
    },

    //.............................................
    // Check if page|position filter values
    // are selected
    isFilterSelected: function ()
    {
      if (this._filter)
      {
        if (this._filter.page && this._filter.page.length)
        {
          return true;
        }
        if (this._filter.position && this._filter.position.length)
        {
          // page filter is required. So ignore position for check.
          //return true;
        }
      }
      return false;
    },

    //.............................................
    // end - Filter
    //.............................................

    setData: function(data)
    {
      this._list = data;
    },

    getData: function()
    {
      return this._list;
    },

    setPriorityData: function(data)
    {
      this._pList = data;
    },

    getPriorityData: function()
    {
      return this._pList;
    },

    //.................................................
    _setEvents: function ()
    {
      //this.own(
      //);
    },
    
    //..............................
    clearData: function()
    {
      domConstruct.empty(this.content);
    },
    
    //..............................
    _subscribe: function()
    {
      this.own(
       topic.subscribe("/interactions/refresh", lang.hitch(this, function()
        {
          this._reloaded = true;
          this.clearData();
          this._init();
        }))
      );
    },

    // .................................................
    destroy: function()
    {
      try
      {
        if (Array.isArray(this._dfrList) && this._dfrList.length)
        {
          this._dfrList.forEach((df) => {
            if (df.rqCtx && df.rqCtx.abortCtx) df.rqCtx.abortCtx.abortIt = true;
          });
          //C3Util.reqMgr.abort();
        }
      }
      catch(er){}

      this.inherited(arguments);

      if (this._dndSrc)
      {
        this._dndSrc.destroy();
        this._dndSrc = null;
      }
      if( this._handleDnDStart )
      {
        this._handleDnDStart.remove();
      }
    },

    _end: 0
  });

});
