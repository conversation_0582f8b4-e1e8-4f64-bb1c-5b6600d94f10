/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define([
  "dojo/_base/declare", 
  "dojo/_base/lang",
  "dojo/on",
  "udc/udc",
  "udc/core/commons/loader",
  "z1/c3/desktop/start",
  "z1/c3/desktop/page/C3AlertBox",
  "z1/c3/desktop/page/RoleLoaderDef",
  "udc/core/rt/widgets/_ComplexBaseWidget",  
  "udc/core/rt/systemservice",
  "dojo/text!z1/c3/desktop/page/templates/Login.html",
  "dojo/i18n!z1/c3/desktop/page/nls/LoginRes",  
  "dojo/dom-class",
  "dojo/dom-construct",
  "z1/c3/desktop/page/C3DeleteDialogBox",
  "z1/common/C3Util",
  "z1/common/RouterUtil",
  "z1/common/FFUtil",
  "dojo/Deferred", "dojo/topic"
],
function(declare, lang, on, udc, loader, z1, Alert<PERSON>ox, RoleLoaderDef, _ComplexBaseWidget, systemService,
  templateStr, Res, domClass, domConstruct, DeleteDialogBox, C3Util, RouterUtil, FFUtil,
  Deferred, topic)
{

  /**
   * Creates a widget to login a c3 user.
   */
  return declare(_ComplexBaseWidget,
  {
    templateString : templateStr,
    
    Res: Res,
    
//    DEFAULT_INDUSTRIES_VALUE :"Select Industry",
    _elements: null,

    // Default value for max_group_count
    MAX_GROUP_COUNT: 5,
    
    // change this flag if registration option is not needed
    ALLOW_REGISTRATION: true,
    
    // mapping agent to content dev for now
    // revert back to agent once we start supporting it again
    Role: {
      "admin": "admin",
      "agent": "contentdev",
      "analyst": "analyst",
      "sysadmin": "sysadmin",
      "sysanalyst":"sysanalyst",
      "executive": "executive",
      "developer": "developer",
      "supervisor": "supervisor",
      "contentdev": "contentdev",
      "viewonly": "viewonly"
    },
    
    EteType: {
      state: "z1etesec"
    },
    

//    installationType : {
//      CLOUD: "cloud",
//      // no outside network
//      ON_PREMISES: "onprem"
//    },

    /**
     * Creates an instance of this page.
     * 
     * @param {Object}
     *          params The params should have a method with the name
     *          "onSuccess" to define the callback method called upon
     *          success. 
     *          It should also have a method with the name 
     *          "onNewUser" which is the callback method called upon
     *          clicking "New User?"
     * @param {Object}
     *          parentNode
     */
    constructor : function(params, parentNode)
    {
      this.inherited(arguments);
      this._parent = parentNode;
      this._onSuccess = params.onSuccess;
      this._onNewUser = params.onNewUser;
      this._elements = [];
      // window url at time of login
      this._wUrl = params._wUrl;
      // turn off|on marketing content
      params._isMktgContentOn = (typeof params._isMktgContentOn === "boolean") ?
        params._isMktgContentOn : true;
    },

    // ////////////////////////////////////////////////////////////////////////////
    // Implementations

    postCreate : function()
    {
      this.inherited(arguments);

      this.handleError();

      // init z1m sdk for html5 real time eventing
      try
      {
        // if (udc.z1Enabled && udc.apikey)
        if (ZineOne !== void(0) && this._isMktgContentOn)
        {
          // apikey for this fetch call will always be the
          // default c3 api key
          fetch('/c3/system/landingPageInfo', {
            "headers": {
               "apikey": C3Util.C3_KEY_PREFIX
            }
          })
            .then(response => response.json())
            .then(data => {
              if (data == void(0)) return;
              
              if (data && data.apikey)
              {
                // set api key
                udc.apikey = data.apikey;
                this._setKeyToSession();
                // graceful exception handling incase the api-key is deleted or invalid.
                try
                {
                  // LandingNav.js - if needed re-initialize and set other info
                  ZineOne.initialize(udc.apikey);
                  // Customer id and namespace are set after login
                  // ZineOne.setCustomerId(udc.context.getUser().id);
                  // ZineOne.addCustomerInfo("namespace", udc.context.getNamespace());

                  let z1Widget = ZineOne.getZ1Widget();
                  if (z1Widget)
                  {
                    // Raw Data handler
                    // This handler is removed when Login page is exited
                    z1Widget.registerWidgetHandler("raw", (rawData) => {
                      if (this._cont2) this._cont2.innerHTML = rawData;
                    });
                    // fire event
                    ZineOne.pushEvent("c3login_pg");
                    this.mainContainer.classList.add("c3_loginMktgOn");
                  }
                }
                catch (e)
                {
                  udc.log("API key not found in the request");
                }
              }

              udc.context.put("isSE_enabled", data?.enableScheduledExperience);
              udc.activateFullStory = false;
              udc.fullStoryNSList = [];
              if (data?.activateFullStory == "true")
              {
                // if full story is active
                // check for namespaces
                if (data?.fullStoryNamespaces && data?.fullStoryNamespaces != "")
                {
                  // remove all white spaces in namespace string
                  let nsStr = data.fullStoryNamespaces ? data.fullStoryNamespaces.replace(/\s+/g, "") : "";
                  udc.fullStoryNSList = nsStr != "" ? nsStr.split(",") : [];
                }
                // set full story status
                udc.activateFullStory = true;
              }
              // once flag is set in zineone properties
              // it continues to be used till
              // the next time the cluster is reset with new properties
              // intentionally stored in local instead of session storage
              localStorage.setItem("z1_enableStory", udc.activateFullStory);
              localStorage.setItem("z1_fs_ns", JSON.stringify(udc.fullStoryNSList));

              if (udc.activateFullStory)
              {
                // activate full story for login page
                // reassess the activation after login (LandingNav)
                C3Util.handleFullStoryActivation();
              }
              
              udc.activateNews = false;
              udc.frillId = "af8510c4-2948-4d24-b3f9-56c0dbb6e9ff";
              if (data && data.activateNews == "true")
              {
                if (data?.frillId && data?.frillId != "")
                {
                  udc.frillId = data.frillId;
                }
                udc.activateNews = true;
              }

              localStorage.setItem("z1_activateNews", udc.activateNews);
              localStorage.setItem("z1_frillId", udc.frillId);
              // Set the maxGroupCount, fail safe to 5 if the value is not returned
              udc.maxGroupCount  = data && data.maxGroupCount ? data.maxGroupCount : this.MAX_GROUP_COUNT;
              localStorage.setItem("z1_maxGroupCount", udc.maxGroupCount);
            });
        }
        else
        {
          if (this._cont2) this._cont2.remove();
        }
      }
      catch (e2)
      {
        udc.log(e2);
      }
    },

    // .....................................................
    startup : function()
    {
      this.inherited(arguments);
      // checkSessionTimeout is passed in by start js when
      // the context has finished loading
      // only show the banner if both cases are true
      var loginStatus = C3Util.getFromNSObj("_loggedout");
      if(loginStatus === "true" && this.checkSessionTimeout)
      {
        C3Util.setToNSObj("_loggedout", "false");
        udc.alert(Res.sessionTimeOut,"banner");

        // C3 has session timeout. perform logout here to complete the logout process.
        topic.publish("landing/logout", {});
      }
      
      // this._isDevMode();
      this._addUISpecificClass();
      if (this.queryParam && this.queryParam["validation"] && this.queryParam["validation"].toLowerCase() === "success") {
          //show verification successful message and reload the page. 
	         var dialogParams = {
		            "dialog_title": Res.accountVerTitle,
		            "dialog_msg": Res.accountVerMsg,
		            "dialog_ok": "Ok"
		        };
	         var dialog = new DeleteDialogBox(dialogParams);
	         dialog.startup();
	         dialog.setParent(this);
	         dialog.hideCancel();
	         dialog.setCallback(this._onMsgRead);
	         this._elements.push(dialog);
	         return;
      }
      else if(this.queryParam && this.queryParam["validation"] && this.queryParam["validation"].toLowerCase() === "failed") {
    	//show verification failed message and reload the page.  	
    	  var dialogParams = {
			            "dialog_title": Res.accountVerTitleFail,
			            "dialog_msg": Res.accountVerMsgFail,
			            "dialog_ok": "Ok"
			        };
		         var dialog = new DeleteDialogBox(dialogParams);
		         dialog.startup();
		         dialog.setParent(this);
		         dialog.hideCancel();
		         dialog.setCallback(this._onMsgRead);
		         this._elements.push(dialog);
		         return;
      }
//      this._fetchIndustriesData();

      // set focus on user id field
      // this.userid.focusNode.focus();
      this.userid.focus();

      this._setEvents();
    },

//    _isDevMode: function() {
//      if(this.devMode) {
//        domClass.remove(this.pwdDiv1, "c3_hide");
//        domClass.remove(this.pwdDiv2, "c3_hide");
//      }
//      else {
//        domClass.add(this.pwdDiv1, "c3_hide");
//        domClass.add(this.pwdDiv2, "c3_hide");
//      }
//    },

    //..........................................
    // Agent UI : add "_z1_a" as top level class
    // C3 UI : remove "_z1_a" from top level class
    _addUISpecificClass: function()
    {
      var dv = document.querySelector("._z1_a");
      if(dv)
      {
        domClass.remove(dv, "_z1_a");
      }
    },

    //...............................................
    // Reloads the /c3 url after removing queryparams if any.
    _onMsgRead: function()
    {
    	  // Get the request parameters for the url to reload
    	  var url = dojo.doc.location.origin + dojo.doc.location.pathname;
    	  //set the url -  the query params as the new url to load
    	  window.location = url;

    },
    
    _onKeyup: function(event, isSSO = false)
    {
      if (event && event.keyCode == 13)
      {
        if (isSSO) this._onSSOLogin();
        else this._onLogin();
      }
    },

    /**
    * Click handler to validate the login form and submit user cred for login.
    */
    _onLogin: function()
    {
      udc.clearAlert();
      
      this.resetLoginError();
      // var myForm = this.loginForm;
      // if (myForm.validate())
      if (this.validateLogin())
      {
        var uid = this.userid.value;
        var pwd = window.btoa(this.passwd.value);
        var onSuccess = this._onSuccess;
        var self = this;
        
        // assumption is that the dojo validation enforces the email syntax as __@___.com
        udc.context.put("namespace", uid.split('@')[1].replace(".", "_"));
        
        udc.showBusy(this._LoginBtnWrp);
        // try reloading the context with this namespace
        udc.context.loadContext().then(lang.hitch(this, function(success)
        {
          if (success)
          {
            // call platform to get the subcloud information
            C3Util.getAccountSettings().then((respData) => {
              new systemService().login(uid, pwd).then(lang.hitch(this, function(data) {
              udc.hideBusy();

              if (!data)
              {
                // baseURL is stripped off of any query string params
                let baseURL = window.location.href;
                baseURL = baseURL.substring(0, baseURL.indexOf("?"));
                window.location.href = baseURL;

                // force browser to reload a new session from server.
                window.location.reload(true);
                return;
              }

              if (data && data.status && data.status.toLowerCase() === "fail")
              {
                udc.log(data);
                data.message = data.message || data.reason || data.result;
                if(data.mode && data.mode === "inactive")
                {
                  // do nothing - the UI will show the failure message and exit
                }
                var serverMsg = (data.message && data.message != "" && data.message != "null") ? data.message : "";
                this.errCont.textContent = serverMsg || Res.loginFailedMsg;
                this.errCont.classList.remove("c3_hide");
                // wrong user/pwd. Clear pwd field to avoid getting
                // prompt again on 'enter' press.
                this.passwd.value = "";
                return;
              }

              // append the namespace to the base c3 url
              // this endpoint does not have any other query string parameters
              let ns = udc.context.namespace || udc.context.get('namespace');
              if (ns)
              {
                // namespaces are part of C3 deep links embedded in the weekly metric report email
                // if user opens c3 from an email link for ns1 and then logged in to ns2
                // then update the window URL to point to ns2
                RouterUtil.addWindowUrlParams({namespace: ns});
                this._wUrl = RouterUtil.getWindowUrl();
              }

              this.ld = data;
              this._setDefault().then(lang.hitch(this, function(d){

                // Extract the token key if sent
                var tkey = this.ld["tkey"] || null;
                if (tkey) udc.context.setTokenKey(tkey);

                if (this.ld && this.ld.industry)
                {
                  udc.context.put("industry", this.ld.industry);
                  // set namespace data in browser local storage
                  C3Util.setToNSObj("industry", this.ld.industry);
                }
                // the validate method returns the same string if its valid else it returns "default"
                // the role string is only valid if all of its parts are valid
                if (this.ld.user && this.ld.user.role) this.ld.user.role = this._validateRole(this.ld.user.role);
                if (this.ld.user && this.ld.user.role && this.ld.user.role.toLowerCase() !== "agent")
                {
                  RoleLoaderDef.loadAllRoleDefs().then(lang.hitch(this, function(data2)
                  {
                    udc.hideBusy();
                    var roles = this.ld.user.role.split("|");
                    var d = RoleLoaderDef.getRoleDef(roles);

                    udc.context.put("access", d);
                    udc.apikey = this.ld.apikey;
                    this._setKeyToSession();
                    onSuccess({
                      mode: this.ld.mode,
                      pwd: this.passwd.value,
                      user: this.ld.user,
                      _wUrl: self._wUrl
                    });

                  }), lang.hitch(this, function(error)
                  {
                    udc.hideBusy();
                  }));
                }
                else
                {
                  onSuccess({
                    mode: this.ld.mode,
                    pwd: self.passwd.value,
                    user: this.ld.user,
                    _wUrl: self._wUrl
                  });
                }
                if("FS" in window) {
                    FS.identify(udc.context.getUser().id);
                    C3Util.setFsVar();
                }
                C3Util.handleUserTracking('logged_in', {});
                // ZMOB-24802 Notify the Audiance server to start preloading the data
                // We are not expecting to handle any response at this point
                C3Util.preloadAudienceData();
                if (udc.activateNews)
                {
                  // activate news through frill for login page
                  C3Util.handleNewsActivation();
                }
                return true;
              }));

            }), lang.hitch(this, function(error)
            {
              udc.log(error);
              this.errCont.textContent = Res.generalError;
              this.errCont.classList.remove("c3_hide");
            }));
            })
          }
        }), lang.hitch(this, function(err)
        {
          udc.hideBusy();
          // baseURL is stripped off of any query string params
          let baseURL = window.location.href;
          baseURL = baseURL.substring(0, baseURL.indexOf("?"));
          window.location.href = baseURL;

          // force browser to reload a new session from server.
          window.location.reload(true);
          return;
        }));
      }
      else {}
    },

    //...................................................
    resetLoginError: function ()
    {
      this.useridErr.textContent = "";
      this.useridErr.classList.add('c3_hide');
      this.passwdErr.textContent = "";
      this.passwdErr.classList.add('c3_hide');
      this.errCont.textContent = "";
      this.errCont.classList.add("c3_hide");
    },

    //...................................................
    showLoginIdError: function (err)
    {
      this.useridErr.textContent = err;
      this.useridErr.classList.remove('c3_hide');
    },

    //...................................................
    showPasswordError: function (err)
    {
      this.passwdErr.textContent = err;
      this.passwdErr.classList.remove('c3_hide');
    },

    //...................................................
    validateLogin: function ()
    {
      // mark validity
      this._useridValidity();
      this._passwdValidity();

      if (this.userid.validity.valueMissing)
      {
        // user id is required
        this.showLoginIdError(Res.vRequiredLoginId);
        return false;
      }
      if (!this.userid.validity.valid)
      {
        this.showLoginIdError(Res.vInvalidLoginId);
        return false;
      }
      if (this.passwd.validity.valueMissing)
      {
        // password is required
        this.showPasswordError(Res.vRequiredPassword);
        return false;
      }
      if (!this.passwd.validity.valid)
      {
        this.showPasswordError(Res.vInvalidPassword);
        return false;
      }
      return true;
    },

    //........................................................
    _onSSOLogin: function()
    {
      var ns = this.attrDomain.value.trim().replace(".", "_");
      if (ns == "")
      {
        this.errCont.textContent = Res.errorDomain;
        this.errCont.classList.remove("c3_hide");
        return;
      }
      
      // retain the namespace
      udc.context.put("namespace", ns);
      udc.showBusy(this._onSSOContinueBtn);
      udc.context.loadContext().then(lang.hitch(this, function(success) {
        udc.hideBusy();
        if (success)
        {
          // call platform to get the sub-cloud information
          // always resolves to true, state is handled by C3Util
          C3Util.getAccountSettings().then((respData) => {
            // Send the key token if available
            var tokenKey = udc.context.getTokenKey() || window._udc_tkey || "";
            var targetUrl = "saml/login?namespace=" + ns;
            var xhr = new XMLHttpRequest();
            xhr.onreadystatechange = () =>
            {
              this.validUrl = false;
              if (xhr.readyState == 4 && xhr.status == 200)
              {
                if (xhr.response && xhr.response != "")
                {
                  try
                  {
                    var resp = JSON.parse(xhr.response);
                    if (resp.url && resp.url != "")
                    {
                      this.validUrl = true;
                      location.href = resp.url;
                    }
                  }
                  catch (err)
                  {
                  }
                }
                if (!this.validUrl)
                {
                  this.errCont.textContent = Res.ssoError;
                  this.errCont.classList.remove("c3_hide");
                }
              }
            };
            xhr.open("GET", targetUrl, true);
            xhr.setRequestHeader(C3Util.C3_KEY, C3Util.getC3Key());
            xhr.setRequestHeader("x-udc-tkey", tokenKey);
            xhr.send();
          })
        }
      }), lang.hitch(this, function(err){
        udc.hideBusy();
        // baseURL is stripped off of any query string params and the path
        let baseURL = window.location.href;
        baseURL = baseURL.substring(0, baseURL.indexOf("/"));
        window.location.href = baseURL + "/c3";
              
        // force browser to reload a new session from server. 
        window.location.reload(true);
        return;
      }));
    },
    
    _onSSOClick: function()
    {
      if (this._isMktgContentOn && ZineOne)
      {
        ZineOne.pushEvent("c3loginsso_pg");
      }

      this.errCont.classList.add("c3_hide");
      this.regularLogin.classList.add("c3_hide");
      this.ssoLogin.classList.remove("c3_hide");
    },
    
    _onSSOBackClick: function()
    {
      this.resetLoginError();
      this.ssoLogin.classList.add("c3_hide");
      this.regularLogin.classList.remove("c3_hide");
    },
    
    _setDefault: function()
    {
      var deferr = new Deferred();
      var targetUrl = "/c3/data/systemconfig/etesec";
      
      loader.GET(targetUrl, {}).then(lang.hitch(this, function(data){
        if (data.status && data.status.toLowerCase() === "fail")
        {
          udc.log(data);
          deferr.resolve({
            pass: true
          });
        }
        else
        {
          if (data && data.ete && data.ete === "false") udc.context.put(this.EteType.state, false);
          deferr.resolve({
            pass: true
          });
        }
      }), lang.hitch(this, function(err)
      {
        udc.log(err);
        deferr.reject(err);
      }));
      
      return deferr.promise;
    },
    
    _validateRole: function (rolestr)
    {
      var roles = rolestr.split("|");
      
      roles.forEach( (role) => {
        // if the role is not already default and you don't find it in the approved list then set it to default 
        if (rolestr != "default" && this.Role[role] == "undefined")
        {
          rolestr = "default";
        }
      });
      
      return rolestr;
    },

    _setKeyToSession: function()
    {
      if (udc.apikey)
      {
        if (typeof(Storage) !== "undefined")
        {
          try
          {
            var z1_apikey = sessionStorage.z1_apikey;
            if (z1_apikey == null || z1_apikey == "")
            {
              sessionStorage.z1_apikey = udc.apikey;
            }
          }
          catch(err)
          {
            udc.log(err);
          }
        }
      }
    },

    //..............................
    _onForgotPwd: function()
    {
      // this.processLoginHelp({cmd: "forgotpwd"});
      z1.fireEvent(z1.Events.forgotPwd);
    }, 
    
    _onRegisterReq: function()
    {
      // REMOVE THE RETURN ONCE THE END POINT IS SECURE
      // revert to previous functionality
      return;
    },

//    //..............................
//    // resend invite request
//    _onReinvite: function()
//    {
//      this.processLoginHelp({cmd: "reinvite"});
//    }, 

    //................................
    // Click Handler for forgot Pwd.
    // Deprecated - see ForgotPassword.js
//    processLoginHelp: function(ctx)
//    {
//      if (this.userid.value === "")
//      {
//        // user id empty
//        this.errCont.textContent = Res.missingUserIdMsg;
//        this.errCont.classList.remove("c3_hide");
//        return;
//      }
//      else
//      {
//        var self = this;
//        // validate if the user exists in the system
//        var targetUrl = "/c3/system/registration/validate?id=" + lang.trim(this.userid.value);
//        udc.showBusy(this.mainContainer);
//        loader.GET(targetUrl, {ignoreAuth:true}).then(lang.hitch(this, function(data)
//        {
//          udc.hideBusy();
//          if (data.message === "not-found")
//          {
//            this.errCont.textContent = self.userid.value + Res.invalidUserMsg;
//            this.errCont.classList.remove("c3_hide");
//            return;
//          }
//          else
//          {
//            if(ctx && ctx.cmd === "forgotpwd")
//            {
//              this._processForgotPwd(data);
//            }
////            else if(ctx && ctx.cmd === "reinvite")
////            {
////              this._processReinvite(data);
////            }
//          }
//
//        }), lang.hitch(this, function(error)
//        {
//          udc.hideBusy();
//          this.errCont.textContent = self.userid.value + Res.invalidUserMsg;
//          this.errCont.classList.remove("c3_hide");
//          return;
//        }));
//      }
//    },

    //.......................................
    // Deprecated - see: ForgotPassword.js
//    _processForgotPwd: function()
//    {
//      var self = this;
//      var targetUrl = "/c3/system/forgotPassword";
//      udc.showBusy(this.mainContainer);
//      loader.POST(targetUrl, {
//        ignoreAuth: true,
//        payload: {
//          emailid: this.userid.value
//        }
//      }).then( lang.hitch(this, function(data)
//      {
//        udc.hideBusy();
//        var data = data || {};
//        data.message = data.message || data.reason || data.result;
//        if(data.mode && data.mode === "inactive")
//        {
//          domClass.remove(this.infoBarOuter, "c3_hide");
//          domClass.remove(this.reinviteCont, "c3_hide");
//          domConstruct.empty(this.reinviteTxt);
//          if(data.message)
//          {
//            this.reinviteTxt.innerHTML = data.message;
//          }
//        }
//        this._alert({
//          title: Res.emailSent,
//          text: data.message || Res.genericPassResetMsg
//        });
//        return;
//      }), lang.hitch(this, function(error)
//      {
//        udc.hideBusy();
//        udc.alert(error);
//      }));
//    },

//    //.......................................
//    _processReinvite: function()
//    {
//      var self = this;
//      var targetUrl = "/c3/system/reinvite";
//      udc.showBusy(this.mainContainer);
//      loader.POST(targetUrl, {
//        ignoreAuth: true,
//        payload: {
//          emailid: this.userid.value
//        }
//      }).then( lang.hitch(this, function(data)
//      {
//        udc.hideBusy();
//        var data = data || {};
//        this._alert({
//          title: Res.reinviteTitle,
//          text: data.message || Res.reinviteMsg1 + "'" + self.userid.value + "'"
//        });
//        return;
//      }), lang.hitch(this, function(error)
//      {
//        udc.hideBusy();
//        udc.alert(error);
//      }));
//    },

//    //.................................
//    _loadCaptchaScript: function()
//    {
//      if(this.installationType.ON_PREMISES === this.installation)
//      {
//        // no captcha support
//        return;
//      }
//      script.get("https://www.google.com/recaptcha/api.js", {
//      }).then(lang.hitch(this, function(data){
//        udc.log(data);
//      }),lang.hitch(this, function(err){
//        udc.log(err);
//      }));
//    },

//    /**
//     * Gather data on first screen, validate it and make a "Get" Request to 
//     * validate the email Id that is entered from backend
//     */
//    _validateRegisterScreen: function()
//    {
//      var vFlag = true;
//      var firstScreenForm = this.personalFrm;
//      var firstScreenForm1 = this.personalFrm1;
//      var validation = (this.devMode) ? firstScreenForm.validate() && firstScreenForm1.validate() : firstScreenForm.validate();
//
//         //validate the form's required fields and format of email
//         if (validation)
//         {
//            if (this.devMode)
//            {
//              // Password and Confirm Pwd should have the same values
//              if (this.password.value !== this.confPassword.value)
//              {
//                this._alert({
//                  title: Res.pwdAlert,
//                  text: Res.pwdAlertMsg
//                });
//                vFlag = false;
//                return;
//              }
//              else
//              {
//                //this._preValidationChecking();
//              }
//            }
//            else
//            {
//              //this._preValidationChecking();
//            }
//            if(!this._validateIndustry())
//            {
//              vFlag = false;
//            }
//            if(!this._isValidName({text: this.firstName.value, fldName: "firstName"}))
//            {
//              vFlag = false;
//            }
//            if(!this._isValidName({text: this.lastName.value, fldName: "lastName"}))
//            {
//              vFlag = false;
//            }
//            if(vFlag)
//            {
//              this._preValidationChecking();
//            }
//         }
//    },

//    //.....................................
//    _isValidName: function(ctxObj)
//    {
//      var vFlag = true;
//      if(ctxObj.text === void(0) || lang.trim(ctxObj.text) === "")
//      {
//        vFlag = false;
//      }
//      if(vFlag)
//      {
//        var result = validator.isValidName(ctxObj.text);
//        if(!result.pass)
//        {
//          vFlag = false;
//        }
//      }
//      if(!vFlag)
//      {
//        this._alert({text: Res["fldName_" + ctxObj.fldName]})
//        return vFlag;
//      }
//      return true;
//    },

    //.....................................
//    _preValidationChecking: function() 
//    {
//      var self= this;
//      //the terms and condition cb should be checked
//      /*if(!this.termsCb.checked) {
//        var alertParam7 = {
//                  "dialog_title":Res.termsAlert, 
//                  "dialog_msg": Res.termsMsg 
//                };
//                 var alertDialog7 = new AlertBox(alertParam7);
//                 alertDialog7.startup();
//                 alertDialog7.setParent(this);
//                 this._elements.push(alertDialog7);
//                return; 
//      }
//      else { */
//        var targetUrl= "/c3/system/registration/validate/" + lang.trim(this.email.value);
//               udc.showBusy(this.mainContainer);
//               loader.GET(targetUrl, {ignoreAuth:true}).then(lang.hitch(this, function(data)
//               {
//                 udc.hideBusy();
//                 self._processEmailValidationResults(data);
//
//               }), lang.hitch(this, function(error)
//               {
//                 udc.hideBusy();
//                 udc.alert("Email Id is not valid." + error);
//                  
//               }));
//      //}
//    },
    
//    /**
//     * Check if the email id is valid and not present in the system.
//     * If not present than proceed to show the next screen.
//     * Otherwise show the error popup.
//     */
//    _processEmailValidationResults: function(data) {
//      if(data.status.toLowerCase() === "success") {
//          this._onRegister();
//      }
//      else {
//        this._alert({
//          title: Res.alertTitle, 
//          text: data.message
//        });
//        return;
//      } 
//    },

//    //.....................................
//    _fetchIndustriesData: function()
//    {
//      var targetUrl = "/c3/system/registration/industryList";
//      loader.GET(targetUrl, {ignoreAuth:true}).then(lang.hitch(this, function(data)
//      {
//        // populate the industries combo box
//        this.populateIndustries(data);
//      }), lang.hitch(this, function(error)
//      {
//        udc.log(error);
//      }));
//    },

//    //..................................
//    _validateIndustry: function()
//    {
//      if(this.comboBox.value === this.DEFAULT_INDUSTRIES_VALUE)
//      {
//        this._alert({
//          title: Res.alertIndustryTitle,
//          text: Res.alertIndustryMsg
//        });
//        return false;
//      } 
//      return true;
//    },

//    //...................................
//    populateIndustries: function(data)
//    {
//      // add id, required for FilteringSelect
//      for (var i = 0; i < data.length; i++)
//      {
//        if (data[i] && !data[i].id)
//        {
//          data[i].id = data[i].name;
//        }
//      }
//      var industriesStore = new Memory({
//        data: data
//      });
//      this.own(industriesStore);
//      this.comboBox = new FilteringSelect({
//        value: data[0].name,
//        store: industriesStore,
//        searchAttr: "name",
//        placeholder: "Industry Type",
//      }, this.ac_industriesBox);
//      this.own(this.comboBox);
//      domClass.add(this.comboBox.domNode, "inputbox c3_industries_cb c3_dj_fld c3_dj_fld_deco_signup");
//    },

//    /**
//     * Clicking on Register after filling in all 3 screens and validating, 
//     * post the data to backend to register the user
//     */
//    _onRegister: function()
//    {
//
//      var registerUserData= {};
//      registerUserData.fname = lang.trim(this.firstName.value);
//      registerUserData.lname = lang.trim(this.lastName.value);
//      registerUserData.email = lang.trim(this.email.value);
//      registerUserData.companyName = lang.trim(this.company.value);
//      registerUserData.industry = lang.trim(this.comboBox.value);
//      if(this.devMode) {
//        registerUserData.password = lang.trim(this.password.value);
//      }
//      if(this.installationType.ON_PREMISES !== this.installation)
//      {
//        // no captcha support
//
//        // attach Captcha data to payload
//        var captchaEl = document.querySelector('.g-recaptcha-response');
//        if(captchaEl && captchaEl.value)
//        {
//          registerUserData["recaptchaResponse"] = captchaEl.value;
//        }
//        else
//        {
//          this._alert({text: Res.vRecaptchaAnswerRequired});
//          return;
//        }
//  
//        // prevent same captcha being sent again
//        if(registerUserData["recaptchaResponse"] && this._previousCaptcha === registerUserData["recaptchaResponse"])
//        {
//          this._alert({text: Res.vRecaptchaAgain});
//          // reset recaptcha
//          grecaptcha.reset();
//          return;
//        }
//        else
//        {
//          this._previousCaptcha = registerUserData["recaptchaResponse"];
//        }
//      }
//
//      //2. post the data to the backend
//      var targetUrl= "/c3/system/registration/register";
//      udc.showBusy(this.mainContainer);
//      loader.POST(targetUrl, {
//        payload: registerUserData, ignoreAuth:true
//      }).then(lang.hitch(this, function(data)
//      {
//          udc.hideBusy();
//          if(data && data.success !== void(0) && data.success === false)
//          {
//            if(this.installationType.ON_PREMISES === this.installation)
//            {
//              // should not come here.
//              var msg = data.message || data.reason;
//              this._alert({text: msg});
//              return;
//            }
//              // no captcha support
//            // Recaptcha verification failed
//            this._alert({text: Res.reCaptchaFailed});
//            if(this._isCaptchaLoaded)
//            {
//              // reset recaptcha
//              grecaptcha.reset();
//            }
//            return;
//          }
//          if (data && data.status === "fail")
//          {
//            if(this.installationType.ON_PREMISES === this.installation)
//            {
//              // should not come here.
//              var msg = data.message || data.reason;
//              this._alert({text: msg});
//              return;
//            }
//            this._alert({text: Res.missingReCaptcha});
//            return;
//          }
//          this._onRegistrationSuccess(data);
//
//      }), lang.hitch(this, function(error)
//      {
//        udc.hideBusy();
//        udc.alert(error);
//      }));
//    } ,
    
//    /**
//     * Callback when the user has been successfully registered at the backend.
//     * Also if user has selected channels then show the OAUth page for getting the  fb access token
//     * 
//     */
//    _onRegistrationSuccess: function(data) 
//    {
//      //open the oAuthDialog which asks the user to login to fb.
//      this.registerData = data;
//      this._onOAuthSuccess();
//    },
//    
//    /**
//     * On registration and successful authorization log into the system and take the user 
//     * to the channel settings page.
//     */
//    _onOAuthSuccess: function()
//    {
//      var self = this;
//      if (this.registerData.status.toLowerCase() === "success")
//      {
//        // show registration successful message and reload the page.
//        var dialogParams1 = {
//          "dialog_title": Res.regSuccess,
//          "dialog_msg": this.registerData.message
//        };
//        var dialog1 = new DeleteDialogBox(dialogParams1);
//        dialog1.startup();
//        dialog1.setParent(this);
//        dialog1.hideCancel();
//        dialog1.setCallback(this._onMsgRead);
//        this._elements.push(dialog1);
//        return;
//      }
//      else
//      {
//        this._alert({
//          title: Res.regFailure,
//          text: this.registerData.message
//        });
//        return;
//      }
//    },
    
    getCookie: function(name)
    {
      const value = `; ${document.cookie}`;
      const parts = value.split(`; ${name}=`);
      if (parts.length === 2) return parts.pop().split(';').shift();
    },
    
    setCookie: function(name, value, exdays)
    {
      var d = new Date();
      d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
      var expires = "expires="+d.toUTCString();
      document.cookie = name + "=" + value + ";" + expires + ";path=/";
    },

    handleError: function()
    {
      var cookie = this.getCookie("LoginError");
      
      if (cookie)
      {
        switch (cookie)
        {
          case "0":
            this.errCont.textContent = Res.generalError;
            this.errCont.classList.remove("c3_hide");
            break;
          case "1":
            this.errCont.textContent = Res.badLoginError;
            this.errCont.classList.remove("c3_hide");
            break;
          case "2":
            this.errCont.textContent = Res.noAccountError;
            this.errCont.classList.remove("c3_hide");
            break;
          case "3":
            this.errCont.textContent = Res.existingAccountError;
            this.errCont.classList.remove("c3_hide");
            break;
          case "4":
            this.errCont.textContent = Res.inactiveAccountError;
            this.errCont.classList.remove("c3_hide");
            break;
          case "5":
            this.errCont.textContent = Res.tooManyRetriesError;
            this.errCont.classList.remove("c3_hide");
            break;
          case "6":
            this.errCont.textContent = Res.ssoAuthFailedError;
            this.errCont.classList.remove("c3_hide");
            break;
          case "7":
            this.errCont.textContent = Res.ssoMissingUsernameError;
            this.errCont.classList.remove("c3_hide");
            break;
        }
        this.setCookie("LoginError", "", 0);
      }
    },

    // ................................................
    _alert: function(msg)
    {
      var alertParams = {
        "dialog_title": msg.title || Res.info,
        "dialog_msg": msg.text
      };
      var alertDialog = new AlertBox(alertParams);
      alertDialog.startup();
      alertDialog.setParent(this);
      this.own(alertDialog);
    },

    //...................................
    _useridValidity: function ()
    {
      let opn = (this.userid.validity.valid) ? "remove" : "add";
      this.userid.classList[opn]("c3_invalid");
    },

    //...................................
    _passwdValidity: function ()
    {
      let opn = (this.passwd.validity.valid) ? "remove" : "add";
      this.passwd.classList[opn]("c3_invalid");
    },

    //................................
    _setEvents: function()
    {
      this.own(
        // toggle pwd view (eye)
        on(this.eyeIconC, "click", lang.hitch(this, function(e) {
          this.passwd.type = this.passwd.type === 'password' ? 'text' : 'password';
          let eyeHint = Res.eyeOffHint;
          let eyeIcnId = "#zi-eye-inactive";
          if(this.eyeIcon.href && this.eyeIcon.href.animVal === "#zi-eye-inactive")
          {
            eyeIcnId = "#zi-eye-active";
            eyeHint = Res.eyeOnHint;
          }
          this.eyeIcon.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', eyeIcnId);
          this.eyeIconC.title = eyeHint;
        })),
        on(this.userid, "input", lang.hitch(this, function(e) {
          this.resetLoginError();
          this._useridValidity();
        })),
        on(this.passwd, "input", lang.hitch(this, function(e) {
          this.resetLoginError();
          this._passwdValidity();
        })),
        on(this.userid, "keyup", lang.hitch(this, function(e){
          this._onKeyup(e);
        })),
        on(this.passwd, "keyup", lang.hitch(this, function(e){
          this._onKeyup(e);
        })),
        on(this._onLoginBtn, "click", lang.hitch(this, function(){
          this._onLogin();
        })),
        on(this._onSSOLoginBtn, "click", lang.hitch(this, function(){
          this._onSSOClick();
        })),
        on(this.attrDomain, "keyup", lang.hitch(this, function(e){
          this._onKeyup(e, true);
        })),
        on(this._onSSOBack, "click", lang.hitch(this, function(){
          this._onSSOBackClick();
        })),
        on(this._onSSOContinueBtn, "click", lang.hitch(this, function(){
          this._onSSOLogin();
        }))
      );
    },

    //.....................................................
    destroy: function()
    {
      try
      {
        if (this._isMktgContentOn && ZineOne)
        {
          // remove / overwrite rawData handler which is specific
          // to Login page
          try
          {
            let z1Widget = ZineOne.getZ1Widget();
            if (z1Widget)
            {
              // register a dummy function as handler
              z1Widget.registerWidgetHandler("raw", (data) => {});
            }
          }
          catch (e)
          {
            udc.log("Overwrite rawData handler ", e);
          }
        }
      }
      catch(er)
      {
        udc.log(er);
      }

      this.inherited(arguments);
      this.destroyElements();
    },

    destroyElements: function() {
    	 // find all the goal summary widgets under our content div and destroy them
        for (var i = 0; i < this._elements.length; i++)
        {
          if (this._elements && typeof(this._elements[i].destroyRecursive) === 'function')
          {
            this._elements[i].destroyRecursive();
          }
        }
        this._elements = [];
    },
    
    _end : 0
  });

});
