define([
  "dojo/_base/declare",
  "dojo/_base/lang",
  "dojo/on",
  "udc/udc",
  "dijit/registry",
  "z1/c3/desktop/start",
  "dojo/topic",
  "dojo/dom-construct",
  "udc/core/rt/widgets/_BaseWidget",
  "z1/common/C3Util", "z1/common/FFUtil",
  "z1/c3/desktop/page/timeselector/C3LitePicker",
  "dojo/i18n!z1/c3/desktop/page/nls/PredictionModelDashRes",
  "dojo/text!z1/c3/desktop/page/templates/machineLearning/predictionModel/PredictionModelDash.html",
  "z1/c3/desktop/page/util/positionItMx",

], function(declare,lang, on, udc, registry, z1, topic, domConstruct, BaseWidget,
    C3Util, FFUtil, C3LitePicker, Res, templateStr,positionItMx)
{
  const tabMap = {
    "1": {widget: "Overview", displayTs: 1},
    "2": {widget: "Results", displayTs: 1},
  };
  const EVT_DATE_CHANGE = "ml/predictionModel/DateChange";
  const EVT_TAB_CHANGE = "ml/predictionModel/TabChange";
  const TAB_ID = "predDashTabId_";

  return declare([BaseWidget,positionItMx],
  {
    templateString: templateStr,
    Res: Res,

    constructor: function (params, parentNode)
    {
      this.inherited(arguments);

      params._pCtx = params.pCtx ? params.pCtx : {};
      if (params.parentNode) params._parent = parentNode;
      this.modelType = udc.context.get("primaryModelType");
      this.modelId = this.modelType === "EPPV2" ? "z1.ml.system.EPPV2"
                            : this.modelType === "EPPV2CLK4" ? "z1.ml.system.EPPV2CLK4"
                            : "z1.ml.system.EPPV2";
      this.enableOOTB = this.modelType === "EPPV2" ? udc.context.get("enableEPPV2")
                            : this.modelType === "EPPV2CLK4" ? udc.context.get("enableEPPV2CLK4")
                            : false;
    },

    postCreate: function ()
    {
      this.inherited(arguments);
    },

    startup: function ()
    {
      this.inherited(arguments);
      this._init();
    },
    _init: function()
    {
      this._createTimeSelectorDisplay();
      this.createInfoBox();
      this._setEvents();
      this._createTitleSuffix();
    },

    _createTitleSuffix: function()
    {
      if(this._title.textContent)
      {
        let suffix = this.modelType?.toLowerCase() == "eppv2clk4" ? Res.predModTtl4ClkSuffix : Res.predModTtl5ClkSuffix;
        this._title.textContent += suffix;
      }
    },

    _createTimeSelectorDisplay: function()
    {
      if (!this.timeSelectorDisplay)
      {
        this.timeSelectorDisplay = new C3LitePicker({
          _parent: this,
          _isRelative: false,
          _cls: ["c3_litePicker_dt_cont"],
          zData: {
            showFrequency: false,
            showDaysPlusOnly: true,
            predefinedRanges: [
              "opLast365Days",
              "opLast180Days",
              "opLast90Days",
              "opLast30Days",
              "opLast7Days"
            ],
            dateRange: {
              min: new Date(FFUtil.getMinimumDateRange())
            },
            allowPartialDays: true,
            predefinedDate: "opLast30Days"
          },
          storageName: "shareGroup1",
          useLocalStorage: true,
          afterStartup: () => {
            this.dtCtx = this.timeSelectorDisplay.getDateRange();
            this.initTabs();
          }
        });
      }
      this.timeSelectorDisplay.startup();
      this.timeSelectorDisplay.setCallback(this._onTimeSelectionChanged, this);
      domConstruct.place(this.timeSelectorDisplay.domNode, this._timeSelCont, 'only');
      this.own(this.timeSelectorDisplay);
    },

    // create the tooltip box that holds the description for early purchase prediction page
    createInfoBox: function()
    {
      this.infoBx = this.infoBx || document.createElement("div");
      this.infoBx.classList.add(...["c3_hide", "c3_pd_12", "c3_br4", "c3_posA",
        "c3-bg-c-gray0", "c3-c-gray110","c3_minMax_w350", "c3_shdwBx2","c3_fs14","c3_lh20"]);
      document.body.appendChild(this.infoBx);
      this.own(this.infoBx);
    },
    _onTimeSelectionChanged: function(ctx)
    {
      this.dtCtx = ctx;
      topic.publish(EVT_DATE_CHANGE, ctx);
    },

    initTabs: function ()
    {
      let l = this._tabsCont.children;
      let tabNum = null;
      for (var i = 0; i < l.length; i++)
      {
        if (l[i].classList.contains("c3_hide_imp") == false 
        && l[i].dataset != void(0) && l[i].dataset.tabNum != undefined)
        {
          tabNum = l[i].dataset.tabNum;
          break;
        }
      }
      this._openSelectedTab(tabNum);
    },

    _openSelectedTab: function (tabNum)
    {
      if (!tabNum) return;

      let t = tabMap[tabNum];
      if (!t) return;
      if(t.widget == "Overview"){
       // if the deploy option was enabled, and user switches to Overview tab toggle the date picker and hide the deploy button
        if(typeof this?._predResults?.toggleDatePicker == "function")
          this._predResults.toggleDatePicker("HIDE_DEPLOY");
      }
      else if(t.widget == "Results")
      {
        if(typeof this?._predResults?._toggleDeployOption == "function")
          this._predResults._toggleDeployOption();
      }
      this._selectedTabNum = tabNum;
      let display = t.displayTs != void(0) ? t.displayTs : 1;
      this._markSelected(this["tab" + tabNum]);
      this._toggleDisplayTo(this["_predTabCont" + tabNum], display);
      if (typeof this["_open" + t.widget] === "function")
      {
        this["_open" + t.widget](tabNum);
      }
      let ctx = {
        "tabId": TAB_ID + tabNum
      };
      topic.publish(EVT_TAB_CHANGE, ctx);
    },


    //.........................................
    _toggleDisplayTo: function (node, display)
    {
      if (!node) return;
      display = (display != void(0)) ? display : 1;
      var nodeList = this._bCont.children;
      this._predDashCont.dataset.isTimeDsp = display;

      if (nodeList && nodeList.length)
      {
        for (var i = 0; i < nodeList.length; i++)
        {
          if (node != nodeList[i]) {
            nodeList[i].classList.add(...["c3_hide"]);
          }
          else {
            nodeList[i].classList.remove("c3_hide");
          }
        }
      }
    },

    _markSelected: function (node)
    {
      this._tabsCont.querySelectorAll(".c3_tab2Sel").forEach(t => t.classList.remove("c3_tab2Sel"));
      if (!node) return;
      node.classList.add(...["c3_tab2Sel"]);
    },

    _openOverview: function(tabNum)
    {
      let node = this["_predTabCont" + tabNum] || this._predTabCont1;
      if (!this._predOvrvw)
      {
        require(["z1/c3/desktop/page/machineLearning/predictionModel/PredictionModelOvrvw"],
        (widgetModule) => {
          this._predOvrvw = new widgetModule({
            _parent: this,
            tsSubscribeStr: EVT_DATE_CHANGE,
            tabSubscribe: {str: EVT_TAB_CHANGE, tabId: TAB_ID + tabNum},
            tabId: TAB_ID + tabNum,
            dtCtx: this.dtCtx,
            busyContainer: this._predDashCont,
            modelType: this.modelType,
            modelId: this.modelId,
            enableOOTB: this.enableOOTB
          });
          this._predOvrvw.startup();
          domConstruct.place(this._predOvrvw.domNode, node, 'only');
          this.own(this._predOvrvw);
        });
      }
    },

    _openResults: function(tabNum)
    {
      let node = this["_predTabCont" + tabNum] || this._predTabCont2;

      if (!this._predResults)
      {
        require(["z1/c3/desktop/page/machineLearning/predictionModel/PredictionModelResults"],
        (PredictionModelResults) => {
          this._predResults = new PredictionModelResults({
            _parent: this,
            tsSubscribeStr: EVT_DATE_CHANGE,
            tabSubscribe: {str: EVT_TAB_CHANGE, tabId: TAB_ID + tabNum},
            tabId: TAB_ID + tabNum,
            dtCtx: this.dtCtx,
            modelCtx: {modelId: this.modelId, modelType: this.modelType},
            getPredOvrvwData: (channelType) => {
              /*
                returns the value from predictionModelOvrvw function ( getPredOvrvwData(channelType) )
              */
              if (this._predOvrvw)
              {
                return this._predOvrvw.getData(channelType);
              }
            },

            getNSTZ: () => {
              if(this._predOvrvw)
              {
                return this._predOvrvw.getNSTZ();
              }
            },

            getDateContext: () => {
              return { ...this.dtCtx };
            },

            toggleDatePicker: (displayState) => {
              /*
                toggle the date picker & deploy button for Prediction Model Page
              */
              switch (displayState)
              {
                case "HIDE_DATE":
                  this._timeSelCont.classList.add("c3_hide");
                  this._deployBtn.classList.remove("c3_hide");
                  break;
                case "HIDE_DEPLOY":
                  this._timeSelCont.classList.remove("c3_hide");
                  this._deployBtn.classList.add("c3_hide");
                  break;
                case "HIDE_BOTH":
                  this._timeSelCont.classList.add("c3_hide");
                  this._deployBtn.classList.add("c3_hide");
                  break;
                case "SHOW_BOTH":
                  this._timeSelCont.classList.remove("c3_hide");
                  this._deployBtn.classList.remove("c3_hide");
                  break;
              }
            },
            _showModelComparisonFilter: udc.context.get("userAccessChampionChallenger") === "true" ? true
                                                       : udc.context.get("userAccessChampionChallenger") === "false" ? false
                                                       : udc.context.get("enableChampionChallenger")
          });
          this._predResults.startup();
          domConstruct.place(this._predResults.domNode, node, 'only');
          this.own(this._predResults);
        });
      }
    },

    _setEvents: function ()
    {
      this.own(
        on(this.tab1, "click", lang.hitch(this, () => {
          this._openSelectedTab(1);
        })),
        on(this.tab2, "click", lang.hitch(this, () => {
          this._openSelectedTab(2);
        })),
        on(this._deployBtn, "click", lang.hitch(this, () =>{
          topic.publish("ml/predictionModel/deployModel", {});
        })),
        // event listener for hovering over the i icon
        on(this._titleInfo,"mouseover", lang.hitch(this, () => {
          if (this.infoBx)
          {
            let descDetails = this.modelType?.toLowerCase() == "eppv2clk4" ? Res.descDetails4Clk : Res.descDetails;
            this.infoBx.textContent = descDetails;
            this.infoBx.classList.remove('c3_hide');
            this._positionIt3(this.infoBx, this._titleInfo);
            
          }
        })),
          on(this._titleInfo, "mouseout", lang.hitch(this, () => {
            this.infoBx.textContent = "";
            this.infoBx.classList.add('c3_hide');
          }))
      )
    },

    destroy: function()
    {
      this.inherited(arguments);
    },

    _end: 0
  });
  
});