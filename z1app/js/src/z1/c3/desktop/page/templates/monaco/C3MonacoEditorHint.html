<details class="c3_mncoHint c3_dlts ${cls} c3_cols_nowrap c3_hidden"
	data-dojo-attach-point="dialogBody">
	<summary class="c3_dtlsH c3_mncoHintH c3_pd_10">${Res.codeHint}</summary>
	<div class="c3_mncoHintB c3_mnco_out c3_cm_out c3_cm_fx c3_flex_1 c3_cols_nowrap c3_hidden"
		data-dojo-attach-point="dialogBody2">
		<div class="c3_mnco_c c3_mnco_c1 c3_cm c3_cm_c c3_cm_c1 c3_cm_fx c3_flex_1 c3_cols_nowrap"
			data-dojo-attach-point="_c1Cont">
			<div class="c3_cm_toolsCont c3_rows_nowrap_start c3_rows_spacing c3_align_center c3_hide"
				data-dojo-attach-point="_cmToolsCont">
			</div>
			<div class="c3_cm_cIn c3_cm_fx c3_flex_1 c3_cols_nowrap" data-dojo-attach-point="container"></div>
		</div>
	</div>
</details>