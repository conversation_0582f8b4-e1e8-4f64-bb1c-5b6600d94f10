<div class="c3_cols_nowrap">
    <div class="c3_hide c3_pd_12" data-dojo-attach-point="_opType">AND</div>
    <div class="c3_cols_nowrap c3_gp20 c3-bg-c-purple40 c3_bg-opcty05 c3_pd_20 c3_pd_24t c3_pd_24b c3_br20"
         data-dojo-attach-point="_cont">
        <div class="c3_rows_nowrap">
            <div class="c3_rows_nowrap_start_center c3_gp14 c3_fs14">
                <div class="c3_rows_nowrap_start_center c3_gp10">
                    <div>${Res.activityContTitle}</div>
                    <div class="c3-c-purple40 c3_sq16 c3_tooltip2 c3_tooltip_W140 c3_tooltip_show_bot" 
                            data-tooltip="${Res.activityTriggerDesc}"
                            data-dojo-attach-point="_dpInfoTooltip">
                        <svg class="c3i c3i_txt c3_sq16">
                            <use xlink:href="#zi-info-outline3"></use>
                        </svg>
                    </div>
                </div>
                <div class="c3_rows_nowrap c3_align_center c3_gp14">
                    <div class="c3_rows_nowrap_start_center c3_gp6" data-dojo-attach-point="_orOperatorWrp">
                        <input class="c3_customRadio c3_mrg_0imp" id="orOperator" type="radio" value="or" name="operatorGroup"
                            data-dojo-attach-point="orOperator">
                        <label class="c3_btn c3_fs14" for="orOperator">${Res.orOperatorTitle}</label>
                    </div>
                    <div class="c3_rows_nowrap_start_center c3_gp6" data-dojo-attach-point="_andOperatorWrp">
                        <input class="c3_customRadio c3_mrg_0imp" id="andOperator" type="radio" value="and" name="operatorGroup"
                            data-dojo-attach-point="andOperator">
                        <label class="c3_btn c3_fs14" for="andOperator">${Res.andOperatorTitle}</label>
                    </div>
                </div>
            </div>
            <div class="c3-c-red40" data-dojo-attach-point="_deleteBtn">
                <svg class="c3i c3i_txt c3_sq16" xmlns="http://www.w3.org/2000/svg"><use xlink:href="#zi-bin"></use></svg>
            </div>
        </div>
    </div>
</div>
