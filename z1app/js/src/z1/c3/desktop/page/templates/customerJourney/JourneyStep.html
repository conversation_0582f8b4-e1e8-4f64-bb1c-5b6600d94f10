<div data-dojo-attach-point="jSeg_container"
	class="c3_cj_stepBox c3_cj_stepBox_step c3_cj_cell c3_rows_nowrap c3_align_center">
	<div data-dojo-attach-point="jSeg_container_inner"
		class="c3_cj_seg_step_box c3_cj_cell_c c3_cj_cell_1 c3_flex_center_1">
		<div data-dojo-attach-point="jSegOuter"
			class="c3_rows_nowrap c3_cj_segOuter">
			<div class="c3_cj_box1 c3_cols_nowrap_edge c3_cols_spacing">
				<div class="c3_cj_delBtn" data-dojo-attach-point="delBtn"
					data-dojo-attach-event="ondijitclick:_onDeleteSegment"
					title="Delete '${name}'"
					data-tooltipBlocked="Delete '${name}'">
					<i class="c3-ti ti-trash"></i>
				</div>

				<div class="c3_cj_stepBoxDtlName c3_flex_center_1 c3_hidden" title="${Res.editNameHint}" data-dojo-attach-point="_stepDetailName">
					<div class="c3_cj_stepBoxDtlInnerNameIcon c3_flex_center_1"><i class="c3-ti ti-pencil"></i></div>
					<div class="c3_cj_stepBoxDtlInnerName c3_rows_nowrap c3_rows_spacing c3_align_end">
						<div data-dojo-attach-point="_jStepNumCont" class="c3_cj_stepBoxNum"></div>
						<div data-dojo-attach-point="_jName" class="c3_cj_stepBoxName c3_text_ellipsis"></div>
					</div>
				</div>
				<div class="c3_cj_stepBoxDtlStt c3_btn c3_rows_nowrap_around c3_align_center"
					title="${Res.showDetailsHint}" 
					data-dojo-attach-point="_statsBx">
					<div data-dojo-attach-point="_countEnteredBx" class="c3_cj_stepBoxStCount c3_cols_nowrap c3_align_center">
						<div class="c3_cj_stepBoxCount" data-dojo-attach-point="_countEnteredCont"></div>
						<div class="c3_cj_stepBoxCountLbl">${Res.countEnteredLbl}</div>
					</div>
					<div data-dojo-attach-point="_countPresentBx" class="c3_cj_stepBoxStCount c3_cols_nowrap c3_align_center">
						<div class="c3_cj_stepBoxCount" data-dojo-attach-point="_countPresentCont"></div>
						<div class="c3_cj_stepBoxCountLbl">${Res.countPresentLbl}</div>
					</div>
					<div data-dojo-attach-point="_countExitedBx" class="c3_cj_stepBoxStCount c3_cols_nowrap c3_align_center">
						<div class="c3_cj_stepBoxCount" data-dojo-attach-point="_countExitedCont"></div>
						<div class="c3_cj_stepBoxCountLbl">${Res.countExitedLbl}</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div data-dojo-attach-point="container_rhs"
		class="c3_cj_cell_c c3_cj_cell_2 c3_flex_center_1">
		<img src="/res/next_arrow_seg_journey.png" class="journey_step_img"
			data-dojo-attach-point="arrowImg" />
	</div>
</div>