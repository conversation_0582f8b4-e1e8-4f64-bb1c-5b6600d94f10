<div>
	<div class="c3_col_center">
	  <div class="c3_col_center1">
	   <div class="c3_goal_canvas_header_div" data-dojo-attach-point="whichHeader">
			<span data-dojo-attach-point="signalGroupTitle" title="Rename" class="c3_canvas_header_title c3_which_sg_canvas_title"
			      data-dojo-attach-event="ondijitclick:_onRenameSignalGroup"></span>
			<span id="whichsaveText" class="c3_saveText c3_hide">${Res.savingText}</span>

 			<div  id="infoWhoTooltip" class="info-bttn">
                 <i class="fa-info"></i>
            </div>
		</div>
		<div class="c3_col_center-inner" data-dojo-attach-point="content"></div>
        <div class="c3_segments_btn_row"> 
           <span class="c3_define_actions" data-dojo-attach-event="ondijitclick:_onDefineActions">${Res.defineActions}
               <i class="fa-angle-double-right"></i>
           </span> 
           <span data-dojo-attach-event="ondijitclick:_onCreateSignalGroup" class="c3_addBtn1 c3_goals_cursor_pointer"> 
              <i class="fa-plus newGoalBtn"></i> 
           <span>${Res.newSignalGroupBtn}</span> </span>
           <!-- 
           <span data-dojo-attach-event="ondijitclick:_onDeleteSignalGroup" class="c3_deleteBtn c3_goals_cursor_pointer c3_goals_delete_group">
              <i class="fa-minus newGoalBtn"></i> 
           <span>${Res.deleteSignalGroupBtn}</span> </span>
            -->             
        </div>
	</div>
	</div>
	<div class="c3_col_right">
		<div class="c3_col_right-inner" data-dojo-attach-point="rightContent"></div>
	</div>
</div>
