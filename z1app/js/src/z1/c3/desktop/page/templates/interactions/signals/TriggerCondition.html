<div class="c3_TCond_box c3_TCond-off"
	data-category=${_category}
	data-dojo-attach-point="_cont">
	<div class="c3_TCond_boxIn c3_flex_1 c3_cols_nowrap_around c3_align_stretch">
		<div class="c3_TCond_eIcon c3_flex_center_1"
			data-dojo-attach-point="_editIconC">
			<i class="ti-pencil-alt" title="edit"></i>
		</div>
		<div class="c3_TCond_c1 c3_cols_nowrap c3_hide">
			<label for="${_chkId}" class="c3_TCond_lbl c3_flex_1 c3_flex_center_1 c3_btn">
				<input id="${_chkId}" type="checkbox" name="${_chkId}"
					data-dojo-attach-point="_tCheckBoxFld" />
			</label>
		</div>
		<div class="c3_TCond_c2 c3_flex_center_1">
			<div class="c3_TCond_descr">${description}</div>
		</div>
		<div class="c3_TCond_r2 c3_flex_center_1">
			<div class="c3_TCondToggle c3_btn fa-toggle-off" data-toggle="off"
				data-dojo-attach-point="_toggleBtn"></div>
		</div>
	</div>
</div>