<div class="c3_pgC c3_xpDash ${cls} c3_flex_1 c3_cols_nowrap c3_hidden"
     data-dojo-attach-point="_cont">
    <div class="c3_pgCH c3_rows_nowrap c3_align_center c3_bxOutline c3_bxWhite c3_pd_40h c3_pd_l-16 c3_br0imp">
        <div class="c3_pgCHC1 c3_flex_3 c3_rows_nowrap c3_align_center">
            <div class="c3_rows_nowrap c3_align_center c3_gpC16">
                <div class="c3_nvBackBtn c3_sq36 c3_br4 btn-default-outline2_new c3_btn c3_flex_center_1 c3-c-ic"
                     data-dojo-attach-point="_cancelBtn">
                    <svg class="c3i c3i_txt"><use class="c3i_u" xlink:href="#zi-arrowLeft"></use></svg>
                </div>
                <div class="c3_ttlEditC c3_cols_nowrap c3_hide1">
                    <div class="c3_rows_nowrap_start c3_align_center c3_gpC8">
                        <div class="c3_pgCTtl2x c3_fs24 c3_fw500 c3_hidden c3_text_ellipsis c3_lh32  c3_pd_4h c3_pd_16v" data-dojo-attach-point="_pgTitle"> ${Res.expLabel} ${_title}</div>
                        <div class="c3_xpMsgType"
                             data-dojo-attach-point="_msgTypeC"></div>
                    </div>
                </div>
            </div>
            <div class="c3_pgCHC2 c3_rows_nowrap c3_rows_spacing_x c3_align_center">
                <div class="btn-grp-fx">
                    <div class="c3_btn btn-fx btn-secondary-outline btn-flat btn-n"
                        data-dojo-attach-point="_viewEditExp">
                        <div class="btn-lbl btn-lbl-up">${Res.viewEditExp}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="c3_xpDash_sh c3_bxWhite_s c3_pd_24h c3_br0imp c3_rows_nowrap c3_align_center" data-dojo-attach-point="_subHeader">
        <div class="c3_tabsWrp c3_cols_nowrap c3_rows_nowrap_start c3_tab2HC"
             data-dojo-attach-point="_tabsCont">
            <div class="c3_tab2 c3_tab2Sel c3_btn c3_cols_nowrap" data-tab-num="1" data-dojo-attach-point="tab1">
                <div class="c3_tab2Bar"></div>
                <div class="c3_xpCrd c3_pd_12 c3_cols_nowrap" data-dojo-attach-point="_crd1C">
                    <div class="c3_chCrdTtl c3_fs14 c3_txt_c">${Res.tab1}</div>
                </div>
            </div>
            <div class="c3_tab2 c3_btn c3_cols_nowrap" data-tab-num="2" data-dojo-attach-point="tab2">
                <div class="c3_tab2Bar"></div>
                <div class="c3_xpCrd c3_pd_12 c3_cols_nowrap" data-dojo-attach-point="_crd2C">
                    <div class="c3_chCrdTtl c3_fs14 c3_txt_c">${Res.tab2}</div>
                </div>
            </div>
            <div class="c3_tab2 c3_btn c3_cols_nowrap c3_hide" data-tab-num="3" data-dojo-attach-point="tab3">
                <div class="c3_tab2Bar"></div>
                <div class="c3_xpCrd c3_pd_12 c3_cols_nowrap" data-dojo-attach-point="_crd3C">
                    <div class="c3_chCrdTtl c3_fs14 c3_txt_c">${Res.tab3}</div>
                </div>
            </div>
        </div>
        <div class="c3_pgCHC2 c3_breakdownFltrWrp c3_fltrBx2 c3_rows_nowrap c3_align_center c3_gpC10"
            data-dojo-attach-point="_rhsHCont">
            <div class="c3_hide" data-dojo-attach-point="_ChartMgrCont" title="${Res.selectMetrics}">
                <div class="c3_exportData_btn exportBtn c3_btn c3_fs19 btn-icn-sq btn-icn-sq-m"
                    data-dojo-attach-point="_ChartMgrBtn">
                    <svg class="c3i c3i_txt"><use xlink:href="#zi-showChartCtrl"/></svg>
                </div>
                <div class="c3_cols_nowrap c3_posR"
                    data-dojo-attach-point="_ChartMgrOptions">
                </div>
            </div>
            <div class="c3_hide c3_exportData_btn exportBtn c3_btn c3_fs19 btn-icn-sq btn-icn-sq-m"
                 title="${Res.exportCSV}"
                 data-dojo-attach-point="_exportBtn">
                <svg class="c3i c3i_txt"><use xlink:href="#zi-download"/></svg>
                <span></span>
                <a data-dojo-attach-point="_downloadBtn"></a>
            </div>
            <div class="c3_rows_nowrap c3_gp6" data-dojo-attach-point="_breakDownCont">
                <div class="c3_fltrBxLbl c3_flex_center_1 c3-c-secondary">${Res.breakDownBy}</div>
                <select data-dojo-attach-point="_breakDownFilter"
                            class="c3_breakdownFltr c3_fltrBxS2 c3_bdr_grey80 c3_flex_1 c3_html_fld c3_html_fld_h">
                    <option value="none" selected>${Res.none}</option>
                    <option value="channel">${Res.channel}</option>
                    <option value="identity">${Res.iden}</option>
                </select>
            </div>
            <div class="c3_xpTimeSel" data-dojo-attach-point="_timeSelCont"></div>
        </div>
        
    </div>
    <div class="c3_pgCB c3_flex_1 c3_cols_nowrap c3_y_auto"
         data-dojo-attach-point="_bCont">
        <div data-dojo-attach-point="_tabCont1" class="c3_xpD_tb"></div>
        <div data-dojo-attach-point="_tabCont2" class="c3_xpD_tb c3_pd_16 c3_hide"></div>
        <div data-dojo-attach-point="_tabCont3" class="c3_xpD_tb c3_pd_16 c3_hide"></div>
    </div>
</div>