<div
	class="c3_allLLinks configure-palette-container c3_child_window_padding c3_bxOutline c3_bxWhite c3_pd_24 configure-palette-container_segment c3_flex_1 c3_cols_nowrap c3_hidden"
	data-dojo-attach-point="configureSegmentPalettePage">
	<div class="c3_flex_1 c3_cols_nowrap c3_cols_spacing c3_flex_hidden c3_hidden" data-dojo-attach-point="livelinks">

		<div class="c3_pgCH c3_rows_nowrap c3_align_center c3_gp10" data-dojo-attach-point="_subHCont1">
			<div class="c3_align_center c3_rows_nowrap c3_flex_3 c3_pgCHC1">
				<div class="c3_pgCTtl" data-dojo-attach-point="_pgTitle">
					${Res.title}
					<span class="c3_pgCDscr">${Res.desc}</span>
				</div>
			</div>
			<div class="c3_pgCHC2 c3_rows_nowrap c3_rows_spacing_x c3_align_center">
				<div class="c3_btn btn-primary btn-n btn-fx" data-dojo-attach-point="addLinkBtn">
					<div class="btn-lbl btn-lbl-up">${Res.addLink}</div>
				</div>
			</div>
		</div>
		
		<div
				class="c3_allSignals_b c3_flex_1 c3_rows_nowrap c3_rows_spacing"
				data-dojo-attach-point="_b_container">
				<div class="c3_incEvtListWrp c3_flex_1 c3_cols_nowrap c3_y_auto">
					<div class="c3_incEvtListH c3_cols_nowrap"
						data-dojo-attach-point="eventListHeadContainer">
						<div class="c3_incEvtListIH c3_incEvtListI c3_rows_nowrap_start c3_align_stretch">
							<div class="c3_dataService_ListC1 c3_incEvtListC c3_flex_center_1 c3_txt_c">
								<div class="c3_incEvtListC1I c3_incEvtListCI">${Res.typeHead}</div>
							</div>
							<div class="c3_incEvtListC2 c3_incEvtListC c3_cols_nowrap c3_justify_center c3_flex_1 c3_txt_c">
								<div class="c3_incEvtListC2I c3_incEvtListCI">${Res.serviceHead}</div>
							</div>
							<div class="c3_incEvtListC5 c3_dataServiceListC5 c3_incEvtListC c3_flex_center_1 c3_txt_c">
								<div class="c3_incEvtListC5I c3_incEvtListCI">${Res.performanceHead}</div>
							</div>
							<div class="c3_incEvtListC6 c3_dataServiceListC6 c3_incEvtListC c3_flex_center_1 c3_txt_c">
								<div class="c3_incEvtListC6I c3_incEvtListCI">${Res.frequencyHead}</div>
							</div>
							<div class="c3_dataServiceListC4 c3_incEvtListC c3_flex_center_1 c3_txt_c">
								<div class="c3_incEvtListC4I c3_incEvtListCI">${Res.updatedHead}</div>
							</div>
							<div class="c3_dataService_ListC7 c3_incEvtListC c3_flex_center_1 c3_txt_c c3_incEvtListCLast" data-dojo-attach-point="delHead">
								<div class="c3_incEvtListC7I c3_incEvtListCI" >${Res.deleteHead}</div>
							</div>
						</div>
					</div>
					<div
						class="c3_incEvtListB c3_flex_1 c3_cols_nowrap"
						data-dojo-attach-point="eventListContOut">
						<div
							class="c3_incEvtListIB c3_flex_1 c3_cols_nowrap c3_dataServiceListIB"
							data-dojo-attach-point="eventListContainer"></div>
					</div>
				</div>
			</div>
			<div class="c3_gen_noData2 c3_hide" data-dojo-attach-point="noDataDiv"></div>
	</div>
</div>