<div class="c3_axnSentDtl c3_ttip c3_cols_nowrap c3_cols_spacing"
	data-dojo-attach-point="dialogBody">
	<header class="c3_dlg_head c3_rows_nowrap" data-dojo-attach-point='header'>
		<div class="c3_cj_edit_dlg_head_col c3_cj_edit_dlg_head_col1" data-dojo-attach-point='head_l'>
			<div class="c3_dlg2_title c3_dlg2_ttl2"
				data-dojo-attach-point='jDlgTitle'>${Res.dlgTitle}</div>
		</div>
		<div class="btn-no-outline c3_btn c3_dlg_closeBtn c3_flex_center_1 c3_fs10" 
			data-dojo-attach-point='btnClose'>
			<svg class="c3i c3i_txt" role="img" tabindex="-1"><use xlink:href="#x-close"></use></svg>
		</div>
	</header>
	<div class="c3_axnSentDtl_dlg_b c3_pd_24 c3_flex_1 c3_cols_nowrap c3_cols_spacing" data-dojo-attach-point='bodyCont'>
		<div class="c3_axnSentDtlC1 c3_cols_nowrap c3_cols_spacing_e"
			data-dojo-attach-point="_cpgnCont">
			<div class="c3_rows_nowrap c3_rows_spacing c3_align_center">
				<div class="c3_axnSentDtlN c3_flex_1">${_name}</div>
			</div>
		</div>
		<div></div>
		<div class="c3_axnSentDtlC2 c3_rows_nowrap_around c3_flex_eq c3_hide"
			data-dojo-attach-point="_statCont">
			<div class="c3_cols_nowrap c3_cols_spacing c3_align_center">
				<div class="c3_lbl c3_txt_c">${Res.targeted}</div>
				<div class="c3_axnSentDtlStV c3_txt_c">${_targeted}</div>
			</div>
			<div class="c3_cols_nowrap c3_cols_spacing c3_align_center">
				<div class="c3_lbl c3_txt_c">${Res.sent}</div>
				<div class="c3_axnSentDtlStV c3_txt_c">${_sent}</div>
			</div>
			<div class="c3_cols_nowrap c3_cols_spacing c3_align_center">
				<div class="c3_lbl c3_txt_c">${Res.reached}</div>
				<div class="c3_axnSentDtlStV c3_txt_c">${_reached}</div>
			</div>
			<div class="c3_cols_nowrap c3_cols_spacing c3_align_center">
				<div class="c3_lbl c3_txt_c">${Res.converted}</div>
				<div class="c3_axnSentDtlStV c3_txt_c">${_converted}</div>
			</div>
		</div>
		<div class="c3_axnSentDtlC3 c3_cols_nowrap c3_cols_spacing_e c3_y_auto"
			data-dojo-attach-point="_bCont"></div>
		<div class="c3_axnSentDtlRSum c3_pd_10 c3_pd-h_0 c3_fw600 c3_rows_nowrap c3_align_center"
			data-dojo-attach-point="_totalRCont">
			<div class="c3_asnrC1 c3_cols_nowrap">
				<div class="c3_axnSentDtlK">${Res.totalAxnsNotSent}</div>
			</div>
			<div class="c3_axnSentDtlV c3_txt_c"
				data-dojo-attach-point="_totalCont"></div>
		</div>
		<div class="c3_axnSentDtlDoc c3_txt_lnk c3_btn c3_hide"
			data-dojo-attach-point="_explanationLink">${Res.explanation}</div>
	</div>
	<footer class="btn-cont c3_dlg_f c3_pd_16">
		<div></div>
		<div 
			data-dojo-attach-point='rhsAxnsBtnCont'>
			<div class="btn-n btn-primary"
				tabindex="0"
				data-dojo-attach-point='btnOk'>
				<div class="btn-primary">${Res.ok}</div>
			</div>
		</div>
	</footer>
</div>