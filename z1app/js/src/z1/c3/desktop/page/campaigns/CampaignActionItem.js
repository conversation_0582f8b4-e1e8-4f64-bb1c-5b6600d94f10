/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define(
[
  "dojo/_base/declare", "dojo/_base/lang", "dojo/on", "udc/udc",
  "udc/core/commons/loader",
  "z1/c3/desktop/start",
  "dijit/registry", "udc/core/rt/widgets/_BaseWidget",
  "dojo/text!z1/c3/desktop/page/templates/campaigns/CampaignActionItem.html",
  "dojo/i18n!z1/c3/desktop/page/nls/CampaignActionItemRes",
  "z1/c3/desktop/data/C3Context",
  "z1/c3/desktop/data/CampaignCtx",
  "z1/common/C3Util",
  "z1/c3/desktop/page/C3DeleteDialogBox",
  "dojo/dom-style", "dojo/dom-construct",
  "dojo/dom-class",
  "dojo/Deferred",
  "dojo/promise/all"
], 
function(declare, lang, on, udc, loader, z1, registry, BaseWidget, templateStr, Res,
  C3Context, CampaignCtx, C3Util, DeleteDialogBox, style, domConstruct, domClass,
  Deferred, all
)
{

  /**
   * Creates a widget to show C3 landing page.
   */
  return declare(BaseWidget, {
    templateString: templateStr,
    Res: Res,
    _cb: null,
    _parent: null,
    _item: null,
    _delCb: null,

    /**
     * Creates an instance of this page.
     * 
     * @param params : {
     *     _dItem: array[item] - activities[_timerIndex] or timers[_timerIndex],
     *     actionList: activities[] or timers[],
     *     _state: journey states[i] item
     *     _timerIndex: index of item in activities[index] or timers[index]
     *     _actionIndex: index of item in
     *       activities[_timerIndex].actions[_actionIndex] or
     *       timers[_timerIndex].actions[_actionIndex]
     *     actionType: actionTypeTime|actionTypeActivity,
     *     _mode: edit|new
     *     _parent: parent widget/node
     * }
     * @param {Object}
     *          parentNode
     */
    constructor: function(params, parentNode)
    {
      this.inherited(arguments);
      //this._item = params.jItem;
      params._item = params._dItem;
      //this.name = this._item.name;
      //this.userCount = (this._item.userCount) ? this._item.userCount + Res.users : 0 + Res.users;

      // readOnlyMode (view only)
      // Add class to indicate read only mode 
      // campaign action is of (a) Time type and it was (b) Published before.
      params._readOnlyMode = false;
      if(params.actionType && params.actionType === CampaignCtx.consts.actionType.TIME && params.lastPublished !== void(0) && params.lastPublished != -1)
      {
        // Read only
        params._readOnlyMode = true;
      }
    },

    postCreate: function()
    {
      this.inherited(arguments);

    },

    // .....................................................
    startup: function()
    {
      this.inherited(arguments);
      // this.jSeg_container.setAttribute("id", item.id);
      if(this._readOnlyMode)
      {
        domClass.add(this.cont, "c3_cpgn_axn_v_box_readOnly");
      }
      this._setCompositeABFlags();
      this._getActionContentString().then(lang.hitch(this, function(){
        this.populate();
        this._setEvents();
      }));
    },

    //............................
    populate: function()
    {
      var n = "..";
      if(this._dItem && this._dItem.actions && this._dItem.actions[this._actionIndex] && this._dItem.actions[this._actionIndex].name)
      {
        n = this._dItem.actions[this._actionIndex].name;
      }
      if (this.actionType === CampaignCtx.consts.actionType.TIME)
      {
        if(this._dItem)
        {
          var s = "";
          var v = this._dItem.value;
          var timeStr;
          var arTime;
          if(this._dItem.unit && this._dItem.unit.search(/days|hours|minutes|seconds/i) === 0)
          {
            if(this._dItem.value && isNaN(this._dItem.value) && this._dItem.value.indexOf("|")  && this._dItem.unit.search(/days/i) === 0)
            {
              // days
              arTime = this._dItem.value.split("|");
              if(arTime && arTime.length === 2)
              {
                s = arTime[0] + " " + this._dItem.unit + " at " + this._formatTimeControlForDisp(arTime[1]);
              }
            }
            else if(this._dItem.value)
            {
              // minutes or hour
              s = this._dItem.value + " " + this._dItem.unit;
            }
            s = "After " + this.getTimeHTML(s);
          }
          else if(this._dItem.unit && this._dItem.unit.toLowerCase() === "on")
          {
            var dt = this._dItem.value;
            var dtFromUTC;
            if(dt && dt.length >= 12)
            {
              // convert UTC to local date
              // Commentout UTC conversion is handled in backend.
              //var dtFromUTC = C3Util.yyyymmddhhmmFromUTC(dt);
              dtFromUTC = dt;

              // set format 'yyyy-mm-dd'

              s = dtFromUTC.substring(0, 4) + "-" + dtFromUTC.substring(4, 6) + "-" + dtFromUTC.substring(6, 8);
            }
            if(dt && dt.length === 12)
            {
              s = s + " at " + this._formatTimeControlForDisp(dtFromUTC.substring(8, 12));
            }
            else if(dt && dt.length > 12)
            {
              // yyyymmdd0000|best
              s = s + " at " + Res.bestTimeStr;
            }
            s = "On " + this.getTimeHTML(s);
          }
          else if(this._dItem.unit && this._dItem.unit.toLowerCase() === "weekdays")
          {
            var weekDaysString = this._dItem.value;
            if(!weekDaysString)
            {
              weekDaysString = "";
            }
            var tStr;
            if(weekDaysString.indexOf("|"))
            {
              // time part |1830 or |best
              tStr = this._formatTimeControlForDisp(weekDaysString.substring(weekDaysString.indexOf("|") + 1));
            }
            // remove |1830 time part
            var wDayStr = weekDaysString.substring(0, weekDaysString.indexOf("|"));
            if(wDayStr !== void(0))
            {
              var arWeek = ["Sunday", "Monday", "Tueday", "Wednesday", "Thursday", "Friday", "Saturday"];
              var arDays = wDayStr.split(",");
              for(var i = 0; i < arDays.length; i++)
              {
                var weekDay = arDays[i];
                if(!isNaN(weekDay) && weekDay >= 0 && weekDay <= 6)
                {
                  s = s + " " + this.getTimeHTML(arWeek[weekDay]);
                }
              }
            }
            if(tStr)
            {
              s = s + " at " + tStr;
            }
            s = "On every" + s;
          }
          else
          {
            s = this._dItem.value;
          }
          if (this._usedIn && this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGNONCE)
          {
            // one time campaign - no need to show 'on ,"
            this.titleCont.innerHTML = "send " + this.getActionHTML(n);
          }
          else
          {
            this.titleCont.innerHTML = s + ", send " + this.getActionHTML(n);
          }
        }
      }
      else if (this.actionType === CampaignCtx.consts.actionType.ACTIVITY)
      {
        if(this._dItem)
        {
          this.fetchAllSignalsData({n: n});
        }
      }
    },

    //............................
    getTimeHTML: function(v)
    {
      return '<span class="c3_cpgn_run_item_val c3_cpgn_time_val">' + v + '</span>';
    },

    //............................
    getActionHTML: function(v)
    {
      return '<span class="c3_cpgn_run_item_val c3_cpgn_send_axn_val">' + v + '</span>' + this._contentString;
    },

    //................................................
    // Formated 15:30 Or 'best'
    _formatTimeControlForDisp: function(timeStr)
    {
      if(timeStr === "best")
      {
        return Res.bestTimeStr;
      }
      else if(timeStr.length)
      {
        // get time |HHMM part
        var ampm = "AM";
        var hh = "00";
        var mm = "00";
        hh = timeStr.substring(0, 2);
        mm = timeStr.substring(2, 4);

        if(Number(hh) >= 12)
        {
          ampm = "PM";
          if(Number(hh) !== 12)
          {
            hh = Number(hh) - 12;
          }
        }
        else
        {
          ampm = "AM";
        }
        if(Number(hh) < 10 && hh !== "00")
        {
          hh = "0" + Number(hh) + "";
        }
        // if hh is '00' then in display show it
        // as '12' irrespective of am/pm
        if(Number(hh) === 0)
        {
          hh = 12;
        }
        return hh + ":" + mm + " " + ampm;
      }
      return;
    },

    //...........................................
    fetchAllSignalsData: function(ctx)
    {
      C3Context.goalData.fetchSignalsPaletteData({
        //container: dojo.query('.c3_col_right-inner')[0],
        jsonUrl: "c3/data/signals/all",
        showPalette: lang.hitch(this, this.showActivityAction, ctx)
      });
    },

    //....................................
    showActivityAction: function(misc, ctx)
    {
      // this._dItem.name will be @deprecated
      //var eName = this._dItem.name;
      var eName;
      if(!ctx || (ctx.status && ctx.status === "fail"))
      {
        udc.log(ctx);
        if(eName)
        {
        }
        return;
      }

      // obj to array
      var items = [];
      for(var k in ctx)
      {
        if( ctx.hasOwnProperty(k) && ctx[k] && ctx[k].name)
        {
          //items.push({name: k, value: ctx[k]});
          items.push({name: ctx[k].name, value: ctx[k]});
        }
      }

      // udc.log("items ", items);
      if(!items || items.length === 0)
      {
        udc.log("Event list is empty ", items);
        if(eName)
        {
        }
        return;
      }

      // data for combobox
      var selectedIndex = 0;
      var data = [];
      //// blank row
      //data.push({id: "", name: Res.events});
      var fMatch = false;
      for (var i = 0; i < items.length; i++)
      {
        //udc.log(items[i].name + " =? " + eName.toLowerCase());
        if(this._dItem.ref && items[i].value && items[i].value.id === this._dItem.ref)
        {
          eName = items[i].name;
          break;
        }
      }

      // this.titleCont.innerHTML = "When " + this.getTimeHTML(eName) + ", send " + this.getActionHTML(misc.n);
      this.titleCont.innerHTML = "Send " + this.getActionHTML(misc.n);
    },

    //.....................................................
    // Get selected Action short name e.g. 'push', 'banner'
    getEntype: function()
    {
      // Note: Here 'id' is not available. 'id' and 'ref' values are 
      //       different. Entype map corresponds to 'id' values.
      //       Derive partial 'id' from 'ref'.
      var aRef = "";
      var wName = "";
      if(this._dItem && this._dItem.actions && this._dItem.actions[this._actionIndex] && this._dItem.actions[this._actionIndex].ref)
      {
        aRef = this._dItem.actions[this._actionIndex].ref;
        wName = aRef.substring(aRef.lastIndexOf(":") + 1);
      }
      // Note: using actionRefTypeMap instead of actionIdTypeMap
      var entypeMap = CampaignCtx.jData.actionRefTypeMap;
      var entype = "push";
      if(aRef && entypeMap)
      {
        var kRef;
        for(var k in entypeMap)
        {
          if (entypeMap.hasOwnProperty(k))
          {
            kRef = k.split(":");
            if(!kRef || !kRef.length) continue;
            if(wName === kRef[kRef.length - 1])
            {
              entype = entypeMap[k];
              return entype;
            }
          }
        }
        udc.log("Not found entype for ref : " + aRef);
      }
      return entype;
    },

    //......................................................
    // case 1: A/B tags, return " with tags [tag1, tag2, ..]"
    // case 2: no A/B tags; content ref selected,
    //         return " with content [name of content]"
    // case 3: empty both A/B and z1_ref are empty, return ""
    // case 4: List Of Articles - corresponding 'tags'.
    // case 5: RawData, return ""
    _getActionContentString: function()
    {
      var deferr = new Deferred();

      this._contentString = "";
      var a;
      if(this._dItem && this._dItem.actions 
          && this._dItem.actions.length
          && this._dItem.actions[this._actionIndex])
      {
        a = this._dItem.actions[this._actionIndex];
      }

      // check if case 1 or case 2
      var _case = 1;
      var flgTags = false;
      var z1tags;
      var z1ref;
      // 'list of artcles' tags
      var loaTags = "";
      if(a && a.params && a.params.length)
      {
        for(var i = 0; i < a.params.length; i++)
        {
          if(a.params[i] && a.params[i].name)
          {
            if(a.params[i].name === "rawData")
            {
              // raw data is sent, no AB or linked content
              // case 5
              _case = 5;
            }
            if(a.params[i].name === "listTitle")
            {
              // "List of Articles" is sent
              // another params 'tags' contain the tag names
              // case 4
              _case = 4;
            }
            if(a.params[i].name === "tags")
            {
              if(typeof(a.params[i].value) === 'string' && a.params[i].value !== "")
              {
                // case 4
                _case = 4;
                loaTags = a.params[i].value;
              }
            }
            if(a.params[i].name === "z1_tags")
            {
              if(typeof(a.params[i].value) === 'string' && a.params[i].value !== "")
              {
                // case 1
                _case = 1;
                z1tags = a.params[i].value;
              }
            }
            if(a.params[i].name === "z1_isAB")
            {
              // value = "true" | "false"
              // ~ using z1_tags and z1_ref combination instead.
            }
            if(a.params[i].name === "z1_ref")
            {
              if(typeof(a.params[i].value) === 'string' && a.params[i].value !== "")
              {
                // case 2
                _case = 2;
                z1ref = a.params[i].value;
              }
            }
          }
        }
      }
      if(_case === 4)
      {
        // List Of Articles
        _case = 1;
        if(loaTags && loaTags.length)
        {
          this._contentString = Res.withTags + '<span class="c3_cpgn_run_item_val c3_cpgn_send_axn_content">' + loaTags.split(",").join(", ") + '</span>';
        }
        else
        {
          this._contentString = "";
        }
        deferr.resolve({
          pass: true,
          contentString: this._contentString
        });
        return deferr.promise;
      }
      else if(_case === 5)
      {
        // Raw Data
        _case = 5;
        this._contentString = "";
        deferr.resolve({
          pass: true,
          contentString: this._contentString
        });
        return deferr.promise;
      }
      else if(!z1tags && !z1ref)
      {
        _case = 3;
        this._contentString = "";
        deferr.resolve({
          pass: true,
          contentString: this._contentString
        });
        return deferr.promise;
      }
      else if(z1tags && z1tags.length)
      {
        // Tags
        _case = 1;
        this._contentString = Res.withTags + '<span class="c3_cpgn_run_item_val c3_cpgn_send_axn_content">' + z1tags.split(",").join(", ") + '</span>';
        deferr.resolve({
          pass: true,
          contentString: this._contentString
        });
        return deferr.promise;
      }
      else if(!z1tags && z1ref && z1ref.length)
      {
        // Content
        _case = 2;

        // fetch content title n format
        var enType = this.getEntype();
        var targetUrl = "/c3/data/kb/content?topK=-1&entype=" + enType;
        loader.GET(targetUrl, {}).then(lang.hitch(this, function(data)
        {
          if (data && data.status && data.status === "fail")
          {
            udc.log("failed: ", data);
          }
          if (data && data.length)
          {
            for(var i = 0; i < data.length; i++)
            {
              if(data[i] && data[i].contentId === z1ref)
              {
                this._contentString = Res.withContent + '<span class="c3_cpgn_run_item_val c3_cpgn_send_axn_content">' + data[i].title + '</span>';
                break;
              }
            }
          }
          deferr.resolve({
            pass: true,
            contentString: this._contentString
          });
        }), lang.hitch(this, function(error)
        {
          udc.info(error);
          deferr.resolve({
            pass: true,
            contentString: this._contentString
          });
        }));
        return deferr.promise;
      }

      return deferr.promise;
    },

    //....................................
    _setCompositeABFlags: function()
    {
      if (this._dItem && this._dItem.actions && this._dItem.actions[this._actionIndex]
        && this._dItem.actions[this._actionIndex].ref === CampaignCtx.consts.compositeABRef)
      {
        this._isABCompositeAction = true;
      }
      else
      {
        this._isABCompositeAction = false;
      }
    },


    /**
     * On any segment selected, capture the item details and call the parent
     */
    //......................................
    // edit item
    _onEdit: function()
    {
      //this.getCallback().call(this.getParent(), this._item);
      this._parent.editActionItem({
        _dItem: this._dItem,
        actionList: this.actionList,
        _timerIndex: this._timerIndex,
        _actionIndex: this._actionIndex,
        _state: this._state,
        _isABCompositeAction: (this._isABCompositeAction)? true: false,
        actionType: this.actionType,
        _mode: "edit"
      });
    },

    //....................................
    _onDelete: function()
    {
      var n = "this item";
      if(this._dItem && this._dItem.actions && this._dItem.actions[this._actionIndex] && this._dItem.actions[this._actionIndex].name)
      {
        n = this._dItem.actions[this._actionIndex].name;
      }

      var delCallback = function()
      {
        this._delete();
      };

      var dialogParams = {
          "dialog_title": Res.dlgDeleteTitle,
          "dialog_msg": Res.dlgDeleteMsg + " " + n + "?"
      };
      var delDialog = new DeleteDialogBox(dialogParams);
      delDialog.startup();
      delDialog.setParent(this);
      delDialog.setCallback(delCallback);
      this.own(delDialog);
    },

    //...............................
    _delete: function()
    {
      //this.getDeleteCallback().call(this.getParent(), this._item);
      //this._parent.deleteActionItem({
      this._parent.deleteItem({
        _dItem: this._dItem,
        actionList: this.actionList,
        _timerIndex: this._timerIndex,
        _actionIndex: this._actionIndex,
        _state: this._state,
        _isABCompositeAction: (this._isABCompositeAction)? true: false,
        actionType: this.actionType,
        _mode: "delete"
      });
    },

    //...............................
    setParent: function(parent)
    {
      this._parent = parent;
    },

    getParent: function()
    {
      return this._parent;
    },

    setCallback: function(cb)
    {
      this._cb = cb;
    },

    getCallback: function()
    {
      return this._cb;
    },

    setDeleteCallback: function(cb)
    {
      this._delCb = cb;
    },

    getDeleteCallback: function()
    {
      return this._delCb;
    },

    // ..........................
    onClose: function()
    {
      // widget is closed.
      // ~
    },

    //..........................
    _setEvents: function()
    {
      this.own(
        // edit
        on(this.itemCont, "click", lang.hitch(this, function(){
          this._onEdit();
        })),

        // delete
        on(this.delBtn, "click", lang.hitch(this, function(){
          this._onDelete();
        }))
      );
    },

    // .........................
    destroy: function()
    {
      this.inherited(arguments);
    },

    _end: 0
  });

});
