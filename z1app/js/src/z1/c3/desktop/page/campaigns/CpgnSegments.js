/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define([
  "dojo/_base/declare", 
  "dojo/_base/lang",
  "udc/core/commons/loader",
  "dojo/on",
  "dojo/dom-class",
  "udc/udc",
  "z1/c3/desktop/start",
  "udc/core/rt/widgets/_ComplexBaseWidget",
  "dojo/text!z1/c3/desktop/page/templates/campaigns/CpgnSegments.html",
  "dojo/i18n!z1/c3/desktop/page/nls/CpgnSegmentsRes",
  "z1/c3/desktop/page/goals/PaletteBoxItem",
  "z1/c3/desktop/page/fields/C3ToggleBtn",
  "z1/c3/desktop/page/campaigns/SelectSegment",
  "z1/c3/desktop/page/campaigns/CpgnAddSegments",
  "z1/c3/desktop/page/campaigns/CampaignSegmentItem",
  "z1/c3/desktop/page/C3DeleteDialogBox",
  "z1/c3/desktop/page/C3AlertBox",
  "z1/common/C3Util",
  "z1/c3/desktop/data/C3Context",
  "z1/c3/desktop/data/C3JourneyCtx",
  "z1/c3/desktop/data/CampaignCtx",
  "z1/c3/desktop/data/conversionSeg/ConvSegData",
  "dijit/Dialog",
  "dojo/topic",
  "dojo/dom-construct",
  "dojo/Deferred",
  "dojo/promise/all"
],
function(
  declare, lang, loader, on, domClass, udc, z1, BaseWidget, templateStr, Res,
  PaletteBoxItem,
  C3ToggleBtn, SelectSegment, CpgnAddSegments, CampaignSegmentItem,
  DeleteDialogBox, AlertBox, C3Util, C3Context, C3JourneyCtx, CampaignCtx,
  ConvSegData, Dialog,
  topic, domConstruct, Deferred, all
)
{

  /**
   * Creates a widget to show journey timeline page.
   */
  return declare(BaseWidget,
  {
    templateString : templateStr,
    Res: Res,

    Screen: {
      SELECT_ACTION_TYPE: 0,
      SELECT_ACTION: 1,
      EDIT_ACTION: 2,
      TIME_CONSTRAINT: 3,
      ACTION_TAGS: 4,
      SELECT_ACTION_CONTENT: 5
    },

    _dItem: null,

    Fields : {
      status: {
        PUBLISHED: "published",
        UNPUBLISHED: "unpublished"
      },
      bestTime: "best"
    },

    /**
     * Creates an instance of this page.
     * params holds the flags and data which is automatically
     * placed in the DOM node.
     * params._dItem item data
     * @param {Object}
     * 
     * Sets fields:
     * - Segments ref: "ref1,ref2,..,refn"
     * 
     * API:
     *   Parent widget must implement methods:
     *   saveItem(ctx)
     *   deleteItem(ctx)
     */
    constructor : function(params)
    {
      this.inherited(arguments);
      params = params || {};
      params._jItem = params._jItem || {};
      params._dItem = params._dItem || {};

      // in-line | dialog
      params._showInline = (typeof params._showInline === 'boolean')
        ? params._showInline : true;

      //this._parent = params._parent;

      // MODE : edit|new
      // this._mode = edit | new

      // WIDGET USED IN
      // pass '_usedIn' : campaign|journey|..
      if(!params._usedIn)
      {
        // default - campaign
        params._usedIn = (CampaignCtx.jData._urlComponent)
          ? CampaignCtx.jData._urlComponent
          : CampaignCtx.consts.usedIn.CAMPAIGN;
      }

      if(!params._idSfx)
      {
        params._idSfx = "_eca_" + Math.ceil(Math.random()*1000) + "_eca_";
      }

      // "All Users" - pass 'hideAllUsers: true' to not show All Users segment

      // Use appropriate rosource strings, based on campaign/interactions
      params.selectSegmentTitle = Res["selectSegmentTitle_" + params._usedIn];

      // artifact segment has changeable:false. [default: true]
      params.isChangeable = (typeof params.isChangeable === "boolean") ? params.isChangeable : true;

      // readOnlyMode (view only)
      // Save and Update operations are not allowed on Action if the 
      // campaign action is of (a) Time type and it was (b) Published before.
      params._readOnlyMode = false;
      if(params.actionType && params.actionType === CampaignCtx.consts.actionType.TIME && params.lastPublished !== void(0) && params.lastPublished != -1)
      {
        // Read only
        params._readOnlyMode = true;
      }

      this._jItems = [];
      this._segWidget = [];
    },

    // ////////////////////////////////////////////////////////////////////////////
    // Implementations

    postCreate : function()
    {
      this.inherited(arguments);

    },

    // .....................................................
    startup : function()
    {
      this.inherited(arguments);

      if(!this._showInline)
      {
        //..
        // SHOW NORMAL POP UP DIALOG
        this.c3Dialog = new Dialog({
          title: this.dialog_title,
          //style: "min-width: 500px;min-height:300px;top:100px;",
          style: "min-width: 500px;min-height:300px;",
          hide: lang.hitch(this, function(){
            if(this.c3Dialog){
              this.c3Dialog.destroy();
            }
          }),
          class: "c3_dialog_box c3_dialog_box_noTitleBar c3_cols_nowrap"
        });
        dojo.place(this.dialogBody, this.c3Dialog.containerNode);
        this.own(this.c3Dialog);
        
        this.c3Dialog.show();
        //..
      }
      if(this._showInline)
      {
        // add class to change UI to inline
        this.dialogBody.classList.add("c3_dlg_inline");
        this.dialogBody.classList.add("c3_flex_1");
        // when in in-line mode, hide all elements
        // that have class 'c3_dlg_only'
        this.dialogBody.querySelectorAll(".c3_dlg_only").forEach((el) => {
          // either hide it or delete the element
          el.classList.add("c3_hide");
        });
      }

      this.populateData();

      this._setEvents();
      this._subscribe();
    },

    //.................................................
    // - Not used at widget creation. Data is passed
    //   in constructor.
    // - Can be used later on to change data of widget.
    setData: function(data)
    {
      this._dItem = data;
      this.populateData();
    },

    //...........................
    getParent: function()
    {
      return this._parent;
    },

    //.......................................................
    // Get value of z1_isAB
    getData: function()
    {
      return "";
    },

    //...............................
    populateData: function()
    {
      this.markSegmentsChangeable();
      this._prepareWhoForUI();
      this._showAllUsers();
    },

    //...........................
    validate: function(data)
    {
      var data = data || this.getData();

      // ~

      return true;
    },

    // ........................................
    // Create UI for segment box - campaign
    // targets item
    _createSegmentItem: function(item)
    {
      var sCtx = {
        _parent: this,
        jItem: item
      };
      if (!this.isChangeable) sCtx.readOnly = true;
      var segment = new CampaignSegmentItem(sCtx);
      segment.setParent(this);
      // segment.setCallback(this._viewJourneySegmentTimeline);
      segment.setDeleteCallback(this._onSegmentDeleted);
      // // if its the 5th and last element dont show the arrow icon. its misleading
      // if (this._jItems.length == 5) segment.showArrowIcon(false);
      segment.startup();
      segment.domNode.dataset.ref = item.id;
      domConstruct.place(segment.domNode, this.addSegmentBtn, "before");
      this.own(segment);
      this._segWidget.push(segment);
    },

    // .........................................
    _onSegmentDeleted: function(item)
    {
      var payload = this.getContext().payload;
      var refList = CampaignCtx.jData.getCpgnStateSegmentRefList(payload.states[0]);
      if (refList && refList.length === 1)
      {
        // // If only one segment is present then
        // // deleting it will also delete the State.
        // // User should add another segment and then delete
        // // this one.
        // var alertParams = {
        // "dialog_title": Res.dlgDeleteSegTitle,
        // "dialog_msg": Res.alertOneTargetGroupRequired
        // };
        // var alertDialog = new AlertBox(alertParams);
        // alertDialog.startup();
        // alertDialog.setParent(this);
        // this.own(alertDialog);
        // return;
      }

      var delCallback = function()
      {
        this._deleteJourneySegment(item);
      };

      var dialogParams = {
        "dialog_title": Res.dlgDeleteSegTitle,
        "dialog_msg": Res.dlgDeleteSegMsg + " '" + item.name + "'?"
      // "dialog_msg": "Delete " + "'" + item.name + "'?"
      };
      var dialog = new DeleteDialogBox(dialogParams);
      dialog.startup();
      dialog.setParent(this);
      dialog.setCallback(delCallback);
    },

    /**
     * Callback when the segment(journey state) is being deleted . Update the local array. Show the
     * add segment button if the journey states are less than 5. destroy the exisiting widgets and
     * re-create new ones update the JCtx and save to backend
     */
    _deleteJourneySegment: function(item)
    {
      var deferr = new Deferred();
      if (!item || !item.id)
      {
        deferr.reject(false);
        return deferr.promise;
      }
      var payload = this.getContext().payload;
      CampaignCtx.jData.deleteCpgnStateSegmentRef(payload.states[0], item.id);
      CampaignCtx.jData.setJourneyComplete(false);
      // Save
      CampaignCtx.jData.saveData(true).then(lang.hitch(this, function(data)
      {
        if (data && data.status === "fail")
        {
          var msg = "";
          if (data.data && data.data.reason)
          {
            msg = data.data.reason;
          }
          udc.error("save failed. " + msg);
          udc.log(data);
        }
        // remove segment data item from array
        if (this._jItems && this._jItems.length > 0)
        {
          for (var i = (this._jItems.length - 1); i > -1; i--)
          {
            if (this._jItems[i].id === item.id)
            {
              var sItem = this._jItems[i];
              this._jItems.splice(i, 1);
              break;
            }
          }
        }

        this.destroySegments();

        // reload - segments
        this.populateData();

        deferr.resolve(item);
      }), lang.hitch(this, function(error)
      {
        udc.error("save failed. ");
        udc.log(error);
        deferr.reject(error);
      }));
      return deferr.promise;
    },

    /**
     * Hide and show add btn which allows users to add segments(journey states) Dont allow more than
     * 5 segments to be added.
     */
    _toggleAddSegBtn: function()
    {
      if (this._jItems.length == 5)
      {
        domClass.add(this.addSegmentBtn, "c3_hide");
      }
      else
      {
        domClass.remove(this.addSegmentBtn, "c3_hide");
      }
    },

    // .......................................
    _addSegments: function()
    {
      let refList = [];
      let refListFinal = [];
      let filterList = [];
      var payload = this.getContext().payload;
      if (payload && payload.states && payload.states.length)
      {
        refList = CampaignCtx.jData.getCpgnStateSegmentRefList(payload.states[0]);

        // Check that the Segment does not exist in Final Target segments.
        if(payload && payload.states[1])
        {
          refListFinal = CampaignCtx.jData.getCpgnStateSegmentRefList(payload.states[1]);
        }
      }

      filterList = refList.concat(refListFinal);

      var cpgnAddSegments = new CpgnAddSegments({
        _parent: this,
        dialog_title: Res.selectSegmentDlgTtl,
        // target segments
        refList: refList,
        // final segments
        refListFinal: refListFinal,
        // do not show items in this list.
        filterList: filterList,
        // used for - "target"|"final" segments
        // used for "target"|"final"
        _usedFor: "target",
        _usedIn: this._usedIn,
        _showInline: false,
        // TI - All Users is controlled by the toggle button
        // so do not show it in segment selection list for TI
        // SI - false
        hideAllUsers: true,
        callbackSaveFn: lang.hitch(this, "_segmentCreatedCB"),
        _onSegmentSelected: lang.hitch(this, "_onSegmentSelected"),
        onSegmentRemoved: lang.hitch(this, "_deleteJourneySegment")
      });
      cpgnAddSegments.startup();
      this.own(cpgnAddSegments);
    },

    //..................................
    _segmentCreatedCB: function (segCtx = {resp: {}})
    {
      var deferr = new Deferred();
      if (segCtx.resp.status !== "success" || !segCtx.data || !segCtx.resp.id)
      {
        if (segCtx.resp.message)
        {
          udc.info(segCtx.resp.message);
        }
        udc.log("Failed to create segment");
        deferr.reject(segCtx.resp);
        return deferr.promise;
      }

      var segItem = Object.assign({}, JSON.parse(JSON.stringify(segCtx.data)), {id: segCtx.resp.id});
      // add segment to campaign and save
      this._onSegmentSelected({item: segItem}).then(lang.hitch(this, function(){
        deferr.resolve(true);
      }));
      return deferr.promise;
    },
    
    // .......................................
    // if this.skipPopup = true, this method is used after clicking the
    // "+" button instead of the _addSegments method.
    _selectSegment: function()
    {
      // used for "target"|"final"
      this._usedFor = this._usedFor || "target";
      let refList = [];
      let refListFinal = [];
      let filterList = [];
      let d = CampaignCtx.jData._jItem;
      let payload;
      if(d && d.payload && d.payload.states && d.payload.states.length)
      {
        payload = d.payload;
        refList = CampaignCtx.jData.getCpgnStateSegmentRefList(payload.states[0]);

        // Check that the Segment does not exist in Final Target segments.
        if(payload && payload.states[1])
        {
          refListFinal = CampaignCtx.jData.getCpgnStateSegmentRefList(payload.states[1]);
        }
      }

      refList.push("system:allUsers");

      filterList = refList.concat(refListFinal);

      var selectSegmentDlg = new SelectSegment({
        _parent: this,
        _usedIn: (this._usedIn) ? this._usedIn : CampaignCtx.consts.usedIn.CAMPAIGN,
        dialog_title: Res["selectSegmentDlgTtl" + this._usedFor] || Res.selectSegmentDlgTtl2,
        // target segments
        refList: refList,
        // final segments
        refListFinal: refListFinal,
        // do not show items in this list.
        filterList: filterList,
        // used for - "target"|"final" segments
        _usedFor: this._usedFor,
        _cb: lang.hitch(this, "_onSegmentSelected"),
        callbackSaveFn: lang.hitch(this, "_segmentCreatedCB"),
        _onSegmentSelected: lang.hitch(this, "_onSegmentSelected"),
        onSegmentRemoved: lang.hitch(this, "_deleteJourneySegment")
      });
      selectSegmentDlg.startup();
      this.own(selectSegmentDlg);
    },

    // ........................................
    // Callback - segment selected in dialog
    // Add it to the goal.
    _onSegmentSelected: function(ctx)
    {
      var deferr = new Deferred();
      // 1. State exists.
      // 2. Add Segment to Customer Group
      // this._segmentGroup.segments.push(ctx.item.id);
      let d = CampaignCtx.jData._jItem;
      if(!d || !d.payload) return;
      let payload = d.payload;
      if (!payload.states || !payload.states.length)
      {
        // create state
        CampaignCtx.jData.createJourneyState(ctx.item);
      }
      else
      {
        CampaignCtx.jData.addCpgnStateSegmentRef(payload.states[0], ctx.item.id, this._usedForExp);
      }

      // 3. show in UI
      this.populateData();
      // 4. Save data
//      this._showPublishBtn(true);
      // CampaignCtx.jData.setGoalComplete(false);
      CampaignCtx.jData.saveData().then(lang.hitch(this, function(data)
      {
        if (data && data.status === "fail")
        {
          var msg = "";
          if (data.data && data.data.reason)
          {
            msg = data.data.reason;
          }
          udc.error("save failed. " + msg);
          udc.log(data);
        }

//        // Update Journey data
//        // CampaignCtx.jData.updateWhoItem(this._segmentGroup);
//        this._togglePublishBtn();
//
//        //this._jItems.push(ctx.item);
//        // if (ctx.mode === "create")
//        // {
//        // this._showJourneyStats("create");
//        // }
//        // Update - stats.
//        this._setStatesContent();
        deferr.resolve(true);
      }), lang.hitch(this, function(error)
      {
        udc.error("save failed. ");
        udc.log(error);
        deferr.reject(error);
      }));

      return deferr.promise;
    },

    // .............................................
    _showAddSegBtn: function(showIt)
    {
      if (showIt)
      {
        domClass.remove(this.addSegmentBtn, "c3_hide");
      }
      else
      {
        domClass.add(this.addSegmentBtn, "c3_hide");
      }

      this._toggleAllUsers(!showIt);
    },

    // .............................................
    _toggleAllUsers: function(turnOn)
    {
      this._showAllUsers();
    },

    // .............................................
    _showAllUsers: function(turnOn)
    {
      let hasAllUsers = true;
      let jData = CampaignCtx.jData._jItem;
      let state;
      if (!jData || !jData.payload || !jData.payload.states)
      {
        hasAllUsers = false;
      }
      else
      {
        // Assumption: In Campaign there is only 1 state
        state = jData.payload.states[0];
        hasAllUsers = CampaignCtx.jData.containsCpgnStateSegmentRef(state, CampaignCtx.jData.allUsers.id);
      }

      domConstruct.empty(this._allUsersCont);
      if (this.toggleAllUsers && typeof this.toggleAllUsers.destroy === 'function')
      {
        this.toggleAllUsers.destroy();
      }

      if (this.hideToggleButton)
      {
        domClass.add(this._allUsersCont, "c3_hide");
        domClass.remove(this.addSegmentBtn, "c3_hide");
      }
      else
      {
        this.toggleAllUsers = new C3ToggleBtn({
          _parent: this,
          _description: Res.targetSpecificGroup,
          cls: "c3_cpgnSegAllUsersBtn",
          value: !hasAllUsers,
          parentCallback: "_hasAllUsersChanged"
        });
        this.toggleAllUsers.startup();
        domConstruct.place(this.toggleAllUsers.domNode, this._allUsersCont, "only");
        this.own(this.toggleAllUsers);
      }
    },

    //.......................................
    _hasAllUsersChanged: function (v)
    {
      // add/remove All Users based on change.
      if (v)
      {
        // remove
        // all users should be the only segment in list
        if (this._segmentList && this._segmentList[0]
        && this._segmentList[0].id === CampaignCtx.jData.allUsers.id){
          this._deleteJourneySegment(this._segmentList[0]);
        }
      }
      else
      {
        // add
        this._onSegmentSelected({item: CampaignCtx.jData.allUsers});
      }
    },

    // .................................
    _prepareWhoForUI: function()
    {
      let jData = CampaignCtx.jData._jItem;
      let state;
      let refList = [];
      if (!jData || !jData.payload || !jData.payload.states)
      {
        return;
      }
      if (!Array.isArray(jData.payload.states))
      {
        return;
      }
      // Assumption: In Campaign there is only 1 state
      state = jData.payload.states[0];
      refList = CampaignCtx.jData.getCpgnStateSegmentRefList(state);

      // reset palette list
      this.destroySegments();
      this._segmentList = [];
      this._showAddSegBtn(true);

      for (var i = 0; i < refList.length; i++)
      {
        // Copy matched Segment palette item data
        // to _segmentList []

        var seg;
        if (refList[i] === CampaignCtx.jData.allUsers.id)
        {
          seg = C3Context.goalData.copyJObj(CampaignCtx.jData.allUsers);
          // If 'All Users' is added then no need to add other segments
          // Hide Add-Segment(+) button.
          this._showAddSegBtn(false);
        }
        else
        {
          seg = C3Context.goalData.getPaletteItemById(refList[i]) || {
            id: refList[i]
          };
          // segments packaged in module may not
          // have corresponding segment def in all segments
          // list. Extract name from the segment id field
          if (seg.name === void(0) && seg.id.includes(":"))
          {
            let arSegId = seg.id.split(":");
            if (arSegId.length > 1)
            {
              seg.name = arSegId[1];
            }
          }
        }
        this._segmentList.push(C3Context.goalData.copyJObj(seg));
      }

      // merge with Stats data
      // comment-out, not showing stats in segments in campaign
      // this._fetchWhoStats();
      this._showWho();
    },

    // ..................................
    _showWho: function()
    {
      if (this._segmentList && this._segmentList.length)
      {
        for (var i = 0; i < this._segmentList.length; i++)
        {
          // Create Segment UI item
          this._createSegmentItem(this._segmentList[i]);
        }

//        this._showPublishBtn(true);
//        this._togglePublishBtn();
      }
    },

    // ..............................
    // delete and clear segments
    destroySegments: function()
    {
      if (!this._segWidget || !this._segWidget.length)
      {
        return;
      }
      for (var i = 0; i < this._segWidget.length; i++)
      {
        if (typeof (this._segWidget[i].destroyRecursive) === 'function')
        {
          this._segWidget[i].destroyRecursive();
        }
      }
      this._segWidget = [];
      // this.segHolder.innerHTML = "";
      var childSegNodes = this.segmentCont.childNodes;
      for ( var node in childSegNodes)
      {
        var chNode = childSegNodes[node];
        //if (domAttr.get(chNode, "data-ref"))
        if (chNode.dataset && chNode.dataset.ref)
        {
          this.segmentCont.removeChild(chNode);
        }
      }
    },

    //................................................................
    // Check if segment can be changed
    // This is only for Experiences.
    // 1. changeable: true -> show 'x' button on segment
    // and show the '+' button to add segments
    // 2. changeable: false -> hide/remove 'x' button from segment
    // and hide the '+' button to add segments
    markSegmentsChangeable: function ()
    {
      if (!this._templateDetails) return;
      let arts = this._templateDetails.artifacts;
      var refList = [];
      var payload = this.getContext().payload;
      if (payload && payload.states && payload.states.length)
      {
        refList = CampaignCtx.jData.getCpgnStateSegmentRefList(payload.states[0]);
      }
      else
        return;
      let refs2 = [];
      refList.forEach(rf => {
        let r = rf;
        if (r.startsWith('segment+')) r = r.replace("segment+", "");
        refs2.push(r);
      });
      
      arts.forEach(a => {
        if (refs2.includes(a.id + ":" + a.version))
        {
          if (a.property &&
            (a.property.changeable === "false" || a.property.changeable === void(0)))
          {
            // If segment artifact is present and changeable is undefined
            // then set changeable to false (default)
            this.isChangeable = false;
          }
        }
      });
      if (this.isChangeable === false)
      {
        this.addSegmentBtn.classList.add("c3_hide");
        this.dialogBody.dataset.segChgbl = 0;
      }
    },

    //.................................................
    // Generate unique suffix string. Used in click
    // events to access matching element in list.
    _getUqSfx: function()
    {
      this._uqCount = this._uqCount + 1;
      return "_xy_" + (new Date()).getTime() + "_" + this._uqCount + "x";
    },

    //................................................
    _alert: function(msg)
    {
      var alertParams = {
        "dialog_title": msg.title || Res.info,
        "dialog_msg": msg.text || msg.msg
      };
      var alertDialog = new AlertBox(alertParams);
      alertDialog.startup();
      alertDialog.setParent(this);
      this.own(alertDialog);
    },

    //........................................
    getContext: function ()
    {
      return CampaignCtx.jData.getData();
    },

    //........................................
    _save: function ()
    {
      if (!this.validate()) return;

      let d = this.getData();
      // save it.
      // set data
      //CampaignCtx.jData.setDelay(d);
      //if (this._parent && typeof this._parent.saveDelay === 'function')
      //{
      //  this._parent.saveDelay();
      //}

      this.destroy();
    },

    //................................
    _setEvents: function()
    {
      this.own(
        // Add Segment - add Campaign target
        on(this.addSegmentBtn, "click", lang.hitch(this, function()
        {
          if (this.skipPopup)
          {
            this._selectSegment();
          }
          else
          {
            this._addSegments();
          }
        })),

        // close
        on(this.btnSave, "click", lang.hitch(this, function(){
          this._save();
        })),
        // cancel
        on(this.btnCancel, "click", lang.hitch(this, function(){
          this.destroy();
        })),
        // close
        on(this.btnClose, "click", lang.hitch(this, function(){
          this.destroy();
        }))
      );
    },

    //...................................
    _subscribe: function()
    {
      this.own(
        // OTC was saved.
        topic.subscribe("/campaignonce/publish", lang.hitch(this, function(ctx)
        {
          // OTC data was saved.
          // Show/hide drop-down box or drop-down options based on if
          // AB actions are saved
          // All actions are deleted. Campaign can have either AB or non-AB actions
          // so show all drop-down options.
          // ~
        })),
        // TI was saved.
        topic.subscribe("/campaign/publish", lang.hitch(this, function(ctx)
        {
          // TI data was saved.
          // ~
        }))
      );
    },

    //.....................................................
    destroy: function()
    {
      this.inherited(arguments);
    },
    
    
    _end : 0
  });
  

});
