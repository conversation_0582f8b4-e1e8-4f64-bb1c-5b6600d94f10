/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define(["dojo/_base/declare", "dojo/_base/lang", "dojo/on", "udc/udc", "dijit/layout/ContentPane",
  "z1/c3/desktop/start", "udc/core/rt/widgets/_ComplexBaseWidget", "udc/core/commons/loader",
  "dojo/text!z1/c3/desktop/page/templates/campaigns/CampaignContainer2.html",
  "dojo/i18n!z1/c3/desktop/page/nls/CampaignContainer2Res",
  "z1/c3/desktop/page/util/C3DialogBox",
  "z1/c3/desktop/page/C3DeleteDialogBox",
  "z1/c3/desktop/page/C3AlertBox",
  "z1/c3/desktop/page/campaigns/Campaign",
  "z1/c3/desktop/page/campaigns/ChangeLog",
  "z1/c3/desktop/page/campaigns/ScheduleCampaignonce",
  "z1/c3/desktop/page/campaigns/SelectSegment", 
  "z1/c3/desktop/page/campaigns/CampaignSegmentItem",
  "z1/c3/desktop/page/campaigns/CampaignActionItem",
  "z1/c3/desktop/page/campaigns/EditCampaignAction",
  "z1/c3/desktop/page/campaigns/CampaignMetrics",
  "z1/c3/desktop/page/campaigns/CampaignInsights",
  "z1/c3/desktop/page/customerJourney/JourneySegment",
  "z1/c3/desktop/page/customerJourney/ViewJourney",
  "z1/c3/desktop/page/campaigns/TimerControl",
  "z1/c3/desktop/page/campaigns/CpgnPostActions",
  "z1/c3/desktop/data/C3Context",
  "z1/c3/desktop/data/C3JourneyCtx",
  "z1/c3/desktop/data/CampaignCtx",
  "z1/c3/desktop/page/comments/CommentsContainer",
  "z1/c3/desktop/page/campaigns/ChangeLogDisplay",
  "z1/c3/desktop/page/campaigns/CpgnChangeList",
  "z1/c3/desktop/page/auditTrail/AuditTrailBtn",
  "z1/c3/desktop/page/auditTrail/AuditTrailContainer",
  "z1/common/C3Util",
  "z1/c3/utils/Permissions",
  "z1/c3/utils/JourneyPermissionState",
  "z1/c3/desktop/page/modules/ModuleSaveAs",
  "dojo/topic", "dojo/dom-style", "dojo/dom-class",
  "dojo/dom-construct", "dojo/Deferred", "dojo/promise/all", "dojo/dom-attr",
  "dijit/form/DateTextBox",
  "z1/c3/utils/validator"
], function(declare, lang, on, udc, ContentPane, z1, BaseWidget, loader,
  templateStr, Res, DialogBox, DeleteDialogBox, AlertBox,
  Campaign, ChangeLog, ScheduleCampaignonce, SelectSegment, CampaignSegmentItem,
  CampaignActionItem, EditCampaignAction, CampaignMetrics, CampaignInsights, JourneySegment, ViewJourney,
  TimerControl, CpgnPostActions,
  C3Context, JourneyCtx, CampaignCtx, CommentsContainer,
  ChangeLogDisplay, CpgnChangeList,
  AuditTrailBtn, AuditTrailContainer, C3Util, UserACL, PermissionState,
  ModuleSaveAs, topic, style, domClass, domConstruct, Deferred,
  all, domAttr, DateTextBox, validator)
{

  /**
   * Creates a widget to show C3 landing page.
   */
  return declare([BaseWidget, Campaign], {
    templateString: templateStr,

    Res: Res,

    _isEditMode: false,

    _journeyId: null,

    _totalJourneys: "",

    _maxCircHt: "70",

    _maxBarHt: "55",

    _radius: "50",

    _jItems: null,

    _segWidget: null,

    _popCnt: 0,
    _convPerc: 0,

    _ALWAYS: "always://",
    


    // One Time Campaign, if conversion segments are
    // specified then the default validity days is 10
    _defaultValidityDays: 10,

    /**
     * Creates an instance of this page.
     * 
     * @param {Object}
     * @param {Object}
     *          parentNode
     */
    constructor: function(params, parentNode)
    {
      this.inherited(arguments);
      this._isEditMode = (params.editMode) ? params.editMode : false;
      this._goalId = params.goalId;
      this._isComplete = (params.isComplete) ? params.isComplete : true;
      this._isReady = (params._journeyItem)? params._journeyItem.isReady: false;
      
      this._isSuspended = (params._journeyItem) ? params._journeyItem.isSuspended : false;

      this._totalJourneys = (params.totalJourneys) ? params.totalJourneys : 0;
      this._journeyId = (params.journeyId) ? params.journeyId : null;
      this._journeyName = (params.journeyName) ? (params.journeyName) : "";
      this._journeyDesc = (params.journeyDesc) ? (params.journeyDesc) : "";
      this._lastUpdatedTime =  (params.lastUpdatedTime) ? (params.lastUpdatedTime) : "";
      this._lastUpdatedBy =  (params.lastUpdatedBy) ? (params.lastUpdatedBy) : "";
      if(!params._cmd || params._cmd !== "clone")
      {
        // not 'clone'
        
      }
      this._parent = parentNode;
      this._jItems = [];
      this._segWidget = [];
      this._popCnt = 0;
      this._convPerc = 0;
      this._accessible = true;
      this._accessFlag = null;

      params._journeyItem = (params._journeyItem) ? (params._journeyItem) : {};

      // if 'lastPublished' is missing then add to _journeyItem and add
      // dummy timestamp to indicate that the Campaign was previously Published.
      // "-1" value indicates never-published => make time-based-action items read only
      if(params._journeyItem.lastPublished === void(0) && params.editMode === false)
      {
        // create new 
        params._journeyItem.lastPublished = -1;
      }
      else if(params._journeyItem.lastPublished === void(0) && params.lastPublished !== void(0))
      {
        // from CamapaignOnce > create new one time campaign flow.
        params._journeyItem.lastPublished = params.lastPublished;
      }
      else if(params._journeyItem.lastPublished === void(0))
      {
        params._journeyItem.lastPublished = (new Date()).getTime();
      }

      // list of segment palettes (data + stats)
      this._segmentList = [];
      // list of final/outcome segments.
      this._finalSegmentList = [];

      params._usedIn = params._usedIn || CampaignCtx.consts.usedIn.CAMPAIGN;

      // by default in OTC set scheduled time to everyday/continuous-running
      if(params._usedIn && params._usedIn === CampaignCtx.consts.usedIn.CAMPAIGNONCE)
      {
        params.timerData = params.timerData || {};
      }
      // by default in Campaign set scheduled time to everyday/continuous-running
      if(params._usedIn && params._usedIn === CampaignCtx.consts.usedIn.CAMPAIGN)
      {
        params.timerData = params.timerData || {};
      }

      // Remove extra params if it's a CLONE
      if(params._cmd && params._cmd === "clone")
      {
        params._journeyItem.lastPublished = void(0);
      }

      if(params._readOnly === void(0))
      {
        // default - editable
        params._readOnly = false;
      }

      // Use appropriate rosource strings, based on campaign/interactions
      params.selectSegmentTitle = Res["selectSegmentTitle_" + params._usedIn];
      params.configureActionsTitle = Res["configureActionsTitle_" + params._usedIn];
      params.selectFinalSegmentTitle = Res["selectFinalSegmentTitle_" + params._usedIn];
      params.retargetTitle = (Res["retargetTitle_" + params._usedIn]) ? Res["retargetTitle_" + params._usedIn] : Res["retargetTitle"];

      // debug 1, production 0
      params._zDBG = 0;
    },

    postCreate: function()
    {
      this.inherited(arguments);
      
      UserACL.checkPermission(this, this.dialogBody, 'campaignonce', ['c']);
      if (this._ypc === false)
      {
        this.saveModuleBtn.remove();
      }
      
      UserACL.checkPermission(this, this.dialogBody, 'modules', ['c'], 'createNSModule');
      if (this._ypc === false)
      {
        this.saveModuleBtn.remove();
      }
      // Commented for 4.212.0 ACL work
      //this._checkSaveTemplatePermission();
    },

    startup: function()
    {
      this.inherited(arguments);
      //The _statusPermissionArr is a combination of the states = isComplete and isReady flags
      //and the User Permissions - Create, Publish
      //e.g. Consider this table when reading the keys of the status flag
      //State						Permission				Button
      //------------------------------------------------------------------
      //isComplete + isReady		Create + Publish		Button to Show
      //------------------------------------------------------------------
      // F		   +  F				C	 + 	P 					Publish
      // F		   +  F             C                           Review
      // F         +  F                     P                   Hide
      // F         +  T             C    +  P                   Publish
      // F         +  T             C                           Review
      // F         +  T                     P                   Publish
      // T         +  F (invalid condition. should not exist)
      // T         +  T             C    +  P                   Unpublish
      // T         +  T             C                           Hide
      // T         +  T                     P                   Unpublish
      
      // show or hide save button
      this._checkSavePermission();
      // keep this as first call in startup()
      this._checkEditPublishPermission();

      this._showPublishedCpgnAlert();
      this._setUIForCampaignType();
      this._getCampaignData();
      this.tabHolder.classList.remove("c3_hide");
      this._initChangeLog();
      this._showScreen(2);
      this._fetchComments();
      this._startCommentsChecker();
      this._showAuditTrailBtn();

      this._setEvents();
      this._subscribe();
    },

    //..............................................
    _checkSavePermission: function()
    {
      // ZMOB-13361 removing save module button temporarily
      this.saveModuleBtn.remove();
      return; 
      
      if (this._ypc)
      {
        this.own(on(this.saveModuleBtn, "click", lang.hitch(this, "onSaveModuleBtn")));
        this.saveModuleBtn.classList.remove('c3_hide');
      }
    },
    
    // an analyst or content dev is not allowed to save as template
    // the button should be hidden despite any other logic handled in this page
    _checkSaveTemplatePermission: function()
    {
      var roles = udc.context.getUser().role.split("|");
      var isAllowed = false;
      for (var i = 0; i < roles.length; i++)
      {
        if (roles[i] !== "analyst" && roles[i] !== "contentdev") isAllowed = true;
      }
      if (!isAllowed)
      {
        this.saveModuleBtn.remove();
      }
    },

    //..............................................
    _checkEditPublishPermission: function()
    {
      //ask if the user has permission to create a triggered interaction
      PermissionState.setState(this._usedIn,this._isComplete, this._isReady);
      this._displayButton();

    },

    //.................................
    _displayButton: function()
    {
      var btn = PermissionState.getButtonToShow();
      // P="Publish", H="Hide", R="Review", U="Unpublish" buttons

      // OTC
      if (this._usedIn && this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGNONCE)
      {
        // OTC - only
        // For now hiding runIt/stopIt buttons when isComplete:true (indicates OTC was ran)
        // isComplete:true when in [running, finished running] states,
        // isComplete:false when in [draft, review] states

        // FIXME - create a Map of following states for finer state management.
        //case draft:
        //  isComplete = false
        //  isSuspended = false
        //  isReady = false
        //  isRunning = false
        //  
        //case review:|ready
        //  isComplete = false;
        //  isSuspended = false;(test this. another cpgn with conversion isSuspended: false)
        //  isReady = true;
        //  isRunning = false
        //  
        //case published:|running
        //  isComplete = true;
        //  isSuspended = true;( another cpgn with conversion isSuspended: false)
        //  isReady = true;
        //  isRunning = true
        //  
        //case suspended:
        //  isComplete = true;
        //  isSuspended = true;( another cpgn with conversion isSuspended: false)
        //  isReady = true;
        //  isRunning = false
        //  
        //case otc ran & finished:
        //  isComplete = true; (isComplete:true + isRunning:false indicates it was running earlier)
        //  isSuspended = true;( another cpgn with conversion isSuspended: false)
        //  isReady = true;
        //  isRunning = false;
        
        

        // Test and Implement it in next iteration
        // The three characters position in key represents:
        // [isComplete][isSuspended][isReady][isRunning]
        this.campaignonceStateMap = {
          "FFFF": "draft",
          "TFTT": "running", // (test OTC, isSuspended state with/without Conversion segments)
          "TTTT": "running", // (test OTC, isSuspended state with/without Conversion segments)
          "FTTF": "ready", // (test OTC, isSuspended state with/without Conversion segments)
          "FFTF": "ready",
          "TTTF": "finished", // (test OTC, isSuspended state with/without Conversion segments)
          "TFTF": "finished" // (test OTC, isSuspended state with/without Conversion segments)
        };

        // For now hiding runIt/stopIt buttons when isComplete:true (indicates OTC was ran)
        // isComplete:true when in [running, finished running] states,
        // isComplete:false when in [draft, review] states
        if (!this._isComplete)
        {
          // hide - buttons
          // hide review|stopIt button
          // isComplete:false when in [draft, review] states
          // Show the state that came from PermissionState.getButtonToShow();
          //
          // 1. analyst clicked 'Send for Review' > campaign state is isReady:true
          //    Analyst opens the campaign again > the 'Send for Review' button is showing.
          //    For now allowing Maker to further Edit campaign and 'Send for Review' again.
          //    To block do if(isComplete && btn==="R") btn = "H";
          //
          btn = btn;
        }
        else
        {
          // hide - buttons
          // isComplete:true when in [running, finished running] states,
          btn = "H";
        }
      }

      // FIXME - if campaigns in 'draft' state then check if 
      //         campaigns minimum required fields are not empty.
      //         If they are empty then set state to "H".
      //         Look in Revision history and in method showPublish/togglePublish
      //         for old logic, to check for minimum required (e.g. segment, actions, ..) data.
      //~

      if (btn === "P")
      {
        domClass.add(this.reviewBtn, "c3_hide");
        domClass.remove(this.pubBtn, "c3_hide");
        if (this.pubBtn)
        {
          if (this._usedIn && this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGN)
          {
            this.pubBtn.innerHTML = Res.publish;
          }
          else
          {
            this.pubBtn.innerHTML = Res.runit;
          }
        }
        domClass.add(this.pubBtn, "btn-primary");
        domClass.remove(this.pubBtn, "btn-warning");
      }
      else if (btn === "H")
      {
        domClass.add(this.pubBtn, "c3_hide");
        domClass.add(this.reviewBtn, "c3_hide");
      }
      else if (btn === "R")
      {
        domClass.remove(this.reviewBtn, "c3_hide");
        domClass.add(this.pubBtn, "c3_hide");
      }
      else if (btn === "U")
      {
        domClass.add(this.reviewBtn, "c3_hide");
        domClass.remove(this.pubBtn, "c3_hide");
        if (this.pubBtn)
        {
          if (this._usedIn && this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGN)
          {
            this.pubBtn.innerHTML = Res.unpublish;
          }
          else
          {
            this.pubBtn.innerHTML = Res.stopit;
          }
        }
        domClass.remove(this.pubBtn, "btn-primary");
        domClass.add(this.pubBtn, "btn-warning");
      }

    },
    
    
    _sendReview: function(){
    	CampaignCtx.jData.sendForReview().then(lang.hitch(this, function(dataAll)
    	   {
    		if(this._usedIn && this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGNONCE)
            {
              z1.fireEvent(z1.Events.showCampaignsOne);
             
            }
            else
            {
              z1.fireEvent(z1.Events.showCampaigns);
            }
          }), lang.hitch(this, function(err)
          {
            udc.log("err, send review ");
            udc.log(err);
            
          }));
    },
    
    // ........................................
    // show/hide elements based on campaign type
    _setUIForCampaignType: function()
    {
      // 1. read-only or editable
      if(this._readOnly)
      {
        if(this._usedIn && this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGNONCE)
        {
          udc.bannerAlert(Res.readOnlyBannerMsg);
        }

        domClass.add(this.campaignCont, "c3_read_only");

        // make <input> fields readonly
        if(this._1tcValidityDays)
        {
          this._1tcValidityDays.readOnly = true;
        }
      }

      // 2. campaign-type: campaign | campaignonce | journey
      if(this._usedIn && this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGN)
      {
        // campaign
      }
      else if(this._usedIn && this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGNONCE)
      {
        // campaingonce - one time campaign
        // a. hide campaign Time period
        this.timePeriodCont.classList.add("c3_hide");
        // a.1 201703 Show Specified Time (and related widgets) campaignonce
        this.timePeriodContOTC.classList.remove("c3_hide");

        // b. Change the items count order 1. 2. 3.
        this.actionsTitleContNum.innerHTML = Res.two;
        this.finalSegmentTitleContNum.innerHTML = Res.three;
      }
    },

    // .............................
    getContext: function()
    {
      return CampaignCtx.jData.getData();
    },

    //...................................
    initPage: function()
    {
      this._setHeader();

      //this._setStatesContent();
      this._showContent();
    },

    // ..................................
    // Campaign:
    // There are at most two states in the Campaign.
    // 1. states[0] - Contains the (who) target Segment groups
    // 2. states[1] - Second state contains the target Outcome
    // of the campaign. E.g. 'Bought group'. This can indicate
    // conversion from state 1 segments to state 2.
    //
    // The outcome state is always number two states[1]. So
    // when creating outcome state make sure state[0] exists. If
    // it's not there then create an empty one with ref "".
    // If it does not exists then create one with ref ""
    // as no segments have been added.
    // Journey:
    // return Selected state
    getSelectedState: function(ctx)
    {
      // 1. State exists.
      // 2. Add Segment to Customer Group
      // this._segmentGroup.segments.push(ctx.item.id);
      var payload = this.getContext().payload;
      if (!payload) return;
      if (this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGN
          || this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGNONCE)
      {
        // Campaign
        if (!payload.states || !payload.states.length)
        {
          // create state with EMPTY 'ref'
          // No segments created so no id/ref available.
          CampaignCtx.jData.createJourneyState({
            id: ""
          });
        }
        // get updated payload structure.
        payload = this.getContext().payload;
        return payload.states[0];
      }
      else if (this._usedIn === CampaignCtx.consts.usedIn.JOURNEY)
      {
        // Journey
        // ~ ...
      }
      return;
    },

    /**
     * Set the page's title as the journey Name and the mode that you are in (edit/create) Attach a
     * handle to allow editing and renaming of journey Title & journey description.
     * 
     * @param label
     * @param value
     * @returns
     */
    _setHeader: function()
    {
      this.journeyMode.innerHTML = (this._isEditMode) ? Res.editItem + ": " : Res.newItem + ": ";
      this.journeyName.innerHTML = (this._isEditMode || this._cmd === "clone") ? this.getContext().name
          : (this._journeyName && this._journeyName !== "") ? this._journeyName : Res["untitledJourney_" + this._usedIn];
      this.journeyDesc.innerHTML = (this._isEditMode) ? this.getContext().description : "";

      this.journeyOverviewName.innerHTML = this.journeyName.innerHTML;
      this.journeyOverviewDesc.innerHTML = this.journeyDesc.innerHTML
      if(this._journeyItem && !isNaN(this._journeyItem.na_users))
      {
        this.journeyPopulation.innerHTML = this._journeyItem.na_users;
      }
      else
      {
        this.journeyPopulation.innerHTML = "0";
      }
      //journeyCreationDt
      //journeyCreatedBy
      this.journeyCreationDt.innerHTML = (this._journeyItem.creationTime) ? C3Util.MS_to_dd_month_yyyy_hhmm(this._journeyItem.creationTime) : "";
      this.journeyCreatedBy.innerHTML = (this._journeyItem.createdBy) ? this._journeyItem.createdBy : "";
      this.journeyLastUpdatedDt.innerHTML = (this._journeyItem.lastUpdatedTime) ? C3Util.MS_to_dd_month_yyyy_hhmm(this._journeyItem.lastUpdatedTime) : "";
      this.journeyLastUpdatedBy.innerHTML = (this._journeyItem.lastUpdatedBy) ? this._journeyItem.lastUpdatedBy : "";
      // this.journeyConversion.innerHTML = "0%";

      this.own(on(this.journeyName, "click", lang.hitch(this, "_createJourneyName")));
    },

    //..................................
    _setStatesContent: function()
    {
      if (this._isEditMode)
      {
        targetUrl = "c3/data/" + this._usedIn + "/segmentStat?journeyId=" + this._journeyId;
        // make a backend call to get the goal data for this id
        loader.GET(targetUrl, {}).then(lang.hitch(this, function(data)
        {
          // show the states that were created and saved
          this._viewStates(data);

        }), lang.hitch(this, function(error)
        {
          udc.log(error);
        }));
      }
    },

    // ..................................
    _showContent: function()
    {
      this._prepareWhoForUI();
      // Timer data
      this.timerData = CampaignCtx.jData.getTimerData();
      //this.showTimerData();
      this._showActions();

      this._showCpgnPostActions();
      // this._setStatesContent();
      this._prepareFinalWhoForUI();
      this._setOTCValidityDays();
      if(this._usedIn && this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGN)
      {
        this._showHideRetargetCont();
        this._populateRetarget();
      }
    },

    // ..................................
    _showActions: function()
    {
      if(this._usedIn && (this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGN
          || this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGNONCE))
      {
          // by default show continuous running
        this.showTimerData();
      }

      domConstruct.empty(this.actionsListCont);

      var data = this.getContext();
      var state;
      if (!data || !data.payload || !data.payload.states || !data.payload.states.length)
      {
        return;
      }
      var states = data.payload.states;
      // for (var i = 0; i < states.length; i++)
      // {
      // Show action list from states[0] only
      if(states && states[0])
      {
        var i = 0;
        this._showActionsList({
          actionList: states[i].timers,
          _state: states[i],
          actionType: CampaignCtx.consts.actionType.TIME,
          _mode: "edit"
        });
        this._showActionsList({
          actionList: states[i].activities,
          _state: states[i],
          actionType: CampaignCtx.consts.actionType.ACTIVITY,
          _mode: "edit"
        });
      }
      // }
    },

    // ...........................................
    _showActionsList: function(ctx)
    {
      if (!ctx) return;
      var actionList = ctx.actionList;
      if (!actionList) return;
      if (this._usedIn && this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGNONCE
        && ctx.actionType === CampaignCtx.consts.actionType.TIME)
      {
        // empty actions list or have c1 schedule specified without action 
        if (actionList.length === 0 ||(actionList.length === 1 && (!actionList[0].actions || actionList[0].actions.length === 0)))
        {
          // show add action button
          this._showAddActionBtn(true);
        }
        else
        {
          // hide add action button
          this._showAddActionBtn(false);
        }
      }
      if (!actionList.length) return;
      actionList.forEach(lang.hitch(this, function(timer, timerIndex){
        if (Array.isArray(timer.actions))
        {
          timer.actions.forEach(lang.hitch(this, function(axn, axnIndex){
            this.createActionItemRow({
              // timer or activity
              _timerData: timer,
              _axnData: axn,
              _dItem: timer,
              actionList: actionList,
              // pass is ABCompositeAction
              _isABCompositeAction: (timer._isABCompositeAction)? true : false,
              _state: ctx._state,
              // timer or activity index
              _timerIndex: timerIndex,
              _actionIndex: axnIndex,
              actionType: ctx.actionType,
              _mode: 'edit',
              _usedIn: this._usedIn,
              lastPublished: this._journeyItem.lastPublished,
              _parent: this
            });
          }));
        }
      }));
      // Actions exists so show Publish button.
      // ui publish state
      // check timePayload exists + !campaignonce first.
      var pubFlag = true;
      if(this._usedIn && this._usedIn !== CampaignCtx.consts.usedIn.CAMPAIGNONCE)
      {
        if (this.timerData)
        {
          // if timer data is set then show publish button
          pubFlag = true;
        }
        else
        {
          pubFlag = false;
        }
      }
      this.showPublishState(pubFlag);
    },

    // ..........................................
    // Create action item row
    createActionItemRow: function(ctx)
    {
      if (!ctx || !ctx._dItem || !ctx._dItem.actions || !ctx._dItem.actions.length) return;
      var campaignActionItem = new CampaignActionItem(ctx);
      campaignActionItem.startup();
      domConstruct.place(campaignActionItem.domNode, this.actionsListCont, 'last');
      this.own(campaignActionItem);
    },

    // .............................................
    _showAddActionBtn: function(showIt)
    {
      if (showIt)
      {
        domClass.remove(this.addActionBtn, "c3_hide");
      }
      else
      {
        domClass.add(this.addActionBtn, "c3_hide");
      }
    },

    //.................................
    // Pre Action script
    // Post Action script
    // Post Action Response script
    _showCpgnPostActions: function ()
    {
      if (this.cpgnPostActions
          && typeof this.cpgnPostActions.destroy === 'function')
      {
        this.cpgnPostActions.destroy();
      }

      var pCtx = {
        _usedIn: this._usedIn,
        _parent: this
      };
      // read-only
      if(this._readOnly)
      {
        // read only - skip
        pCtx._readOnly = true;
      }
      this.cpgnPostActions = new CpgnPostActions(pCtx);
      this.cpgnPostActions.startup();
      domConstruct.place(this.cpgnPostActions.domNode, this._postActionsCont);
      this.own(this.cpgnPostActions);
    },

    // .............................................
    _showAddSegBtn: function(showIt)
    {
      if (showIt)
      {
        domClass.remove(this.addSegmentBtn, "c3_hide");
      }
      else
      {
        domClass.add(this.addSegmentBtn, "c3_hide");
      }
    },

    // .................................
    _prepareWhoForUI: function()
    {
      var jData = this.getContext();
      var state;
      var refList = [];
      if (!jData || !jData.payload || !jData.payload.states)
      {
        return;
      }
      // Assumption: In Campaign there is only 1 state
      state = jData.payload.states[0];
      refList = CampaignCtx.jData.getCpgnStateSegmentRefList(state);

      // reset palette list
      this.destroySegments();
      this._segmentList = [];
      this._showAddSegBtn(true);

      for (var i = 0; i < refList.length; i++)
      {
        // Copy matched Segment palette item data
        // to _segmentList []

        var seg;
        if (refList[i] === CampaignCtx.jData.allUsers.id)
        {
          seg = C3Context.goalData.copyJObj(CampaignCtx.jData.allUsers);
          // If 'All Users' is added then no need to add other segments
          // Hide Add-Segment(+) button.
          this._showAddSegBtn(false);
        }
        else
        {
          seg = C3Context.goalData.getPaletteItemById(refList[i]) || {
            id: refList[i]
          };
        }
        this._segmentList.push(C3Context.goalData.copyJObj(seg));
      }

      // merge with Stats data
      this._fetchWhoStats();
    },

    // ..................................
    _showWho: function()
    {
      if (this._segmentList && this._segmentList.length)
      {
        for (var i = 0; i < this._segmentList.length; i++)
        {
          // Create Segment UI item
          this._createSegmentItem(this._segmentList[i]);
        }

        this._showPublishBtn(true);
        this._togglePublishBtn();
      }
    },

    // ...................................................................
    // {
    // isFinalSegments - true/false - Indicates if segments are Outcome
    // segments or initial segment group.
    // }
    _fetchWhoStats: function(ctx)
    {
      var ctx = ctx || {};
      if (this._isEditMode)
      {
        targetUrl = "c3/data/" + this._usedIn + "/segmentStat?journeyId=" + this._journeyId;
        // make a backend call to get the goal data for this id
        loader.GET(targetUrl, {}).then(lang.hitch(this, function(data)
        {
          // show the states that were created and saved
          // this._viewStates(data);
          if (data && data.length)
          {
            this._mergePaletteDataWithStats({
              isFinalSegments: ctx.isFinalSegments,
              data: data
            });
          }
          else
          {
            // no stats, show without stats.
            this.journeyPopulation.textContent = 0;
            if (!ctx.isFinalSegments)
            {
              this._showWho();
            }
            else
            {
              this._showOutcomeWho();
            }
          }
        }), lang.hitch(this, function(error)
        {
          udc.log(error);
          this._showWho();
        }));
      }
      else
      {
        // new campaign
        if (!ctx.isFinalSegments)
        {
          this._showWho();
        }
        else
        {
          this._showOutcomeWho();
        }
      }
    },

    // ..................................................
    // Merge Segment palette item data with Stats data.
    _mergePaletteDataWithStats: function(ctx)
    {
      var ctx = ctx || {};
      var data = ctx.data || [];
      var segmentList;
      if (!ctx.isFinalSegments)
      {
        segmentList = this._segmentList;
      }
      else
      {
        segmentList = this._finalSegmentList;
      }
      //
      this._viewStates(data);
      //if (data && segmentList && segmentList.length)
      //{
      //  for (var i = 0; i < segmentList.length; i++)
      //  {
      //    for (var j = 0; j < data.length; j++)
      //    {
      //      if (segmentList[i].id === data[j])
      //      {
      //        // copy Stat properties
      //        var sData = data[j];
      //        for ( var k in sData)
      //        {
      //          if (sData.hasOwnProperty(k))
      //          {
      //            segmentList[i][k] = sData[k];
      //          }
      //        }
      //        break;
      //      }
      //    }
      //  }
      //}

      if (!ctx.isFinalSegments)
      {
        this._showWho();
      }
      else
      {
        this._showOutcomeWho();
      }

      // // iterate through the new array to create segment state widgets
      // if (items.length > 0)
      // {
      // for ( var item in items)
      // {
      // this._onJourneyStepSelected({
      // item: items[item],
      // mode: "display"
      // });
      //
      // }
      //
      // this._showPublishBtn(true);
      // this._togglePublishBtn();
      // this._showJourneyStats("view");
      // }
      // this._sortChartData();
    },

    /**
     * Shows the states (segments) to view when loading an existing campaign
     */
    _viewStates: function(data)
    {
      var items = [];
      if (data && data.length)
      {
        // only one segment/state in Campaign - data[0]
        // data[1] - Final state (converted). Ignoring population from this state.
        this._popCnt = (data[0] && data[0].userCount)? data[0].userCount: 0;
      }
      this.journeyPopulation.innerHTML = this._popCnt;
      // show '?' if recounting needs to be done
      if(this._segmentList && this._segmentList.length)
      {
        for(var i = 0; i < this._segmentList.length; i++)
        {
          if(this._segmentList[i].population === "?")
          {
            this.journeyPopulation.innerHTML = Res.recountIt;
            break;
          }
        }
      }

      // this._sortChartData();
    },

    /**
     * Update the population and conversion statistics of journey when the user views, creates or
     * deletes a journey or step within
     */
    _showJourneyStats: function(op, item)
    {
      // if user deletes an existing step, reduce the population count and compute the avg
      // conversion
      if (op === "update")
      {
        this._popCnt = this._popCnt - item.userCount;
        if (this._jItems && this._jItems.length > 0)
        {
          for ( var item in this._jItems)
          {
            this._convPerc += (this._jItems[item].conversion) ? this._jItems[item].conversion : 0;
          }
          this._convPerc = this._convPerc / (this._jItems.length);
        }
        else
        {
          this._convPerc = 0;
        }
      }
      // if the user views an existing journey or adds a new journey step increment the total
      // population and re-compute the avg
      else
      {
        this._popCnt = 0;
        this._convPerc = 0;
        if (this._jItems && this._jItems.length > 0)
        {
          for ( var item in this._jItems)
          {
            this._popCnt += (this._jItems[item].userCount) ? this._jItems[item].userCount : 0;
            this._convPerc += (this._jItems[item].conversion) ? this._jItems[item].conversion : 0;
          }
          this._convPerc = this._convPerc / (this._jItems.length);
        }
      }

      this.journeyPopulation.innerHTML = this._popCnt;
      // this.journeyConversion.innerHTML = Math.round(this._convPerc) + " %";

    },

    // .....................................
    _sortChartData: function()
    {
      // var tempItems = this._jItems;
      /*
       * var tempItems = [{ "userCount": 14, "avgUserCount": 0, "ref":
       * "udc.system.core.CommonOOTB:IceCream", "conversionCountWithAction": 0, "name": "Ice Cream
       * Sandwich", "conversionCountNoAction": 0 }, { "userCount": 50, "avgUserCount": 0, "ref":
       * "udc.system.core.CommonOOTB:Nexus7User", "conversionCountWithAction": 0, "name": "Nexus 7",
       * "conversionCountNoAction": 0 }, { "userCount": 12, "avgUserCount": 0, "ref":
       * "udc.system.core.CommonOOTB:IPhoneUser", "conversionCountWithAction": 0, "name": "iPhone
       * User", "conversionCountNoAction": 0 }];
       */
      var tempItems = this._jItems.slice(0);
      if (tempItems.length > 0)
      {
        tempItems.sort(this.sortByUserCount);
        this._maxUserCt = tempItems[0].avgUserCount;
        tempItems.sort(this.sortByConversionCount)
        /*
         * this._maxConvCt = tempItems[0].conversionCountWithAction +
         * tempItems[0].conversionCountNoAction;
         */
        this._maxConvCt = tempItems[0].conversion;
        this._drawUserChart();
      }

    },

    sortByUserCount: function(segA, segB)
    {
      return parseInt(segB.avgUserCount) - parseInt(segA.avgUserCount);
    },

    sortByConversionCount: function(segA, segB)
    {
      return parseInt(segB.conversionCountWithAction + segB.conversionCountNoAction)
        - parseInt(segA.conversionCountWithAction + segA.conversionCountNoAction);
    },

    /**
     * Create chart to show the average user count as circles and the conversion count as bar chart
     */
    _drawUserChart: function()
    {
      if (this._maxUserCt != 0 || this._maxBarHt != 0)
      {
        domClass.remove(this.chartLegend, "c3_hide");
      }
      for ( var item in this._jItems)
      {
        var segItem = this._jItems[item];
        // create the div that will hold the circle and conversion bar chart
        var chartCircleOuterDiv = this._createElement("div", this.journeyCircleChart, {},
          "c3_cj_circle c3_cj_cell");
        var chartBarOuterDiv = this._createElement("div", this.journeyBarChart, {},
          "c3_cj_bar c3_cj_cell");
        var chartAxnOuterDiv = this._createElement("div", this.takeActionChart, {},
          "c3_cj_take_axn c3_cj_cell");
        chartCircleOuterDiv.dataset.circleRef = segItem.id;
        chartCircleOuterDiv.dataset.jItemRef = segItem.id;
        chartBarOuterDiv.dataset.barRef = segItem.id;
        chartBarOuterDiv.dataset.jItemRef = segItem.id;
        chartAxnOuterDiv.dataset.takeAxnRef = segItem.id;
        chartAxnOuterDiv.dataset.jItemRef = segItem.id;
        // Access all items with
        // document.querySelectorAll("[data-j-item-ref='udc.system.core.CommonOOTB:IceCream']");
        // draw both the circle and bar graphs only when the data is non-zero
        if (this._maxUserCt != 0 && segItem.avgUserCount != null && segItem.avgUserCount != 0)
        {
          // find out how many times avgUserCount is that of maxUser
          var userTimes = this._maxUserCt / segItem.avgUserCount;
          // make the height relative to the maxHt
          var circHt = Math.round(this._maxCircHt / userTimes);
          var circWt = circHt;

          var styleAttr = "height:" + circHt + "px;width:" + circWt + "px;border-radius:"
            + this._radius + "px";
          var circleChartDiv = this._createElement("section", chartCircleOuterDiv, {
            style: styleAttr,
            innerHTML: segItem.avgUserCount
          }, "c3_cj_search_item_bar");

        }

        // var segConvCt = segItem.conversionCountWithAction + segItem.conversionCountNoAction;
        var segConvCt = (segItem.conversion) ? segItem.conversion : 0;
        var dispPerc = segItem.conversion + "%";
        if (this._maxBarHt != 0 && segConvCt != 0)
        {
          var convTimes = this._maxConvCt / segConvCt;
          var barHt = Math.round(this._maxBarHt / convTimes);
          var styleAttr1 = "height:" + barHt + "px;";
          var barChart = this._createElement("section", chartBarOuterDiv, {
            style: styleAttr1,
            innerHTML: dispPerc
          }, "c3_cj_convert_item_bar");
        }

      }
    },

    /**
     * Dialog Popup to come up when new Journey Title to be created / edited
     */
    _createJourneyName: function()
    {
      // read-only
      if(this._readOnly)
      {
        // read only - skip
        return;
      }

      var journeyName = (this.getContext()) ? this.getContext().name : Res["untitledJourney_" + this._usedIn];
      var journeyDesc = (this.getContext()) ? this.getContext().description : "";
      var dialogParams = {
        "dialog_title": Res["journeyDialogTitle_" + this._usedIn],
        "inputHint": Res["journeyInputHint_" + this._usedIn],
        "inputValue": journeyName,
        "inputLabel": Res.inputLabel,
        "showDescription": true,
        "descHint": Res.journeyDesc,
        // "descValue": CampaignCtx.jData.getGoalDescription()
        "descValue": journeyDesc
      };
      var dialog = new DialogBox(dialogParams);
      dialog.setParent(this);
      dialog.setCallback(this._onJourneyNameCreated);
      validator.setErrorMsg(Res.errorNameTxt);
      validator.setDescriptionErrorMsg(Res.errorDescriptionTxt);
      dialog.setValidateFn(validator.isValidField);
      dialog.startup();
      this.own(dialog);
    },

    /**
     * Success Callback when the user has clicked OK button on providing Journey Name and
     * Description Update the JourneyContext with the journey name & description. Make a backend
     * call to update the server
     */
    _onJourneyNameCreated: function(data)
    {
      var deferr = new Deferred();
      if (data)
      {
        if (data.name && data.name !== "")
        {
          this.journeyName.innerHTML = data.name;
          var jDesc = (data.description) ? data.description : "";
          this.journeyDesc.innerHTML = jDesc;

          this.journeyOverviewName.innerHTML = data.name;
          this.journeyOverviewDesc.innerHTML = jDesc;

          CampaignCtx.jData.setJourneyDescription(jDesc);
          CampaignCtx.jData.setJourneyName(data.name);
          CampaignCtx.jData.setJourneyComplete(false);
          CampaignCtx.jData.saveData().then(lang.hitch(this, function(data)
          {
            if (data && data.status === "fail")
            {
              var msg = "";
              if(data.data && data.data.reason)
              {
                msg = data.data.reason;
              }
              udc.error("save failed. " + msg);
              udc.log(data);
            }

            if(!this._isEditMode)
            {
              // new Campaign
              // change mode from 'create' to 'edit'
              this._isEditMode = true;
              // set local campaign id
              this._journeyId = CampaignCtx.jData.getId();
            }

            deferr.resolve({
              pass: true
            });
          }), lang.hitch(this, function(error)
          {
            udc.error("save failed. ");
            udc.log(error);
            deferr.reject(error);
          }));
        }
      }
      return deferr.promise;
    },

    /**
     * Click handler to open the dialog box which holds the segments existing in the system for
     * selection
     */
    _createJourneySteps: function()
    {
      var selectStepsDlg = new JourneyStep();
      selectStepsDlg.setParent(this);
      selectStepsDlg.setCurrentSteps(this._jItems);
      selectStepsDlg.setCallback(this._onJourneyStepSelected);
      selectStepsDlg.startup();
    },

    /**
     * Callback when a segment has been selected and needs to be shown in the swimlane
     */
    _onJourneyStepSelected: function(ctx)
    {
      this._jItems.push(ctx.item);
      // this._toggleAddSegBtn();
      this._createSegmentStep(ctx.item);
      if (ctx.mode === "create")
      {
        this._showJourneyStats("create");
        this._showPublishBtn(true);
        CampaignCtx.jData.setJourneyComplete(false);
        CampaignCtx.jData.createJourneyState(ctx.item);
        CampaignCtx.jData.saveData(true).then(lang.hitch(this, function(data)
          {
          if (data && data.status === "fail")
          {
            var msg = "";
            if(data.data && data.data.reason)
            {
              msg = data.data.reason;
            }
            udc.error("retarget save failed. " + msg);
            udc.log(data);
          }

          if(!this._isEditMode || ctx.mode === "create")
          {
            // new Campaign
            // change mode from 'create' to 'edit'
            this._isEditMode = true;
            ctx.mode = "edit";
            // set local campaign id
            this._journeyId = CampaignCtx.jData.getId();
          }

          //deferr.resolve({
          //  pass: true
          //});
        }), lang.hitch(this, function(error)
        {
          udc.error("retarget save failed. ");
          udc.log(error);
          //deferr.reject(error);
        }));

        this._togglePublishBtn();
      }
    },

    // .....................................
    _publishJourney: function()
    {
      var deferr = new Deferred();
      var deferr2 = new Deferred();
      var dataMsg;

      this._publishJourneyStep1().then(lang.hitch(this, function(data){
        if(data && data.msg && data.msg === "no-time-specified")
        {
          // timer not specified
          // pass the 'msg'
          dataMsg = data;
          deferr.resolve(data);
        }
        else
        {
          deferr.resolve(true);
        }
      })).then(lang.hitch(this, function(data2){

        // step 2

        if(dataMsg && dataMsg.msg && dataMsg.msg === "no-time-specified")
        {
          // flow goes to _onConfirmNotification()
          //
          deferr2.resolve(true);
        }
        else
        {
          this._togglePublishBtn();
          if(this._usedIn && this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGNONCE)
          {
            z1.fireEvent(z1.Events.showCampaignsOne);
            // DEBUG
            // TEMPORARY FIX
            // using flag + readOnly => this.startup()
            // temporary - after Publish, restrict user to campaign editor
            // screen so that the next backend calls (All) is delayed, giving
            // time to finish Publish call.
            //this._readOnly = true;
            //this.startup();

            // 1. set Read-only 2. re-draw screen 3. Show listing page after 1 Second
            // 1 & 2 is to prevent user from clicking 'RunIt' or other buttons again
            // 3. is to give backend enough time to finish Update > Un-publish > Publish
            //this._readOnly = true;
            //this.startup();
            //// hide the 'run-it'
            //if(this.pubBtn) this.pubBtn.classList.add("c3_hide");
            ////window.setTimeout( z1.fireEvent(z1.Events.showCampaignsOne), 1500 );

          }
          else
          {
            z1.fireEvent(z1.Events.showCampaigns);
          }
          deferr2.resolve(true);
        }
      }));

      return deferr2.promise;
    },

    // .....................................
    _publishJourneyStep1: function()
    {
      var deferr = new Deferred();


      //correct sequence should be:
      //UPDATE
      //UNPUBLISH
      //RUN
      //PUBLISH
      //ALL - listing page 

      if (this.pubBtn && this.pubBtn.innerHTML === Res.runit)
      {
        // 1.
        CampaignCtx.jData.runCampaignOnce("c1").then(lang.hitch(this, function(data){

          // 2.
          // For One-time-campaign, check if there are
          // final/conversion segments (.states[1]).
          // If final/conversion segments exists then
          // onRunIt > publish it.
          // If no final/conversion segments specified then
          // onRunIt > suspend-it
          var isFinalSegmentsExists = this._isConversionSegmentsExists();
          if(isFinalSegmentsExists)
          {
            CampaignCtx.jData.publishJourney().then(lang.hitch(this, function(data){
              deferr.resolve(true);
            }));
          }
          else
          {
            // suspend
            CampaignCtx.jData.suspendJourney().then(lang.hitch(this, function(data){
              deferr.resolve(true);
            }));
          }
        }));
      }
      else if (this.pubBtn && this.pubBtn.innerHTML === Res.publish)
      {
        if (!CampaignCtx.jData._doesTimerExist())
        {
          // on 'Ok' this will publish campaign
          this._timerNotification();
          deferr.resolve({msg: "no-time-specified"});
          //return;
        }
        else
        {
          CampaignCtx.jData.publishJourney().then(lang.hitch(this, function(data){
            deferr.resolve(true);
          }));
        }
      }
      else
      {
        CampaignCtx.jData.unPublishJourney().then(lang.hitch(this, function(data){
          deferr.resolve(true);
        }));
      }

      return deferr.promise;
    },

    // ...........................................
    // @deprecated use above
    _publishJourneyOLD: function()
    {


      //correct sequence should be:
      //UPDATE
      //UNPUBLISH
      //RUN
      //PUBLISH
      //ALL 

      if (this.pubBtn && this.pubBtn.innerHTML === Res.runit)
      {
        // 1.
        CampaignCtx.jData.runCampaignOnce("c1");

        // 2.
        // For One-time-campaign, check if there are
        // final/conversion segments (.states[1]).
        // If final/conversion segments exists then
        // onRunIt > publish it.
        // If no final/conversion segments specified then
        // onRunIt > suspend-it
        var isFinalSegmentsExists = this._isConversionSegmentsExists();
        if(isFinalSegmentsExists)
        {
          CampaignCtx.jData.publishJourney();
        }
        else
        {
          // suspend
          CampaignCtx.jData.suspendJourney();
        }
      }
      else if (this.pubBtn && this.pubBtn.innerHTML === Res.publish)
      {
        if (!CampaignCtx.jData._doesTimerExist())
        {
          this._timerNotification();
          return;
        }
        else
        {
          CampaignCtx.jData.publishJourney();
        }
      }
      else
      {
        CampaignCtx.jData.unPublishJourney();
      }
      this._togglePublishBtn();
      if(this._usedIn && this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGNONCE)
      {
        //z1.fireEvent(z1.Events.showCampaignsOne);
        // DEBUG
        // TEMPORARY FIX
        // using flag + readOnly => this.startup()
        // temporary - after Publish, restrict user to campaign editor
        // screen so that the next backend calls (All) is delayed, giving
        // time to finish Publish call.
        //this._readOnly = true;
        //this.startup();

        // 1. set Read-only 2. re-draw screen 3. Show listing page after 1 Second
        // 1 & 2 is to prevent user from clicking 'RunIt' or other buttons again
        // 3. is to give backend enough time to finish Update > Un-publish > Publish
        this._readOnly = true;
        this.startup();
        // hide the 'run-it'
        if(this.pubBtn) this.pubBtn.classList.add("c3_hide");
        //window.setTimeout( z1.fireEvent(z1.Events.showCampaignsOne), 1500 );

      }
      else
      {
        z1.fireEvent(z1.Events.showCampaigns);
      }
    },
    

    //.........................................
    _timerNotification: function()
    {
      // show the dialog confirming deletion of channel.
      var dialogParams = {
        "dialog_title": Res.notificationTitle,
        "dialog_msg": Res["notificationMsg_" + this._usedIn]
      };
      var dialog = new DeleteDialogBox(dialogParams);
      dialog.startup();
      dialog.setParent(this);
      dialog.setCallback(this._onConfirmNotification);
      this.own(dialog);
    },

    //........................................
    _onConfirmNotification: function()
    {
      CampaignCtx.jData.publishJourney();
      CampaignCtx.jData.runCampaignOnce("campaign");
      this._togglePublishBtn();
      z1.fireEvent(z1.Events.showCampaigns);
    },
    

    // ..........................................
    _togglePublishBtn: function()
    {
    	this._displayButton();
    },

    // ......................................
    _showPublishBtn: function(flag)
    {
      // a. flag = true
      // b. states[0] has Actions. Else, hide publish button
      // c. timer data is set (optional for campaignonce)
      if(flag)
      {

        var data = this.getContext();
        var state;
        if (!data || !data.payload || !data.payload.states || !data.payload.states.length)
        {
          return;
        }
        var states = data.payload.states;
        // for (var i = 0; i < states.length; i++)
        // {
        // Only first state (states[0]) has Actions in Campaign
        if(states && states[0])
        {
          // Check if Segments exists
          var refList = CampaignCtx.jData.getCpgnStateSegmentRefList(states[0]);
          if(!refList || !refList.length)
          {
            // no Target Segments specified in states[0]
            udc.log("no Target Segments specified in states[0] so Publish not shown");
            flag = false;
          }

          // Check if Actions exists
          var flgT = false;
          var flgA = false;
          if(states[0].timers && states[0].timers.length)
          {
            flgT = true;
          }
          if(states[0].activities && states[0].activities.length)
          {
            flgA = true;
          }
          if(flag && !flgT && !flgA)
          {
            // no Actions in states[0]
            udc.log("No actions in State[0] so Publish not shown");
            flag = false;
          }
        }
      }

      // c. timer data is set (optional for campaignonce)
      // scheduled campaign
      if (!this.timerData && this._usedIn
        && this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGN)
      {
        flag = false;
      }

      // case d.
      // hide button if it's NEW campaign/journey
      // cmd == 'clone', most data set but not Saved
      if (CampaignCtx.jData.isNewJourney())
      {
        flag = false;
      }


    },

    // .....................................
    showPublishState: function(flag)
    {
      this._togglePublishBtn();
      this._showPublishBtn(flag);
    },

    // ........................................
    // Create UI for segment box - campaign
    // targets item
    _createSegmentItem: function(item)
    {
      var segment = new CampaignSegmentItem({
        _parent: this,
        jItem: item,
        readOnly: (this._readOnly === true) ? true : false
      });
      segment.setParent(this);
      // segment.setCallback(this._viewJourneySegmentTimeline);
      segment.setDeleteCallback(this._onSegmentDeleted);
      // // if its the 5th and last element dont show the arrow icon. its misleading
      // if (this._jItems.length == 5) segment.showArrowIcon(false);
      segment.startup();
      segment.domNode.setAttribute("data-ref", item.id);
      domConstruct.place(segment.domNode, this.addSegmentBtn, "before");
      this.own(segment);
      this._segWidget.push(segment);
    },

    // .........................................
    _onSegmentDeleted: function(item)
    {
      var payload = this.getContext().payload;
      var refList = CampaignCtx.jData.getCpgnStateSegmentRefList(payload.states[0]);
      if (refList && refList.length === 1)
      {
        // // If only one segment is present then
        // // deleting it will also delete the State.
        // // User should add another segment and then delete
        // // this one.
        // var alertParams = {
        // "dialog_title": Res.dlgDeleteSegTitle,
        // "dialog_msg": Res.alertOneTargetGroupRequired
        // };
        // var alertDialog = new AlertBox(alertParams);
        // alertDialog.startup();
        // alertDialog.setParent(this);
        // this.own(alertDialog);
        // return;
      }

      var delCallback = function()
      {
        this._deleteJourneySegment(item);
      };

      var dialogParams = {
        "dialog_title": Res.dlgDeleteSegTitle,
        "dialog_msg": Res.dlgDeleteSegMsg + " '" + item.name + "'?"
      // "dialog_msg": "Delete " + "'" + item.name + "'?"
      };
      var dialog = new DeleteDialogBox(dialogParams);
      dialog.startup();
      dialog.setParent(this);
      dialog.setCallback(delCallback);
    },

    /**
     * Callback when the segment(journey state) is being deleted . Update the local array. Show the
     * add segment button if the journey states are less than 5. destroy the exisiting widgets and
     * re-create new ones update the JCtx and save to backend
     */
    _deleteJourneySegment: function(item)
    {
      if (!item || !item.id) return;
      var payload = this.getContext().payload;
      CampaignCtx.jData.deleteCpgnStateSegmentRef(payload.states[0], item.id);
      CampaignCtx.jData.setJourneyComplete(false);
      // Save
      CampaignCtx.jData.saveData(true).then(lang.hitch(this, function(data)
      {
        if (data && data.status === "fail")
        {
          var msg = "";
          if (data.data && data.data.reason)
          {
            msg = data.data.reason;
          }
          udc.error("save failed. " + msg);
          udc.log(data);
        }
        // remove segment data item from array
        if (this._jItems && this._jItems.length > 0)
        {
          for (var i = (this._jItems.length - 1); i > -1; i--)
          {
            if (this._jItems[i].id === item.id)
            {
              var sItem = this._jItems[i];
              this._jItems.splice(i, 1);
              break;
            }
          }
        }

        this.destroySegments();

        if(!this._isEditMode)
        {
          // new Campaign
          // change mode from 'create' to 'edit'
          this._isEditMode = true;
          // set local campaign id
          this._journeyId = CampaignCtx.jData.getId();
        }

        // reload - segments
        this._prepareWhoForUI();
        this._togglePublishBtn();
        if (this._jItems.length == 0)
        {
          this._showPublishBtn(false);
        }

        // if (this._isEditMode)
        // {
        // this.journeyCircleChart.innerHTML = "";
        // this.journeyBarChart.innerHTML = "";
        //
        // this._drawUserChart();
        // }
        // clear State related items
        if (item && item.id)
        {
          topic.publish("/campaign/segment/delete", item);
        }
      }), lang.hitch(this, function(error)
      {
        udc.error("save failed. ");
        udc.log(error);
      }));
    },

    /**
     * Hide and show add btn which allows users to add segments(journey states) Dont allow more than
     * 5 segments to be added.
     */
    _toggleAddSegBtn: function()
    {
      if (this._jItems.length == 5)
      {
        domClass.add(this.addJourneyStepsBtn, "c3_hide");
      }
      else
      {
        domClass.remove(this.addJourneyStepsBtn, "c3_hide");
      }
    },

    // .......................................
    _selectSegment: function()
    {
      var refList = [];
      var payload = this.getContext().payload;
      if (payload && payload.states && payload.states.length)
      {
        refList = CampaignCtx.jData.getCpgnStateSegmentRefList(payload.states[0]);

        // Check that the Segment does not exist in Final Target segments.
        if(payload && payload.states[1])
        {
          var refListFinal = CampaignCtx.jData.getCpgnStateSegmentRefList(payload.states[1]);
          if(refListFinal && refListFinal.length)
          {
            for(var i = 0; i < refListFinal.length; i++)
            {
              refList.push(refListFinal[i]);
            }
          }
        }
      }

      var selectSegmentDlg = new SelectSegment({
        _parent: this,
        dialog_title: Res.selectSegmentDlgTtl,
        // do not show items in this list.
        // filterList: this._segmentGroup ? this._segmentGroup.segments : []
        filterList: refList
      });
      selectSegmentDlg.setParent(this);
      // selectSegmentDlg.setCurrentSteps(this._jItems);
      selectSegmentDlg.setCallback(this._onSegmentSelected);
      selectSegmentDlg.startup();
      this.own(selectSegmentDlg);
    },

    /**
     * Callback when a segment has been selected and needs to be shown in the swimlane
     */
    // ........................................
    // Callback - segment selected in dialog
    // Add it to the goal.
    _onSegmentSelected: function(ctx)
    {
      // 1. State exists.
      // 2. Add Segment to Customer Group
      // this._segmentGroup.segments.push(ctx.item.id);
      var payload = this.getContext().payload;
      if (!payload) return;
      if (!payload.states || !payload.states.length)
      {
        // create state
        CampaignCtx.jData.createJourneyState(ctx.item);
      }
      else
      {
        CampaignCtx.jData.addCpgnStateSegmentRef(payload.states[0], ctx.item.id);
      }

      // 3. show in UI
      // this._createSegmentItem(ctx.item);
      // This makes an extra call to segmentStats with old data. Incorrect Stats.
      // Fix is to call segmentStats again in saveData().then( .. ) block
      // FIXME separate segmentStats call from _prepareWhoForUI().
      this._prepareWhoForUI();
      // 4. Save data
      this._showPublishBtn(true);
      // CampaignCtx.jData.setGoalComplete(false);
      CampaignCtx.jData.saveData().then(lang.hitch(this, function(data)
      {
        if (data && data.status === "fail")
        {
          var msg = "";
          if (data.data && data.data.reason)
          {
            msg = data.data.reason;
          }
          udc.error("save failed. " + msg);
          udc.log(data);
        }

        if(!this._isEditMode)
        {
          // new Campaign
          // change mode from 'create' to 'edit'
          this._isEditMode = true;
          // set local campaign id
          this._journeyId = CampaignCtx.jData.getId();
        }

        // Update Journey data
        // CampaignCtx.jData.updateWhoItem(this._segmentGroup);
        this._togglePublishBtn();

        this._jItems.push(ctx.item);
        // if (ctx.mode === "create")
        // {
        // this._showJourneyStats("create");
        // }
        // Update - stats.
        this._setStatesContent();
      }), lang.hitch(this, function(error)
      {
        udc.error("save failed. ");
        udc.log(error);
      }));

    },

    // .....................................
    // .....................................
    // FINAL OUTCOME states[1]

    // .................................
    _prepareFinalWhoForUI: function()
    {
      var jData = this.getContext();
      var state;
      var refList = [];
      if (!jData || !jData.payload || !jData.payload.states || jData.payload.states.length < 2)
      {
        return;
      }
      // Assumption: In Campaign 'outcome state' is stored in 2nd states ( states[1] )
      state = jData.payload.states[1];
      refList = CampaignCtx.jData.getCpgnStateSegmentRefList(state);

      // reset palette list
      domConstruct.empty(this.finalSegmentCont);
      // this.destroySegments();
      this._finalSegmentList = [];

      for (var i = 0; i < refList.length; i++)
      {
        // Copy matched Segment palette item data
        // to _finalSegmentList []

        var seg;
        if (refList[i] === CampaignCtx.jData.allUsers.id)
        {
          seg = C3Context.goalData.copyJObj(CampaignCtx.jData.allUsers);
        }
        else
        {
          seg = C3Context.goalData.getPaletteItemById(refList[i]) || {
            id: refList[i]
          };
        }
        this._finalSegmentList.push(C3Context.goalData.copyJObj(seg));
      }

      // merge with Stats data
      this._fetchWhoStats({
        isFinalSegments: true
      });
    },

    // ..................................
    _showOutcomeWho: function()
    {
      if (this._finalSegmentList && this._finalSegmentList.length)
      {
        for (var i = 0; i < this._finalSegmentList.length; i++)
        {
          // Create Segment UI item
          this._createFinalSegmentItem(this._finalSegmentList[i]);
        }

        this._showPublishBtn(true);
        this._togglePublishBtn();
      }
    },

    // ........................................
    // Create UI for segment box - campaign
    // targets item
    _createFinalSegmentItem: function(item)
    {
      var segment = new CampaignSegmentItem({
        _parent: this,
        jItem: item
      });
      segment.setParent(this);
      segment.setDeleteCallback(this._onFinalSegmentDeleted);
      segment.startup();
      segment.domNode.setAttribute("data-ref", item.id);
      // domConstruct.place(segment.domNode, this.addSegmentBtn, "before");
      domConstruct.place(segment.domNode, this.finalSegmentCont, "last");
      this.own(segment);
      // this._segWidget.push(segment);

      // hide add button
      this._showAddFinalSegBtn(false);
    },

    // .........................................
    _onFinalSegmentDeleted: function(item)
    {
      var payload = this.getContext().payload;
      var refList = CampaignCtx.jData.getCpgnStateSegmentRefList(payload.states[1]);
      // if(refList && refList.length === 1)
      // {
      // // If only one segment is present then
      // // deleting it will also delete the State.
      // // User should add another segment and then delete
      // // this one.
      // var alertParams = {
      // "dialog_title": Res.dlgDeleteSegTitle,
      // "dialog_msg": Res.alertOneTargetGroupRequired
      // };
      // var alertDialog = new AlertBox(alertParams);
      // alertDialog.startup();
      // alertDialog.setParent(this);
      // this.own(alertDialog);
      // return;
      // }

      var delCallback = function()
      {
        this._deleteFinalSegment(item);
      };

      var dialogParams = {
        "dialog_title": Res.dlgDeleteSegTitle,
        "dialog_msg": Res.dlgDeleteSegMsg + " '" + item.name + "'?"
      // "dialog_msg": "Delete " + "'" + item.name + "'?"
      };
      var dialog = new DeleteDialogBox(dialogParams);
      dialog.startup();
      dialog.setParent(this);
      dialog.setCallback(delCallback);
    },

    // ........................................
    _deleteFinalSegment: function(item)
    {
      //set campaign insight accessibility to false when the final segment is deleted
      this._accessFlag = false;
      
      if (!item || !item.id) return;
      var payload = this.getContext().payload;
      CampaignCtx.jData.deleteCpgnStateSegmentRef(payload.states[1], item.id);
      if(CampaignCtx.jData.isCpgnFinalStateSegmentRefListEmpty())
      {
        // If Triggered Interaction and Re-target set
        // then also delete the retarget info
        this._removeRetargetProp();
      }
      CampaignCtx.jData.setJourneyComplete(false);

      // show/hide one-time-campaign valid days field
      this._setOTCValidityDays();
      // if conversion segments deleted then re-populate the re-target
      this._populateRetarget();

      // Save
      CampaignCtx.jData.saveData(true).then(lang.hitch(this, function(data)
      {
        if (data && data.status === "fail")
        {
          var msg = "";
          if(data.data && data.data.reason)
          {
            msg = data.data.reason;
          }
          udc.error("delete save failed. " + msg);
          udc.log(data);
        }

        if(!this._isEditMode)
        {
          // new Campaign
          // change mode from 'create' to 'edit'
          this._isEditMode = true;
          // set local campaign id
          this._journeyId = CampaignCtx.jData.getId();
        }

        //deferr.resolve({
        //  pass: true
        //});
      }), lang.hitch(this, function(error)
      {
        udc.error("retarget save failed. ");
        udc.log(error);
        //deferr.reject(error);
      }));


      // this.destroySegments();
      domConstruct.empty(this.finalSegmentCont);
      // reload - segments
      this._prepareFinalWhoForUI();
      this._showAddFinalSegBtn(true);
      this._togglePublishBtn();
      if (this._jItems.length == 0)
      {
        // Comment-out, allow Publish with 0 Final Segments.
        // this._showPublishBtn(false);
      }

      // clear State related items
      if (item && item.id)
      {
        topic.publish("/campaign/final/segment/delete", item);
      }

      // show/hide re-target
      this._showHideRetargetCont();
      this._showOrHideRetargetOptions();
    },

    // .............................................
    _showAddFinalSegBtn: function(showIt)
    {
      if (showIt)
      {
        // below commented - allow adding multiple conversion segments.
        //domClass.remove(this.addFinalSegmentBtn, "c3_hide");
        domClass.add(this.finalSegmentCont, "c3_hide");
      }
      else
      {
        // below commented - allow adding multiple conversion segments.
        //domClass.add(this.addFinalSegmentBtn, "c3_hide");
        domClass.remove(this.finalSegmentCont, "c3_hide");
      }
    },

    // .......................................
    _selectFinalSegment: function()
    {
      var refList = [];
      var payload = this.getContext().payload;
      if (payload && payload.states && payload.states.length > 1)
      {
        refList = CampaignCtx.jData.getCpgnStateSegmentRefList(payload.states[1]);
      }

      // Do not show 'All Users' in Final Target segments list.
      refList.push(CampaignCtx.jData.allUsers.id);

      // Check that the Segment does not exist in Starting Target segments.
      if(payload && payload.states && payload.states[0])
      {
        var refList0 = CampaignCtx.jData.getCpgnStateSegmentRefList(payload.states[0]);
        if(refList0 && refList0.length)
        {
          for(var i = 0; i < refList0.length; i++)
          {
            refList.push(refList0[i]);
          }
        }
      }

      var selectSegmentDlg = new SelectSegment({
        _parent: this,
        dialog_title: Res.selectSegmentDlgTtl,
        // do not show items in this list.
        // filterList: this._segmentGroup ? this._segmentGroup.segments : []
        filterList: refList
      });
      selectSegmentDlg.setParent(this);
      selectSegmentDlg.setCallback(this._onFinalSegmentSelected);
      selectSegmentDlg.startup();
    },

    /**
     * Callback when a segment has been selected and needs to be shown in the swimlane
     */
    // ........................................
    // Callback - segment selected in dialog
    // Add it to the goal.
    _onFinalSegmentSelected: function(ctx)
    {
      // 1. State exists.
      // 2. Add Segment to Customer Group
      // this._segmentGroup.segments.push(ctx.item.id);
      var payload = this.getContext().payload;
      if (!payload) return;
      if (!payload.states || !payload.states.length || payload.states.length < 1)
      {
        // First add states[0] entry for the Target Segments
        // and then add states[1] entry for Conversion Segments.
        this._alert({text: Res["msgCreateTargetSegmentsFirst_" + this._usedIn]});
        return;
      }
      else if (!payload.states || !payload.states.length || payload.states.length < 2)
      {
        // create state
        CampaignCtx.jData.createJourneyState(ctx.item);
      }
      else
      {
        CampaignCtx.jData.addCpgnStateSegmentRef(payload.states[1], ctx.item.id);
      }

      // UI
      // show one-time-campaign valid days field
      this._setOTCValidityDays();

      // 3. show in UI
      // this._createSegmentItem(ctx.item);
      this._prepareFinalWhoForUI();
      // 4. Save data
      this._showPublishBtn(true);
      // CampaignCtx.jData.setGoalComplete(false);
      CampaignCtx.jData.saveData().then(lang.hitch(this, function(data)
      {
        if (data && data.status === "fail")
        {
          var msg = "";
          if(data.data && data.data.reason)
          {
            msg = data.data.reason;
          }
          udc.error("add conversion segment save failed. " + msg);
          udc.log(data);
        }

        if(!this._isEditMode)
        {
          // new Campaign
          // change mode from 'create' to 'edit'
          this._isEditMode = true;
          // set local campaign id
          this._journeyId = CampaignCtx.jData.getId();
        }

        //deferr.resolve({
        //  pass: true
        //});
      }), lang.hitch(this, function(error)
      {
        udc.error("add conversion segment save failed. ");
        udc.log(error);
        //deferr.reject(error);
      }));

      this._togglePublishBtn();
      
      // 5. set campaign insight accessibility to true when the final segment is selected
      this._accessFlag = true;

      this._jItems.push(ctx.item);

      // UI
      // show one-time-campaign valid days field
      //this._setOTCValidityDays();

      // Re-target UI
      this._showHideRetargetCont();
      this._showOrHideRetargetOptions();
    },

    //.............................................
    // Check if Final/Conversion segments exists
    _isConversionSegmentsExists: function()
    {
      return CampaignCtx.jData.isConversionSegmentsExists();
    },

    // end - FINAL OUTCOME states[1]
    // .....................................
    // .....................................


    //...................................
    // One Time Campaign - Validity Days
    // How many days the one-time-campaign is valid.
    // The campaign is considered ended after this
    // number of days and the data collected after words
    // is no longer used in statistics.
    // If no value is set then backend will use
    // default value of 7 days.
    _setOTCValidityDays: function()
    {
      if(this._usedIn && this._usedIn !== CampaignCtx.consts.usedIn.CAMPAIGNONCE)
      {
        // Field is used only in One-time-campaign
        domClass.add(this._1tcValidityCont, "c3_hide");
        return;
      }

      var payload = this.getContext().payload;
      if (!payload) return;
      if (!payload.states || !payload.states.length || payload.states.length < 1)
      {
        // State states[0] doesn't exists. Target Segments
        return;
      }
      else if (!payload.states || !payload.states.length || payload.states.length < 2)
      {
        // states[1] for final segments/conversions does not exists.
        //~
        return;
      }

      // For One-time-campaign, check if there are
      // final/conversion segments (.states[1]).
      // If final/conversion segments exists then
      // allow to specify campaign validity days
      // If no final/conversion segments specified then
      // do nothing
      var isFinalSegmentsExists = this._isConversionSegmentsExists();
      if(isFinalSegmentsExists)
      {
      }
      else
      {
        // no final/conversion segments specified
        // hide the input field
        domClass.add(this._1tcValidityCont, "c3_hide");
        // if params[{name: "z1.otc.valid.days",..},] is there then delete it
        this._removeOTCValidityDaysParams();
        return;
      }

      domClass.remove(this._1tcValidityCont, "c3_hide");

      // Set value if params[n].name === "z1.otc.valid.days" exists
      //~

      if(!payload.params)
      {
        payload.params = [];
      }
      var flgFound = -1;
      for(var i = 0; i < payload.params.length; i++)
      {
        if(payload.params[i] && payload.params[i].name && payload.params[i].name === "z1.otc.valid.days")
        {
          flgFound = i;
          break;
        }
      }
      if(flgFound !== -1 && payload.params[flgFound])
      {
        // populate
        this._1tcValidityDays.value = payload.params[flgFound].value;
      }
      else
      {
        // set a default validity days
        this._1tcValidityDays.value = this._defaultValidityDays;
        payload.params.push({
          "name": "z1.otc.valid.days",
          "value": this._1tcValidityDays.value
        });
      }

    },

    //.....................................
    // If Final/conversion segments are deleted
    // then remove the params["z1.otc.valid.days"]
    _removeOTCValidityDaysParams: function()
    {
      var payload = this.getContext().payload;
      if (!payload) return;

      if(!payload.params)
      {
        payload.params = [];
      }
      var flgFound = -1;
      for(var i = 0; i < payload.params.length; i++)
      {
        if(payload.params[i] && payload.params[i].name && payload.params[i].name === "z1.otc.valid.days")
        {
          flgFound = i;
          break;
        }
      }
      if(flgFound !== -1 && payload.params[flgFound])
      {
        // remove property "z1.otc.valid.days"
        payload.params.splice(flgFound, 1);
      }
    },

    //...................................
    _onChangeOTCValidityDays: function()
    {
      // save campaign data
      if(this._usedIn && this._usedIn !== CampaignCtx.consts.usedIn.CAMPAIGNONCE)
      {
        // Field is used only in One-time-campaign
        return;
      }

      var payload = this.getContext().payload;
      if (!payload) return;
      var isFinalSegmentsExists = this._isConversionSegmentsExists();
      if(isFinalSegmentsExists)
      {
      }
      else
      {
        // no final/conversion segments specified
        // hide the input field
        domClass.add(this._1tcValidityCont, "c3_hide");
        return;
      }

      // validate
      var dVal = this._1tcValidityDays.value;
      if(dVal === void(0) || isNaN(dVal) || dVal === "" || dVal === 0)
      {
        // accept only non-zero values
        this._alert({text: "Enter days as a number value."});
        return;
      }

      if(!payload.params)
      {
        payload.params = [];
      }
      var flgFound = -1;
      for(var i = 0; i < payload.params.length; i++)
      {
        if(payload.params[i] && payload.params[i].name && payload.params[i].name === "z1.otc.valid.days")
        {
          flgFound = i;
          break;
        }
      }
      if(flgFound !== -1 && payload.params[flgFound])
      {
        payload.params[flgFound].value = dVal;
      }
      else
      {
        payload.params.push({
          "name": "z1.otc.valid.days",
          "value": dVal
        });
      }

      if(this._readOnly)
      {
        return;
      }

      // save
      CampaignCtx.jData.saveData().then(lang.hitch(this, function(data)
      {
        if (data && data.status === "fail")
        {
          var msg = "";
          if(data.data && data.data.reason)
          {
            msg = data.data.reason;
          }
          udc.error("1tcValidity save failed. " + msg);
          udc.log(data);
        }

        if(!this._isEditMode)
        {
          // new Campaign
          // change mode from 'create' to 'edit'
          this._isEditMode = true;
          // set local campaign id
          this._journeyId = CampaignCtx.jData.getId();
        }

        //deferr.resolve({
        //  pass: true
        //});
      }), lang.hitch(this, function(error)
      {
        udc.error("1tcValidity save failed. ");
        udc.log(error);
        //deferr.reject(error);
      }));

    },

    //.....................................
    //         Re - target
    //.....................................
    //.....................................
    _showHideRetargetCont: function()
    {
      if(this._usedIn && this._usedIn !== CampaignCtx.consts.usedIn.CAMPAIGN)
      {
        return;
      }
      var isFinalSegmentsExists = this._isConversionSegmentsExists();
      //if(isFinalSegmentsExists)
      //{
        this._retargetCont.classList.remove("c3_hide");
      //}
      //else
      //{
      //  this._retargetCont.classList.add("c3_hide");
      //}
      ////this._showHideRetargetTimeCont();
    },

    //.....................................
    _showHideRetargetTimeCont: function()
    {
      //if(this._retargetSelect.value !== "timeperiod")
      if(this._retargetSelect.value !== "timeperiod" && this._retargetSelect.value !== "periodAfter1stAxn")
      {
        this._specifyRetargetTimeCont.classList.add("c3_hide");
      }
      else
      {
        this._specifyRetargetTimeCont.classList.remove("c3_hide");
      }
    },

    //......................................
    _showOrHideRetargetOptions: function()
    {
      if(this._isConversionSegmentsExists())
      {
        this._opn_now.classList.remove("c3_hide");
        this._opn_timeperiod.classList.remove("c3_hide");
      }
      else
      {
        this._opn_now.classList.add("c3_hide");
        this._opn_timeperiod.classList.add("c3_hide");

      }
    },

    //......................................
    _populateRetarget: function()
    {
      // Used only in Triggered Campaign
      if(this._usedIn && this._usedIn !== CampaignCtx.consts.usedIn.CAMPAIGN)
      {
        return;
      }

      var payload = this.getContext().payload;
      if (!payload) return;
      if (!payload.states || !payload.states.length || payload.states.length < 1)
      {
        // State states[0] doesn't exists. Target Segments
        return;
      }
      else if (!payload.states || !payload.states.length || payload.states.length < 2)
      {
        // states[1] for final segments/conversions does not exists.
        // commented as checking isFinalSegmentsExists below
        //return;
      }

      // For triggered-campaign, check if there are
      // final/conversion segments (.states[1]).
      // If final/conversion segments exists then
      // allow to specify re-target days
      // If no final/conversion segments specified then
      // delete re-target information if it exists.
      var isFinalSegmentsExists = this._isConversionSegmentsExists();
      // comment-out: now always showing the conversion section
      //if(isFinalSegmentsExists)
      //{
      //}
      //else
      //{
      //  // no final/conversion segments specified
      //
      //  // hide the re-target section
      //  this._showHideRetargetCont();
      //  return;
      //}

      this._showHideRetargetCont();
      this._showOrHideRetargetOptions();

      // Set value if params[n].name === "z1.otc.valid.days" exists
      //~

      if(!payload.params)
      {
        payload.params = [];
      }
      var flgFound = -1;
      for(var i = 0; i < payload.params.length; i++)
      {
        if(payload.params[i] && payload.params[i].name && payload.params[i].name === "z1.retarget")
        {
          flgFound = i;
          break;
        }
      }
      if(flgFound !== -1 && payload.params[flgFound])
      {
        // populate
        this._retargetString = payload.params[flgFound].value;

        if(!isFinalSegmentsExists && this._retargetString !== void(0) && this._retargetString !== ""
          && (
              this._retargetString === "now"
              || this._isTimePeriodOpn(this._retargetString)
              //|| this._isAlwaysTimePeriodOpn(this._retargetString)
            )
         )
        {
          // if Conversion segments are deleted and the
          // re-target value was conversion segments based then
          // change the value to default 'always' value.
          //~
          // set to default
          this._retargetString = "always";
          payload.params[flgFound].value = this._retargetString; 
        }

        if(this._retargetString === void(0) || this._retargetString === "")
        {
          // empty => 'always' (default)
          this._retargetSelect.value = "always";
        }
        else if(this._retargetString === "always")
        {
          // always
          this._retargetSelect.value = "always";
        }
        else if(this._retargetString === "never")
        {
          // never
          this._retargetSelect.value = "never";
        }
        else if(this._retargetString === "now")
        {
          // now
          this._retargetSelect.value = "now";
        }
        else if(this._isTimePeriodOpn(this._retargetString))
        {
          // value is in "1235d" or "12.23h" format
          // time specified
          this._retargetSelect.value = "timeperiod";
          this._showHideRetargetTimeCont();
          this._populateRetargetTimeSpecified();
        }
        else if(this._isAlwaysTimePeriodOpn(this._retargetString))
        {
          // "always://1235d" or "always://12.23h" format
          // time specified
          this._retargetSelect.value = "periodAfter1stAxn";
          this._showHideRetargetTimeCont();
          this._populateRetargetAlwaysTimeSpecified();
        }
        else 
        {
          // invalid
          udc.log("Invalid : Re-target value format - " + this._retargetString);
        }

        this._showHideRetargetTimeCont();
      }
    },

    //....................................
    // e.g. retarget value : '2m' 2 minutes
    _isTimePeriodOpn: function(val)
    {
      // check if value is in "1235d" or "12.23h" format
      var rg = /^(([0-9])+(\.([0-9])+){0,1}([a-z]))$/i;
      if(rg.test(val))
      {
        return true;
      }
      return false;
    },

    //....................................
    // e.g. retarget value : 'always://2m'
    _isAlwaysTimePeriodOpn: function(val)
    {
      // check if value is in "always://1235d" or "always://12.23h" format
      var rg = /^(always:\/\/)(([0-9])+(\.([0-9])+){0,1}([a-z])){0,1}$/i;
      if(rg.test(val))
      {
        return true;
      }
      return false;
    },

    //.....................................
    // '4m'
    _populateRetargetTimeSpecified: function()
    {
      if(!this._retargetString) return;
      var s = this._retargetString;
      var tPart = s.substring(0, s.length - 1);
      var uPart = s.substring(s.length - 1, s.length);
      // set Unit <select>
      this._tsUnit.value = uPart;
      // set value
      this._tsRetarget.value = tPart;
    },

    //.....................................
    // 'always://4m'
    _populateRetargetAlwaysTimeSpecified: function()
    {
      if(!this._retargetString) return;
      var s = this._retargetString.replace(this._ALWAYS, "");
      var tPart = s.substring(0, s.length - 1);
      var uPart = s.substring(s.length - 1, s.length);
      // set Unit <select>
      this._tsUnit.value = uPart;
      // set value
      this._tsRetarget.value = tPart;
    },

    //....................................
    // Used only in Triggered Campaign
    // call this if anything related to
    // re-target is changed
    _saveRetargetChanges: function()
    {
      // Used only in Triggered Campaign
      if(this._usedIn && this._usedIn !== CampaignCtx.consts.usedIn.CAMPAIGN)
      {
        return;
      }

      var payload = this.getContext().payload;
      if (!payload) return;
      if (!payload.states || !payload.states.length || payload.states.length < 1)
      {
        // State states[0] doesn't exists. Target Segments
        return;
      }
      else if (!payload.states || !payload.states.length || payload.states.length < 2)
      {
        // states[1] for final segments/conversions does not exists.
        // commented as checking isFinalSegmentsExists below
        //return;
      }

      // For triggered-campaign, check if there are
      // final/conversion segments (.states[1]).
      // If final/conversion segments exists then
      // allow to specify re-target days
      // If no final/conversion segments specified then
      // delete re-target information if it exists.
      var isFinalSegmentsExists = this._isConversionSegmentsExists();
      //// commented-out
      //if(isFinalSegmentsExists)
      //{
      //}
      //else
      //{
      //  // no final/conversion segments specified
      //
      //  // FIXME - if re-target data exists then delete it.
      //  // If Final Segment was deleted then delete the re-target info
      //  this._removeRetargetProp();
      //
      //  // save ?
      //  //~
      //
      //  // hide the re-target section
      //  this._showHideRetargetCont();
      //  return;
      //}

      this._showHideRetargetCont();

      // get retarget value to be saved.
      this._retargetString = "";

      if(this._retargetSelect.value === "always")
      {
        // always
        this._retargetString = "always";
      }
      else if(this._retargetSelect.value === "never")
      {
        // never
        this._retargetString = "never";
      }
      else if(this._retargetSelect.value === "now")
      {
        // now
        this._retargetString = "now";
      }
      else if(this._retargetSelect.value === "periodAfter1stAxn")
      {
        // after time period after first action
        // 'always://1m'
        //
        // time specified
        var rTS = this._getRetargetTimeSpecifiedString();
        if(!this._validateRetargetTimeSpecifiedString(rTS))
        {
          // time specified is invalid
          return;
        }
        this._retargetString = this._ALWAYS + rTS;
      }
      else if(this._retargetSelect.value === "timeperiod")
      {
        // '1m'
        //
        // time specified
        var rTS = this._getRetargetTimeSpecifiedString();
        if(!this._validateRetargetTimeSpecifiedString(rTS))
        {
          // time specified is invalid
          return;
        }
        this._retargetString = rTS;
      }

      if(!payload.params)
      {
        payload.params = [];
      }
      var flgFound = -1;
      for(var i = 0; i < payload.params.length; i++)
      {
        if(payload.params[i] && payload.params[i].name && payload.params[i].name === "z1.retarget")
        {
          flgFound = i;
          break;
        }
      }
      if(!this._retargetString && flgFound !== -1)
      {
        // 'never'
        // delete the params["z1.retarget"] property
        this._removeRetargetProp();
      }
      else if(flgFound !== -1 && payload.params[flgFound])
      {
        payload.params[flgFound].value = this._retargetString;
      }
      else
      {
        payload.params.push({name: "z1.retarget", value: this._retargetString});
      }

      // save
      CampaignCtx.jData.setJourneyComplete(false);
      CampaignCtx.jData.saveData().then(lang.hitch(this, function(data)
      {
        if (data && data.status === "fail")
        {
          var msg = "";
          if(data.data && data.data.reason)
          {
            msg = data.data.reason;
          }
          udc.error("retarget save failed. " + msg);
          udc.log(data);
        }

        if(!this._isEditMode)
        {
          // new Campaign
          // change mode from 'create' to 'edit'
          this._isEditMode = true;
          // set local campaign id
          this._journeyId = CampaignCtx.jData.getId();
        }

        //deferr.resolve({
        //  pass: true
        //});
      }), lang.hitch(this, function(error)
      {
        udc.error("retarget save failed. ");
        udc.log(error);
        //deferr.reject(error);
      }));
    },

    //................................................
    // Used when a retarget time period is specified
    // 1 minute => '1m'
    _getRetargetTimeSpecifiedString: function()
    {
      var ts;
      if(this._tsRetarget && (this._tsRetarget.value !== void(0) || this._tsRetarget.value.trim() !== ""))
      {
        //this._retargetString = this._tsRetarget.value + "" + this._tsUnit.value;
        ts = this._tsRetarget.value + "" + this._tsUnit.value;
      }
      return ts;
    },

    //................................................
    // Used when a retarget time period is specified
    // 1 minute => '1m'
    _validateRetargetTimeSpecifiedString: function(ts)
    {
      if((ts !== void(0) || ts !== ""))
      {
        // check if value is in "1235d" or "12.23h" format
        // 0 not allowed.
        var rg = /^(([1-9])+(\.([0-9])+){0,1}([a-z]))$/i;
        if(rg.test(ts))
        {
          return true;
        }
        else
        {
          this._alert({text: Res.vInvalidRetargetValue});
          return false;
        }
      }
      else
      {
        return false;
      }
      return true;
    },

    //....................................
    _removeRetargetProp: function()
    {
      var payload = this.getContext().payload;
      if (!payload) return;
      if(!payload.params)
      {
        return;
      }
      var flgFound = -1;
      for(var i = 0; i < payload.params.length; i++)
      {
        // find index of 'z1.target'
        if(payload.params[i] && payload.params[i].name && payload.params[i].name === "z1.retarget")
        {
          flgFound = i;
          break;
        }
      }
      if(flgFound !== -1 && payload.params[flgFound]
        && (
            payload.params[flgFound] === "now"
            || this._isTimePeriodOpn(payload.params[flgFound])
            )
      )
      {
        // remove params['z1.retarget']
        //payload.params.splice(flgFound, 1);
        //
        // if value is 'now' or 'timePeriod' type then overwrite
        // with default one.
        // instead of deleting overwrite it with default value 'always'
        payload.params[flgFound].value = "always";

        // saving campaign where this method is called from
        //~
      }
    },

    //.....................................
    //         /end : Re - target
    //.....................................

    // .....................................
    deleteActionItem: function(ctx)
    {
      if (!ctx || !ctx._dItem) return;
      this.saveItem(ctx);
    },

    // ............................
    deleteItem: function(ctx)
    {
      var deferr = new Deferred();

      if (!ctx || ctx._timerIndex === void (0))
      {
        deferr.reject({
          pass: false,
          message: "No ctx or timerIndex specified"
        });
        return deferr.promise;
      }

      let timersOrActivities = CampaignCtx.jData.getTimersOrActivities();

      if (ctx._state && ctx._state[timersOrActivities])
      {
        // splice in .saveItem()
        this.saveItem(ctx).then(lang.hitch(this, function(data)
        {

          deferr.resolve({
            pass: true
          });

        }), lang.hitch(this, function(error)
        {
          udc.error(error);
          deferr.reject(error);
        }));
      }
      else
      {
        deferr.resolve({
          pass: true
        });
      }
      return deferr.promise;
    },

    // .....................................
    saveItem: function(ctx = {})
    {
      // Generic Save() to save Action data
      // in appropriate 'states[i].timers[]' or states[i].activities[]
      // Flags (in ctx):
      // edit|new
      // usedIn: campaign|journey
      // campaign => states[0]
      // timerIndex
      // actionType - indicates timers or activities
      // ~

      var deferr = new Deferred();

      // put the changes in main data structure.

      var timersOrActivities = "timers";
      if (ctx.actionType === CampaignCtx.consts.actionType.TIME)
      {
        timersOrActivities = "timers";
      }
      else if (ctx.actionType === CampaignCtx.consts.actionType.ACTIVITY)
      {
        timersOrActivities = "activities";
      }
      if(ctx._isABCompositeAction)
      {
        
      }
      var timersOrActivitiesOrig = "timers";
      if (ctx.actionTypeOrig && ctx.actionTypeOrig === CampaignCtx.consts.actionType.TIME)
      {
        timersOrActivitiesOrig = "timers";
      }
      else if (ctx.actionTypeOrig && ctx.actionTypeOrig === CampaignCtx.consts.actionType.ACTIVITY)
      {
        timersOrActivitiesOrig = "activities";
      }

      if (ctx._mode === "new")
      {
        if (ctx._dItem && ctx._state)
        {
          ctx._state[timersOrActivities].push(ctx._dItem);
        }
      }
      else if (ctx._mode === "edit")
      {
        if (ctx._dItem && ctx._timerIndex !== void (0))
        {
          if(ctx._isABCompositeAction)
          {
            // ~
          }
          // check if action type (timer or activities) is changed.
          if(ctx.actionTypeOrig && ctx.actionType !== ctx.actionTypeOrig)
          {
            // 1. save to timers[] or activities[] as new action
            ctx._state[timersOrActivities].push(ctx._dItem);
            // 2. delete old action
            ctx._state[timersOrActivitiesOrig].splice(ctx._timerIndex, 1);
          }
          else
          {
            ctx._state[timersOrActivities][ctx._timerIndex] = ctx._dItem;
          }
        }
      }
      else if (ctx._mode === "delete")
      {
        if (ctx._dItem && ctx._timerIndex !== void (0))
        {
          // 1. compositeAction[i] - delete if it's ABCompositeAction
          if(ctx._isABCompositeAction)
          {
            if(ctx._dItem && ctx._dItem.actions && ctx._dItem.actions[0] && ctx._dItem.actions[0].name)
            {
              var abAxnName = ctx._dItem.actions[0].name;
              if(abAxnName && abAxnName !== "")
              {
                var abAxnIndex = CampaignCtx.jData.findCompositeActionsItemIndexByName(abAxnName);
                var campData = CampaignCtx.jData.getData();
                campData.payload = campData.payload || {};
                campData.payload.compositeActions = campData.payload.compositeActions || [];
                // delete compositeAction item
                campData.payload.compositeActions.splice(abAxnIndex, 1);
              }
            }
          }

          // 2.
          // check if action type (timer or activities) is changed before
          // clicking delete.
          if(ctx.actionTypeOrig && ctx.actionType !== ctx.actionTypeOrig)
          {
            ctx._state[timersOrActivitiesOrig].splice(ctx._timerIndex, 1);
          }
          else
          {
            ctx._state[timersOrActivities].splice(ctx._timerIndex, 1);
          }
        }
      }

      // SAVE
      CampaignCtx.jData.saveData().then(lang.hitch(this, function(data)
      {
        // udc.success("Saved");
        // redraw activities
        // this._parent.updateActivities();

        if (ctx && typeof (ctx.callbackFn) === 'function')
        {
          // NOTE: use
          // saveActionItem().then( function(){..do stuff here after save })
          // instead of callbackFn()

          // callback
          ctx.callbackFn(ctx);
        }

        if(!this._isEditMode)
        {
          // new Campaign
          // change mode from 'create' to 'edit'
          this._isEditMode = true;
          // set local campaign id
          this._journeyId = CampaignCtx.jData.getId();
        }

        this._showActions();
        // ui publish state
        this.showPublishState(true);

        deferr.resolve({
          pass: true
        });
      }), lang.hitch(this, function(error)
      {
        udc.error(error);
        deferr.reject(error);
      }));
      return deferr.promise;
    },

    // .....................................
    editActionItem: function(ctx)
    {
      if(this._readOnly)
      {
        return;
      }
      if (!ctx || !ctx._dItem) return;
      var editCampaignAction = new EditCampaignAction({
        _jItem: ctx._jItem || void (0),
        // item data
        _dItem: ctx._dItem,
        _timerIndex: ctx._timerIndex,
        _isABCompositeAction: ctx._isABCompositeAction,
        // Commented, 'edit' mode means 'state[i]'
        // already exists, no need to call getSelectedState()
        // _state: this.getSelectedState(),
        _state: ctx._state,
        _usedIn: this._usedIn,
        _mode: "edit",
        actionList: ctx.actionList,
        actionType: ctx.actionType,
        lastPublished: this._journeyItem.lastPublished,
        _parent: this
      });
      editCampaignAction.startup();
      this.own(editCampaignAction);
    },

    // ...........................
    newActionItem: function()
    {
      // Check if the 'state[i]' exists in data structure.
      var stateSel = this.getSelectedState();
      if(!stateSel)
      {
        udc.info("Cannot add action. Add journey-step/campaign-segment first");
        return;
      }

      //--
      // Case: OTC with Schedule already specified
      // The Schedule information is saved in timers[0]
      // so an empty Action block already exists.
      // And in OTC we have only 1 timers => timers[0]
      // call 'edit' instead
      if (this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGNONCE
          && stateSel.timers && stateSel.timers[0]
      )
      {
        this.editActionItem({
          _dItem: stateSel.timers[0],
          actionList: stateSel.timers,
          _timerIndex: 0,
          _state: stateSel,
          _isABCompositeAction: false,
          actionType: CampaignCtx.consts.actionType.TIME,
          _mode: "edit"
        });
        return;
      }
      //-- end

      var pObj = {
        _jItem: this._jItem || void (0),
        // undefined item for new
        _dItem: void (0),
        // _timerIndex: this.state.timers.length,
        // Case 1 Any actions other than ABCompositeAction
        // Case 2 ABCompositeAction is selected by
        //        user.
        _state: this.getSelectedState(),
        _usedIn: this._usedIn,
        _mode: "new",
        lastPublished: this._journeyItem.lastPublished,
        _parent: this
      };

      if(this._usedIn && this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGNONCE)
      {
        // set Action type to Timer.
        pObj.actionType = CampaignCtx.consts.actionType.TIME;
      }

      // NEW - Action item
      var editCampaignAction = new EditCampaignAction(pObj);
      editCampaignAction.startup();
      this.own(editCampaignAction);
    },

    // .........................................
    onMatchAllActions: function(isMatchAll)
    {
      // button ui
      if (isMatchAll)
      {
        this.matchAllBtn.dataset.match = "true";
        domClass.remove(this.iconMatchAllBtn, "fa-square-o");
        domClass.add(this.iconMatchAllBtn, "fa-check-square-o");
      }
      else
      {
        this.matchAllBtn.dataset.match = "false";
        domClass.add(this.iconMatchAllBtn, "fa-square-o");
        domClass.remove(this.iconMatchAllBtn, "fa-check-square-o");
      }

      // FIXME - this.matchAll data.
      // ~
    },

    // .................................
    onTimeType: function()
    {
      if (this.tsTimeType.value === "dateRange")
      {
        domClass.remove(this.dateRangeBox, "c3_hide");
      }
      else if (this.tsTimeType.value === "Daily")
      {
        domClass.add(this.dateRangeBox, "c3_hide");
      }
    },

    // ..............................
    // show timer data
    showTimerData: function()
    {
      // campaignonce OTC
      if(this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGNONCE)
      {
        var tCtx;
        var payload = this.getContext().payload;
        if (payload && payload.states && payload.states.length)
        {
          if(payload.states[0].timers[0] !== void(0) && payload.states[0].timers[0].value)
          {
            tCtx = {
              "unit": payload.states[0].timers[0].unit,
              "value": payload.states[0].timers[0].value
            };
          }
        }
        if(tCtx)
        {
          if(tCtx.unit === "weekDays")
          {
            var wd = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

            // weekday value:0,2,3|1830
            // here multiple days are selected 0, 2, 3
            // 1830 - 24hr hhmm time format.
            var weekDaysString = tCtx.value;
            if(!weekDaysString)
            {
              return;
            }
            // backend keeps this in UTC
            // convert it to local time
            weekDaysString = C3Util.weekdayTimeFromUTC(weekDaysString);
            // remove |1830 time part
            var wDayStr = weekDaysString.substring(0, weekDaysString.indexOf("|"));
            var arDays = wDayStr.split(",");
            var strW = "";
            var fTimeStr = "";
            var arWStr = [];
            for(var i = 0; i < arDays.length; i++)
            {
              arWStr.push(wd[arDays[i]]);
            }
            strW = arWStr.join(", ");
            // validate time part
            if(weekDaysString.indexOf("|") >= 0)
            {
              var timeStr = weekDaysString.substring(weekDaysString.indexOf("|") + 1);
              if(timeStr != "" && timeStr !== void(0) && timeStr.length === 4)
              {
                // hhmm
                fTimeStr = this._formatTimeStr(timeStr);
                //// convert UTC to local date
                ////var timeFromUTC = C3Util.hhmmFromUTC(timeStr);
                //// No date part, only hhmm so use local time
                //var timeFromUTC = timeStr;
                //
                //// set hh:mm in UI
                //var h = timeFromUTC.substring(0, 2);
                //var m = timeFromUTC.substring(2, 4);
                //if(Number(h) >= 12)
                //{
                //  this.tsAmPm.set("value", "pm");
                //  h = Number(h) - 12;
                //  if(h < 10)
                //  {
                //    h = "0" + h + "";
                //  }
                //}
                //this.tsHHMM.set("value", h + ":" + m);
              }
            }

            var dayRangeStr = "";
            this.timerData = CampaignCtx.jData.getTimerData();
            if (this.timerData && this.timerData.dayRange && this.timerData.dayRange.end)
            {
              var d1Str = "";
              var d2Str = "";
              var d1 = this.timerData.dayRange.start;
              if(d1 && d1.length > 7) d1Str = d1.substring(0, 4) + "-" + d1.substring(4, 6) + "-" + d1.substring(6, 8);
              var d2 = this.timerData.dayRange.end;
              if(d2 && d2.length > 7) d2Str = d2.substring(0, 4) + "-" + d2.substring(4, 6) + "-" + d2.substring(6, 8);
              // day-range with weekdays
              dayRangeStr = "<label>" + Res.fromDate + "</label>"
                + "<span class='c3_timerControl_out_v'>" + d1Str + "</span> "
                + "<label>" + Res.toDate + "</label>" + "<span class='c3_timerControl_out_v'>"
                + d2Str + "</span>";
            }

            this.timeValueContOTC.innerHTML = "<label class='c3_timerControl_out_lbl'>" + Res.timerOnWeekdays + "</label> "
            + "<span class='c3_timerControl_out_v'>" + strW + fTimeStr + "</span> "
            + dayRangeStr
            + "<span class='c3_timerControl_out_v_chg'>[change]</span>";

          }
          else if(tCtx.unit === "on")
          {
            if(tCtx.value === "now")
            {
              this.timeValueContOTC.innerHTML = "<label class='c3_timerControl_out_lbl'>" + Res.timerOnNow + "</label> "
              + "<label class='c3_timerControl_out_v_chgOr'>" + Res.timerOnNowOr + "</label>"
              + "<span class='c3_timerControl_out_v_chg'>" + Res.timerOnNow2 + "</span>";
            }
            else if(tCtx.value !== void(0) && tCtx.value.length === 12)
            {
              // particular date
              var dt = tCtx.value;
              var dtStr = dt.substring(0, 4) + "-" + dt.substring(4, 6) + "-" + dt.substring(6, 8);
              var timeStr = dt.substring(8, 12);
              if(timeStr != "" && timeStr !== void(0) && timeStr.length === 4)
              {
                // hhmm
                fTimeStr = this._formatTimeStr(timeStr);
              }
              this.timeValueContOTC.innerHTML = "<label class='c3_timerControl_out_lbl'>" + Res.timerOnDate + "</label> "
              + "<span class='c3_timerControl_out_v'>" + dtStr + fTimeStr + "</span> "
              + "<span class='c3_timerControl_out_v_chg'>[change]</span>";
            }
          }
        }

        return;
      }

      // campaign

      if(this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGN)
      {
         // if(!this.timerData || JSON.stringify(this.timerData) === "{}")
        if (!this.timerData)
        {
          return;
        }
        if (this.timerData.dayRange)
        {
          this.timeValueCont.innerHTML = "<label>" + Res.fromDate + "</label>"
            + "<span class='c3_timerControl_out_v'>" + this.timerData.dayRange.start + "</span> "
            + "<label>" + Res.toDate + "</label>" + "<span class='c3_timerControl_out_v'>"
            + this.timerData.dayRange.end + "</span><span class='c3_timerControl_out_v_chg'>[change]</span>";
        }
        //else if (!CampaignCtx.jData.hasTimeInterval)
        else if ( JSON.stringify(this.timerData) === "{}")
        {
          // Everyday
          this.timeValueCont.innerHTML = "<span class='c3_timerControl_out_v'>" + Res.everyday
            + "</span><span class='c3_timerControl_out_v_chg'>[change]</span>";
        }
      }
    },

    //....................................
    // @param timeStr - 1015 or "best"
    _formatTimeStr: function(timeStr)
    {
      if(timeStr === void(0)) return "";
      if(timeStr === "best")
      {
        return " at best time";
      }
      else
      {
        // hhmm

        // convert UTC to local date
        //var timeFromUTC = C3Util.hhmmFromUTC(timeStr);
        // No date part, only hhmm so use local time
        var timeFromUTC = timeStr;

        var ampmStr = "am";

        // set hh:mm in UI
        var h = timeFromUTC.substring(0, 2);
        var m = timeFromUTC.substring(2, 4);
        if(Number(h) >= 12)
        {
          ampmStr = "pm";
          //h = Number(h) - 12;
          if(Number(h) !== 12)
          {
            h = Number(h) - 12;
          }
          if(h < 10)
          {
            h = "0" + h + "";
          }
        }
        else
        {
          ampmStr = "am";
        }
        // if hh is '00' then in display show it
        // as '12' irrespective of am/pm
        if(Number(h) === 0)
        {
          h = 12;
        }
        return " at " + h + ":" + m + " " + ampmStr;
      }
      return "";
    },

    // .....................................
    // Save campaign timer
    saveTimer: function(ctx)
    {
      var deferr = new Deferred();
      if (!CampaignCtx.jData.getId() || !ctx || !ctx.timerPayload)
      {
        udc.log("Failed. Data id not found");
        deferr.reject({
          pass: false,
          data: "Data id not found"
        });
        return deferr.promise;
      }
      CampaignCtx.jData.setTimerData(ctx.timerPayload);
      this.timerData = CampaignCtx.jData.getTimerData();
      this.showTimerData();

      deferr.resolve({
        pass: true
      });

      return deferr.promise;
    },

    // ...........................................
    // (TI) Show popup to select date/time range
    onTimerClick: function()
    {
      var cpgnName = (this.getContext()) ? this.getContext().name : Res["untitledJourney_" + this._usedIn];
      var messageText = "'" + cpgnName + "'" + Res.timerMsg;
      var timerDialog;
      if (CampaignCtx.jData.hasTimeInterval())
      {
        timerDialog = new TimerControl({
          // goalItem: goalItem
          goalItem: {},
          showTimeBox: false,
          _allDays: Res.allDaysLbl,
          title: Res["timerTitle_" + this._usedIn],
          timerMessage: messageText
        });
        timerDialog.startup();
        timerDialog.setParent(this);
        timerDialog.setCallback(this._timerCallback);
        timerDialog.setScreenValues(this.timerData);
        this.own(timerDialog);
      }
      else
      {
        timerDialog = new TimerControl({
          // goalItem: goalItem || {}
          goalItem: {},
          showTimeBox: false,
          _allDays: Res.allDaysLbl,
          title: Res["timerTitle_" + this._usedIn],
          timerMessage: messageText
        });
        timerDialog.startup();
        timerDialog.setParent(this);
        timerDialog.setCallback(this._timerCallback);
        this.own(timerDialog);
      }
    },

    // ...........................
    // (TI)
    _timerCallback: function(ctx)
    {
      if (!ctx || !ctx.timerPayload)
      {
        return;
      }
      if (!CampaignCtx.jData.getId())
      {
        // save blank campaign to get id
        var pObj = {
          name: (this.getContext()) ? this.getContext().name : Res["untitledJourney_" + this._usedIn],
          description: (this.getContext()) ? this.getContext().description : ""
        };
        this._onJourneyNameCreated(pObj).then(lang.hitch(this, function(data)
        {
          if (CampaignCtx.jData.getId())
          {
            this.saveTimer(ctx);
          }
        }), lang.hitch(this, function(er)
        {
          C3Util.handleServerError(er);
          udc.log("Failed. Data id not found");
        }));
      }
      else
      {
        this.saveTimer(ctx);
      }

    },

    // ...........................................
    // (OTC) Show popup to select date/time range
    onTimerClickOTC: function()
    {
      // get {unit, value}
      var tCtx;
      var payload = this.getContext().payload;
      if (payload && payload.states && payload.states.length)
      {
        if(payload.states[0].timers[0] !== void(0) && payload.states[0].timers[0].value
            && payload.states[0].timers[0].value !== "now")
        {
          tCtx = {
            "unit": payload.states[0].timers[0].unit,
            "value": payload.states[0].timers[0].value
          };
          if (this.timerData && this.timerData.dayRange)
          {
            tCtx.dayRange = this.timerData.dayRange;
          }
        }
      }

      var cpgnName = (this.getContext()) ? this.getContext().name : Res["untitledJourney_" + this._usedIn];
      var messageText = "'" + cpgnName + "'" + Res.timerMsg;
      var scheduleCampaignonce;
      var pCtx = {
        goalItem: {},
        _allDays: Res.allDaysLbl,
        title: Res["timerTitle_" + this._usedIn],
        timerMessage: messageText,
        _usedIn: this._usedIn,
        _mode: "new",
        _parent: this
      };
      if (tCtx)
      {
        pCtx._mode = "edit";
      }
      else
      {
      }
      scheduleCampaignonce = new ScheduleCampaignonce(pCtx);
      scheduleCampaignonce.startup();
      scheduleCampaignonce.setCallback(this._timerCallbackOTC);
      if(tCtx) scheduleCampaignonce.setScreenValues(tCtx);
      this.own(scheduleCampaignonce);
    },

    // ...........................
    // (OTC)
    // @param {
    //   unit: "on"|"weekDays"
    //   value: "now"|"201703251040"|"2,4|1040",
    //   dayRange: [optional] 2017-09 The time period during which this will run.
    //             E.g. run every Tuesday and Thursday from June 01, 18 to Sept 01, 18
    //             { start: '2015-9-15', end: '2015-10-15' }
    //             Note: dayRange is saved separately in payload2 using /timePayload api
    // }
    _timerCallbackOTC: function(ctx)
    {
      if (!ctx || !ctx.value)
      {
        return;
      }
      var tCtx = {
        // OTC - if dayRange is deleted then payload2
        // is set to {}
        //timerPayload: {}
        timerPayload: void(0)
      };
      if (ctx.dayRange && ctx.dayRange.start)
      {
        tCtx.timerPayload = {
          dayRange: ctx.dayRange
        };
      }

      // set dayRange
      CampaignCtx.jData.setTimerData(tCtx.timerPayload);

      var payload = this.getContext().payload;
      if (!payload) return;
      // Campaign
      if (!payload.states || !payload.states.length)
      {
        // create state with EMPTY 'ref'
        // No segments created so no id/ref available.
        CampaignCtx.jData.createJourneyState({
          id: ""
        });
      }
      // get updated payload structure.
      payload = CampaignCtx.jData.getData().payload;
      if(payload.states[0].timers[0] === void(0))
      {
        payload.states[0].timers.push({
          actions: []
        });
      }
      payload.states[0].timers[0].unit = ctx.unit;
      payload.states[0].timers[0].value = ctx.value;

      CampaignCtx.jData.saveData().then(lang.hitch(this, function(data){
      }),lang.hitch(this, function(err){
        udc.log(err);
      }));

      // new OTC UI
      // Schedule date is changed.
      // After clicking 'confirm' button
      // show schedule in an
      // text line format at bottom.
      this.timePeriodContOTC.classList.remove("c3_hide");

      this.showTimerData();
    },

    //....................................
    // If campaign is published then cuation user
    // that editing it will change the campaign
    // to un-published state. And when the campaign
    // is Published again, it will start from beginning
    // not where it was stopped/un-published. 
    _showPublishedCpgnAlert: function()
    {
      if (this._usedIn
        && (this._usedIn === CampaignCtx.consts.usedIn.CAMPAIGN || this._usedIn === CampaignCtx.consts.usedIn.JOURNEY))
      {
        if (this._journeyItem
          && (this._journeyItem.isComplete && !this._journeyItem.isSuspended))
        {
          udc.info(Res.cautionEditingPublishedCpgn);
        }
        else if (this._journeyItem
            && (this._journeyItem.isComplete && this._journeyItem.isSuspended))
        {
          udc.info(Res.cautionEditingSuspendedCpgn);
        }
      }
    },

    //............................................
    onSaveModuleBtn: function()
    {
      if (this._isEditMode && this._isComplete)
      {
        var payload = CampaignCtx.jData._prepareDataForSave() || "";
        var mdCtx = {
          _parent: this,
          _showInline: false,
          type: "experience",
          subtype: "c1",
          savePayload: payload
        };
        var dlg = new ModuleSaveAs(mdCtx);
        dlg.startup();
        this.own(dlg);
      }
      else
      {
        udc.alert(Res.saveTemplateWarning);
      }
    },

    // ....................................
    showOrHideOverview: function(showIt)
    {
      if (showIt)
      {
        domClass.remove(this.overviewCont, "c3_hide");
      }
      else
      {
        domClass.add(this.overviewCont, "c3_hide");
      }
    },

    // ...................................
    // Journey Overview box contents.
    // Dynamically change content here. For
    // now it's fixed.
    showOverview: function(ctx)
    {
      // ...
      if (ctx && ctx.showIt !== void (0))
      {
        this.showOrHideOverview(ctx.showIt);
      }
    },

    // ........................................
    // @param {boolean} recreateIt - [optional] 
    _showMetrics: function(recreateIt = false)
    {
      if (this._isEditMode)
      {
        if (recreateIt)
        {
          // delete it first
          if(this.campaignMetrics && typeof this.campaignMetrics.destroy === 'function')
          {
            this.campaignMetrics.destroy();
            this.campaignMetrics = void(0);
          }
        }
        let jid = this._journeyId || CampaignCtx.jData._jId || "";
        if(!this.campaignMetrics){
          this.campaignMetrics = new CampaignMetrics({
            _journeyId: jid,
            _journeyItem: this._journeyItem,
            _stepIndex: "0",
            campaignTypeName: this._usedIn,
            _usedIn: this._usedIn,
            _changeLog: this._changeLog,
            _parent: this
          });
          this.campaignMetrics.startup();
          domConstruct.place(this.campaignMetrics.domNode, this.metricsCont, 'last');
          this.own(this.campaignMetrics);
        }
      }
    },

    // .....................................
    _showInsights: function()
    {
      //show a page of options
      if (this._isEditMode)
      {
        //if there is no conversion segment selected then insights dependent on this selection should not be accessible
        //this value will be passed to CampaignInsights on click of tab3head
        if(CampaignCtx.jData.isCpgnFinalStateSegmentRefListEmpty()) this._accessible = false;
        //check accessFlag for any changes during this session
        this._accessible = this._accessFlag != null ? this._accessFlag : this._accessible;
         
        let jid = this._journeyId || CampaignCtx.jData._jId;
        if(!this.campaignInsights){
          this.campaignInsights = new CampaignInsights({
            _journeyId: jid || "",
            _journeyItem: this._journeyItem,
            _stepIndex: "0",
            campaignTypeName: this._usedIn,
            _usedIn: this._usedIn,
            _accessible: this._accessible,
            _changeLog: this._changeLog,
            _parent: this
          });
          this.campaignInsights.startup();
          domConstruct.place(this.campaignInsights.domNode, this.insightsCont, 'last');
          this.own(this.campaignInsights);
        }
        else {
          this.campaignInsights.startup();
          this.campaignInsights.setAccess(this._accessible);
        }
      }
    },

    // .........................................
    _initChangeLog: function ()
    {
      let cCtx = {
        _parent: this,
        _journeyId: this._journeyId,
        _usedIn: this._usedIn
      };
      this._changeLog = new ChangeLog(cCtx);
      this.own(this._changeLog);
    },

    //.........................................
    _showChangeLogDisplay: function ()
    {
      if (!this._journeyId || this._journeyId === "")
      {
        return;
      }
      if (!this._changeLog || !Array.isArray(this._changeLog.changeLog)
        || !this._changeLog.changeLog.length)
      {
        return;
      }

      if (this._changeLogDisplay)
      {
        this._changeLogDisplay.populate();
        return;
      }

      let dv = document.createElement("div");
      dv.classList.add(...["c3_chgLgDWrp", "c3_rows_nowrap"]);
      let dv1 = document.createElement("div");
      dv.append(dv1);
      this._changeLogDisplay = new ChangeLogDisplay({
        _parent: this,
        _changeLog: this._changeLog
      });
      this._changeLogDisplay.startup();
      dv.append(this._changeLogDisplay.domNode);
      //this.dialogBody.prepend(dv);
      this.metricsCont.prepend(dv);
      this.own(this._changeLogDisplay);
    },

    // .........................................
    _moveChangeLogDisplay: function (tabNum = 1)
    {
      let dv = this.dialogBody.querySelector(".c3_chgLgDWrp");
      if (!dv) return;
      if (tabNum === 3)
      {
        // insights section
        this._tabInsightsB.prepend(dv);
      }
      else
      {
        // overview tab
        this.metricsCont.prepend(dv);
      }
    },

    // ................................................
    _alert: function(msg)
    {
      if(!msg.text && msg.html && msg.html !== "")
      {
        // using msg.html instead.
        msg.text = "";
      }
      var alertParams = {
        "dialog_title": msg.title || Res.info,
        "dialog_msg": msg.text
      };
      if(msg.html && msg.html !== "")
      {
        // Note: use 'html' for known text only.
        // Where the message text is loaded from Res file.
        // This will overwrite msg.text
        alertParams._html = msg.html;
      }
      var alertDialog = new AlertBox(alertParams);
      alertDialog.startup();
      alertDialog.setParent(this);
      this.own(alertDialog);
    },


    //................................
    // No ctx on first run
    _fetchComments: function(ctx)
    {
      // on first run 'ctx' is null
      // ctx._cmd : update - check and update
      //      comments list.

      let jid = this._journeyId || CampaignCtx.jData._jId;

      if(!jid)
      {
        // new journey, no comments
        return;
      }
      var targetUrl = "/c3/data/" + this._usedIn + "/comment?journeyId=" + jid;
      
      //udc.showBusy(this.chatter);
      loader.GET(targetUrl, {
        ignoreAuth: true
      }).then(lang.hitch(this, function(data)
      {
        //udc.hideBusy();
        if (data && data.status === "fail")
        {
          udc.alert(data.reason, "info");
          udc.log(data.reason);
          return;
        }
        if(Array.isArray(data))
        {
          if(!ctx)
          {
            this._setCommentsData(data);
          }
          else if (ctx && ctx._cmd === "update")
          {
            // set only if the comments count has changed
            if(this._commentsData && this._commentsData.length !== data.length)
            {
              this._setCommentsData(data);
              if(this.commentsContainer)
              {
                this.commentsContainer.updateComments(data);
              }
            }
          }
        }
      }), lang.hitch(this, function(error)
      {
        //udc.hideBusy();
        udc.alert(error, "error");
        udc.log(error);
      }));
    },

    //...............................
    _setCommentsData: function(commentsData)
    {
      if(!commentsData || !Array.isArray(commentsData))
      {
        this._commentsData = [];
      }
      else
      {
        this._commentsData = commentsData;
      }
      if(this._commentsData && this._commentsData.length)
      {
        if(this._commentsCount)
        {
          this._commentsCount.innerHTML = "(" + this._commentsData.length + ")";
        }
      }
    },

    //................................
    _onComments: function()
    {
      let jid = this._journeyId || CampaignCtx.jData._jId;

      if(!jid)
      {
        this._alert({text: "Before using comments, please Save Journey"});
        return;
      }
      if(this.commentsContainer)
      {
        domConstruct.empty(this.commentsContainer);
        if(this.commentsContainer && typeof this.commentsContainer.destroy === 'function')
        {
          this.commentsContainer.destroy();
        }
      }

      var pObj = {
        _parent: this,
        _usedIn: this._usedIn,
        _journeyId: jid
      };
      if(this._commentsData)
      {
        pObj._commentsData = this._commentsData;
      }

      this.commentsContainer = new CommentsContainer(pObj);
      this.commentsContainer.startup();
      domConstruct.place(this.commentsContainer.domNode, this.bodyCont, "last");
      this.own(this.commentsContainer);
    },

    //....................................
    _startCommentsChecker: function()
    {
      this._intervalComments = setInterval(lang.hitch(this, function()
      {
        udc.log("check new comments");
        let jid = this._journeyId || CampaignCtx.jData._jId;

        if(!jid)
        {
          // new journey, no comments
          return;
        }
        this._fetchComments({
          _cmd: "update"
        });
      }), 120000);
    },

    //............................
    _showAuditTrailBtn: function()
    {
      if(this.auditTrailBtn) return;
      this.auditTrailBtn = new AuditTrailBtn({
        _parent: this,
        _usedIn: this._usedIn
      });
      this.auditTrailBtn.startup();
      this.own(this.auditTrailBtn);
    },

    //................................
    _onAuditTrail: function()
    {
      if(!this._journeyId)
      {
        this._alert({text: Res.saveBeforeAuditTrail});
        return;
      }
      if(this.auditTrailContainer)
      {
        domConstruct.empty(this.auditTrailContainer);
        if(this.auditTrailContainer && typeof this.auditTrailContainer.destroy === 'function')
        {
          this.auditTrailContainer.destroy();
        }
      }

      var pObj = {
        _parent: this,
        _usedIn: this._usedIn,
        // _type: 'item'|'type'
        _type: "item",
        _journeyId: this._journeyId
      };

      this.auditTrailContainer = new AuditTrailContainer(pObj);
      this.auditTrailContainer.startup();
      //domConstruct.place(this.auditTrailContainer.domNode, this.jBodyCont, "last");
      domConstruct.place(this.auditTrailContainer.domNode, document.body, "last");
      this.own(this.auditTrailContainer);
    },

    // ........................................
    selectTab: function(num)
    {
      var num = num || 1;
      // remove all c3_tb_sel
      var oldSelected = this.dialogBody.querySelectorAll(".c3_tb_sel");
      this.addOrRemoveClassInEach(oldSelected, "c3_tb_sel", false);
      // hide all body divs
      var oldBodySelected = this.dialogBody.querySelectorAll(".c3_tbB");
      this.addOrRemoveClassInEach(oldBodySelected, "c3_hide", true);
      // add c3_tb_sel to current tab
      domClass.add(this["tab" + num + "head"], "c3_tb_sel");
      // domClass.remove(this["tab" + num + "body"], "c3_hide");
      // show selected container
      // show current tab
      var newBodySelected = this.dialogBody.querySelectorAll(".c3_tbB" + num);
      this.addOrRemoveClassInEach(newBodySelected, "c3_hide", false);
    },

    // .........................................
    _showScreen: function(screenNum)
    {
      if(screenNum === 1)
      {
        // overview
        this.selectTab(1);
        this._moveChangeLogDisplay(screenNum);
        let cCtx = {
          _parent: this,
          _journeyId: this._journeyId,
          _usedIn: this._usedIn
        };
        this._changeLog.getChangeLog(cCtx).then(lang.hitch(this, function (logData){
          this._showMetrics();
        }));
      }
      else if(screenNum === 2)
      {
        // definition
        this.selectTab(2);
        // this._showDetail();
      }
      else if(screenNum === 3)
      {
        // dashboard tab
        this.selectTab(3);
        this._moveChangeLogDisplay(screenNum);
        this._showInsights();
      }
    },

    // ............................
    _setEvents: function()
    {
      this.own(
        // Add Segment - add Campaign target
        on(this.addSegmentBtn, "click", lang.hitch(this, function()
        {
          this._selectSegment();
        })),

        // on time type change
        on(this.tsTimeType, "change", lang.hitch(this, function()
        {
          udc.log(this.tsTimeType.value);
          // read-only
          if(this._readOnly)
          {
            // read only - skip
            return;
          }

          this.onTimeType();
        })),

        // Add Final-Segment - add final Campaign target
        on(this.addFinalSegmentBtn, "click", lang.hitch(this, function()
        {
          var payload = this.getContext().payload;
          if (!payload) return;
          if (!payload.states || !payload.states.length || payload.states.length < 1)
          {
            // First add states[0] entry for the Target Segments
            // and then add states[1] entry for Conversion Segments.
            this._alert({text: Res["msgCreateTargetSegmentsFirst_" + this._usedIn]});
            return;
          }
          this._selectFinalSegment();
        })),

        // on one-time-campaign validity/campaign-end days change
        on(this._1tcValidityDays, "change", lang.hitch(this, function(){
          udc.log(this._1tcValidityDays.value);
          // read-only
          if(this._readOnly)
          {
            // read only - skip
            return;
          }

          this._onChangeOTCValidityDays();
        })),

        // Re target
        on(this._retargetSelect, "change", lang.hitch(this, function(){
          // read-only
          if(this._readOnly)
          {
            // read only - skip
            return;
          }

          this._showHideRetargetTimeCont();
          //if(this._retargetSelect.value !== "timeperiod")
          if(!isNaN(this._tsRetarget.value) && (this._retargetSelect.value === "timeperiod" || this._retargetSelect.value === "periodAfter1stAxn") )
          {
            // if previously retarget value was 'timeperiod' and user
            // changed it to 'periodAfter1stAxn' and there is value in <input> box
            // then auto-save the <select> change.
            this._saveRetargetChanges();
          }
          else if(this._retargetSelect.value !== "timeperiod" && this._retargetSelect.value !== "periodAfter1stAxn")
          {
            this._saveRetargetChanges();
          }
          else
          {
          }
        })),

        // Re target - time <input>
        on(this._tsRetarget, "change", lang.hitch(this, function(){
          // save
          //this._tsRetarget.validity.valid;
          // read-only
          if(this._readOnly)
          {
            // read only - skip
            return;
          }

          this._saveRetargetChanges();
        })),

        // Re target - time unit <select>
        on(this._tsUnit, "change", lang.hitch(this, function(){
          // save
          // read-only
          if(this._readOnly)
          {
            // read only - skip
            return;
          }
          this._saveRetargetChanges();
        })),


        // on time selection (TI)
        on(this.timeValueCont, "click", lang.hitch(this, function()
        {
          this.onTimerClick();
        })),

        // on time selection (OTC)
        on(this.timeValueContOTC, "click", lang.hitch(this, function()
        {
          // read-only
          if(this._readOnly)
          {
            // read only - skip
            return;
          }
          this.onTimerClickOTC();
        })),

        // Add Action - add Campaign target
        on(this.addActionBtn, "click", lang.hitch(this, function()
        {
          this.newActionItem();
        })),

        // match all - actions
        on(this.matchAllBtn, "click", lang.hitch(this, function(evt)
        {
          var matchAll = this.matchAllBtn.dataset.match;
          var isMatchAll = false;
          // toggle
          if (matchAll === "true")
          {
            isMatchAll = false;
          }
          else if (matchAll === "false")
          {
            isMatchAll = true;
          }
          this.onMatchAllActions(isMatchAll);
        })),

        // Comments
        on(this._commentsBtn, "click", lang.hitch(this, function(){
          this._onComments();
        })),


        // Overview tab
        on(this.tab1head, "click", lang.hitch(this, function(){
          this._showScreen(1);
        })),
        // Definition tab
        on(this.tab2head, "click", lang.hitch(this, function(){
          this._showScreen(2);
        })),
        // Insights tab
        on(this.tab3head, "click", lang.hitch(this, function(){
          this._showScreen(3);
        }))
      );
    },

    // ...........................
    // Subscribe to events.
    _subscribe: function()
    {
      this.own(topic.subscribe("/campaign/overview/show", lang.hitch(this, function(ctx)
      {
        // drive journey overview box contents
        // ~
        this.showOverview(ctx);
      })),

      topic.subscribe("/campaign/publish", lang.hitch(this, function(ctx)
      {
    	//deliberately set the isComplete flag to false as an update to the artifact has been made.
    	PermissionState.update(this._usedIn, false, this._isReady);
        this._togglePublishBtn();
       
      })),
      

      
      // show signals when the activity ctx image on csb landing page is clicked
      topic.subscribe("/interactions/refresh", lang.hitch(this, function()
      {
        this._getCampaignData().then(lang.hitch(this, function(){
          this._showMetrics(true);
        }));
      })));
    },

    // ..............................
    // delete and clear segments
    destroySegments: function()
    {
      if (!this._segWidget || !this._segWidget.length)
      {
        return;
      }
      for (var i = 0; i < this._segWidget.length; i++)
      {
        if (typeof (this._segWidget[i].destroyRecursive) === 'function')
        {
          this._segWidget[i].destroyRecursive();
        }
      }
      this._segWidget = [];
      // this.segHolder.innerHTML = "";
      var childSegNodes = this.segmentCont.childNodes;
      for ( var node in childSegNodes)
      {
        var chNode = childSegNodes[node];
        if (domAttr.get(chNode, "data-ref"))
        {
          this.segmentCont.removeChild(chNode);
        }
      }
    },

    //.........................
    destroy: function()
    {
      // CampaignOnce OTC if udc.alert 'view only ..' 
      // message is showning then hide it.
      var aNode = document.querySelector("#alertSection");
      if(aNode && aNode.style.display === "block")
      {
        var aNodeText = aNode.querySelector("#alertText");
        if(aNodeText && aNodeText.textContent === Res.readOnlyBannerMsg)
        {
          // hide it
          aNode.style.display = "none";
        }
      }

      // clean the content area and header
      this.journeyName.innerHTML = "";
      this.journeyMode.innerHTML = "";
      this.journeyDesc.innerHTML = "";

      if(this._intervalComments)
      {
        clearInterval(this._intervalComments);
      }

      this.inherited(arguments);
    },

    _end: 0
  });

});
