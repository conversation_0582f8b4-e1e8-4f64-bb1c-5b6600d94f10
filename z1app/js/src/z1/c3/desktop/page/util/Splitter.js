/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define([
  "dojo/_base/declare",
  "dojo/_base/lang",
  "udc/udc",
  "z1/common/C3Util",
  "z1/c3/utils/ResizeUtil",
  "z1/c3/utils/DomUtil",
], function(
  declare, lang, udc, C3Util, ResizeUtil, DomUtil
)
{

  /**
   * Splitter - makes panes resizable by adding
   * splitter between them.
   * 
   * Usage:
   * new Splitter({
   * - panes: {Array} - array of DIVs references.
   *       Example: panes: [leftDiv, rightDiv]
   *       Can have 2 or more panes.
   * - direction: {string} direction [optional]
   *      [default: 'H']
   *      'H' - Horizontal splitter
   *      'V' - vertical (future)
   * - flexHeight: [ default: false - set hight to 100% ,
   *      use display flex height.
   * 
   * - splitEqually: {boolean} [optional]
   *      [default: true]
   *      `true` - equal sized panes
   *      `false` - widget does not set `width` of
   *               panes. Assumption is that the size
   *               classes are already specified on the
   *               panes.
   * });
   * 
   * Limitations:
   * - If 'min/max-width' are set then it should be
   * using 'px' unit.
   * 
   * Examples:
   * HTML struture
   * <pre>
   * <div class="c3_rows_nowrap c3_align_stretch">
   *   <div data-dojo-attach-point="leftDiv">...</div>
   *   <div data-dojo-attach-point="rightDiv">...</div>
   * </div>
   * </pre>
   * Example 1: splitter between two DIV's
   * let splitter = new Splitter();
   * splitter.createSplitter({
   *   // put section div's
   *   panes: [leftDiv, rightDiv]
   * });
   * this.own(splitter);
   * 
   * Example 2: Three panes with two splitters
   * splitter.createSplitter({
   *   // put section div's
   *   panes: [leftDiv, middleDiv, rightDiv]
   * })
   */
  return declare(null, {
    //Res: Res,

    constructor: function(params)
    {
      let uq = "_" + Math.floor(Math.random()*1000) + (new Date().getTime());
      params._unqSpltMx = (params._unqSpltMx) ? params._unqSpltMx : uq;

      params.panes = params.panes || [];
      // Split panes equally. (default: true)
      if (typeof params.splitEqually != "boolean") params.splitEqually = true;
      // Currently supports horizontal splitting only.
      // default horizontal split
      if (!params.direction) params.direction = "H";

      params._diviArr = [];
      lang.mixin(this, params); // keep this line last
    },

    //..................................................
    startup: function ()
    {
      let n = this.panes.length;
      let clsSplitEq = this.splitEqually ? "c3_split_eql" : "c3_split_uneq";
      let clsPSplitEq = this.splitEqually ? "c3_splitEql" : "c3_splitUneq";
      let dirCls = (this.direction == "H") ? "c3_rows_nowrap" : "c3_cols_nowrap";
      let pEl = this.panes[0].parentNode;
      // classes on parent node
      if (!this.flexHeight)
      {
        if (pEl)
          pEl.classList.add("c3_split", "c3_split" + n, dirCls, clsPSplitEq);
        // classes on panes
        this.panes.forEach((p, i) => p.classList.add("c3_split_c", "c3_split_c" + i, clsSplitEq));
      }

      // The panes might not have been fully rendered
      // yet and in that case the logic relying on
      // min-width/sizes will not work.
      // Detect if all panes are rendered and only
      // then create splitter.
      let iRndrd = 0;
      this.panes.forEach((p, i) => {
        // on rendered
        DomUtil.onElementRendered(p, (elmnt) => {
          // onRendered callback
          iRndrd++;
          if (iRndrd == n)
          {
            // All panes have rendered. Safe to create splitters.
            for (let i = 1; i < this.panes.length; i++)
            {
              this._createSplitterForTwo(i);
            }

            // Attach resize observer to container.
            // If browser is resized then re-adjust the
            // widths of panes. Use the panes width
            // in 'px' to calculate corresponding
            // width in '%' and apply that on resize.
            pEl.resizeHandler = (entry) => {
              if (!entry.target) return;
              let wSplt = 20; // 20px
              let wTotal = 0;
              let cW = [];
              let gotWidths = true;
              this.panes.forEach(c => {
                let wStr = getComputedStyle(c).getPropertyValue('width');
                let w;
                if (wStr.endsWith('px'))
                {
                  w = parseInt(wStr);
                  cW.push(w);
                  wTotal += w;
                }
                else
                {
                  // 'width' is either unavalable
                  // or is not set in 'px'
                  gotWidths = false;
                }
              });
              if (!gotWidths)
              {
                // set to equal %
                let w = (100/n) + "%";
                let divPx = ((n-1)*20/2) + "px";
                this.panes.forEach((c, i) => {
                  if (!isNaN(w)) c.style.width = `calc(${w} - ${divPx})`;
                });
              }
              else
              {
                wTotal = wTotal + n*wSplt;
                this.panes.forEach((c, i) => {
                  // convert px to %
                  let w = cW[i]*100/wTotal;
                  if (!isNaN(w)) c.style.width = w + "%";
                });
              }
            };
            ResizeUtil.observe(pEl);
            this.pEl = pEl;
          }
        });
      });
    },

    //.........................................
    /**
     * Adds splitter between two panes
     * 
     * @param i splitter index
     */
    _createSplitterForTwo: function (i = 1)
    {
      let leftDiv = this.panes[i - 1];
      let rightDiv = this.panes[i];

      // 'min-width'
      let minWidthL = this.getSizeFromCSS(leftDiv, 'min-width');
      let minWidthR = this.getSizeFromCSS(rightDiv, 'min-width');
      // 'max-width'
      let maxWidthL = this.getSizeFromCSS(leftDiv, 'max-width');
      let maxWidthR = this.getSizeFromCSS(rightDiv, 'max-width');

      // check if `flex:1` is set.
      let isFlex1L = getComputedStyle(leftDiv).getPropertyValue('flex') === "1 1 0%";
      let isFlex1R = getComputedStyle(rightDiv).getPropertyValue('flex') === "1 1 0%";

      // case: if min-width == max-width then the div size
      // is fixed splitter will have no effect. So skip
      // creating splitter.
      if (this.isMinMaxWSame(minWidthL, maxWidthL)) return;
      if (this.isMinMaxWSame(minWidthR, maxWidthR)) return;

      // create divider
      let dividerDiv = this.createDivider(leftDiv);

      // store mousedown event context
      let md;
      let evts = {};

      // mouseMove
      evts["mouseMove" + this._unqSpltMx + "_" + i] = (e) => {
        let delta = {
          x: e.clientX - md.e.clientX,
          y: e.clientY - md.e.clientY
        };

        if (this.direction === "H")
        {
          // Horizontal
          delta.x = Math.min(Math.max(delta.x, -md.firstWidth), md.secondWidth);
          // Check for min-width
          if (minWidthL && minWidthL > (md.firstWidth + delta.x))
          {
            // Left min-width reached
            return;
          }
          if (minWidthR && minWidthR > (md.secondWidth - delta.x))
          {
            // Right min-width reached
            return;
          }
          // Check for max-width
          if (maxWidthL && maxWidthL < (md.firstWidth + delta.x))
          {
            // Left max-width reached
            return;
          }
          if (maxWidthR && maxWidthR < (md.secondWidth - delta.x))
          {
            // Right max-width reached
            return;
          }
          // Change widths
          dividerDiv.style.left = md.offsetLeft + delta.x + "px";
          leftDiv.style.width = (md.firstWidth + delta.x) + "px";
          rightDiv.style.width = (md.secondWidth - delta.x) + "px";
          if (isFlex1L && isFlex1R)
          {
            // Handle `flex: 1`
            // flex-basis
            // For when both panes have `flex: 1`
            leftDiv.style.flexBasis = (md.firstWidth + delta.x) + "px";
            rightDiv.style.flexBasis = (md.secondWidth - delta.x) + "px";
          }
        }
      };

      // mouseUp
      // end drag - onMouseUp
      evts["mouseUp" + this._unqSpltMx + "_" + i] = (e) => {
        document.body.removeEventListener("mousemove", evts["mouseMove" + this._unqSpltMx + "_" + i]);
        document.body.removeEventListener("mouseup", evts["mouseUp" + this._unqSpltMx + "_" + i]);
        leftDiv.removeAttribute("inert");
        rightDiv.removeAttribute("inert");
      };

      // on mouseDown
      const onMouseDown = (e) =>
      {
        leftDiv.inert = true;
        rightDiv.inert = true;

        // store mousedown event context
        md = {
          e,
          offsetLeft: dividerDiv.offsetLeft,
          offsetTop: dividerDiv.offsetTop,
          firstWidth: leftDiv.offsetWidth,
          secondWidth: rightDiv.offsetWidth
        };

        // dragging - onMouseMove
        document.body.addEventListener("mousemove", evts["mouseMove" + this._unqSpltMx + "_" + i]);
        document.body.addEventListener("mouseup", evts["mouseUp" + this._unqSpltMx + "_" + i]);
      };

      // onMouseDown on splitter
      dividerDiv.addEventListener("mousedown", onMouseDown);
      this._diviArr.push({dividerDiv: dividerDiv, onMouseDown: onMouseDown});
    },

    //........................................
    createDivider: function (leftDiv)
    {
      let dividerDiv = document.createElement("div");
      dividerDiv.classList.add("c3_split_splitter", "c3-bg-c-gray0", "c3_flex_center_1");
      let diviC = document.createElement("div");
      diviC.classList.add("c3-c-gray90");
      let dividerIcon = C3Util.useSvgDef("zi-splitter-icon", ["c3_fs-xs", "c3_ht20"]);
      diviC.append(dividerIcon);
      dividerDiv.append(diviC);
      leftDiv.insertAdjacentElement('afterend', dividerDiv);
      return dividerDiv;
    },

    //..........................................
    getSizeFromCSS: function (elmnt, propName)
    {
      let minWidthLStr = getComputedStyle(elmnt).getPropertyValue(propName);
      // Assumption: if property (e.g. 'min-width') is set then it is in 'px'.
      if (minWidthLStr.endsWith('px')) return parseInt(minWidthLStr);
      return;
    },

    //........................................
    isMinMaxWSame: function (mnW, mxW)
    {
      // case: if min-width == max-width then the div size
      // is fixed splitter will have no effect. So skip
      // creating splitter.
      if (mnW && mxW && mnW == mxW)
      {
        udc.log("Not creating splitter. min-width==max-width");
        return true;
      }
      return false;
    },

    //........................................
    destroy: function ()
    {
      // Method might get called twice depending
      // on parent's destroy wiring.
      try
      {
        if (Array.isArray(this._diviArr) && this._diviArr.length)
        {
          while((diviObj = this._diviArr.pop()) != null)
          {
            // remove mousedown from splitter
            if (diviObj?.dividerDiv)
              diviObj.dividerDiv.removeEventListener("mousedown", diviObj.onMouseDown);
          }
        }
        // remove resize observer
        if (this.pEl) ResizeUtil.unobserve(this.pEl);
      }
      catch (err){}

      this.inherited(arguments);
    },

    _end: 0
  });
});
