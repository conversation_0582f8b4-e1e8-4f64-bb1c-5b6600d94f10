/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define([
  "dojo/_base/declare", 
  "dojo/_base/lang",
  "udc/core/commons/loader",
  "dojo/on",
  "dojo/dom-class",
  "udc/udc",
  "z1/c3/desktop/start",
  "udc/core/rt/widgets/_ComplexBaseWidget",
  "dojo/text!z1/c3/desktop/page/templates/experiences2/AxnTemplateSelectorSrch.html",
  "dojo/i18n!z1/c3/desktop/page/nls/AxnTemplateSelectorSrchRes",
  "z1/common/C3Util",
  "z1/c3/desktop/page/util/C3Grid",
  "z1/c3/desktop/page/util/C3Table2",
  "z1/c3/desktop/data/modules/ModulesData",
  "z1/c3/desktop/data/campaigns/CpgnData",
  "z1/c3/desktop/data/CampaignCtx",
  "z1/c3/desktop/page/modules/actionTemplateWidgets/ActionCtx",
  "z1/c3/desktop/page/modules/actionTemplateWidgets/AxnUtil",
  "z1/c3/desktop/page/settings/content/PreviewContainerXp",
  "z1/c3/desktop/page/util/C3SearchBox",
  "z1/c3/desktop/page/util/positionItMx",
  "dijit/Dialog",
  "dojo/topic",
  "dojo/dom-construct",
  "z1/c3/utils/Permissions",
  "dojo/Deferred",
  "dojo/promise/all",
  "z1/c3/desktop/data/SystemConfigurations",
],
function(
  declare, lang, loader, on, domClass, udc, z1, BaseWidget, templateStr, Res,
  C3Util, C3Grid, C3Table2, ModulesData, CpgnData, CampaignCtx,
  ActionCtx, AxnUtil, PreviewContainerXp, C3SearchBox, positionItMx,
  Dialog, topic, domConstruct, UserACL, Deferred, all, SystemConfigurations
)
{

  let {articleTmp, rawDataTmp, smsTmp} = AxnUtil.nonTemplates;
  
  let tmpListTR = [
    {
      "appState": "READY",
      "images": {
        "alert0.jpg": {
          "url": "alert.jpg",
          "order": 0
        }
      },
      "appName": "ZineOne OOTB Action Templates",
      "appPriceModel": {},
      "appInfo": {
        "overview": "Version 1.0 is the beginning. It takes a wise man to understand that the beginning is the most important part of the work.",
        "versionDetail": "{'description':'version 1.0','jar':'ActionTemplateOOTB-1.0.jar'}",
        "version": "1.0"
      },
      "appDescription": "Out-of-the-box templates to enhance action definition with predefined rules and readily available runtime rule processing",
      "appCategory": "any",
      "description": "Create a custom alert message.",
      "type": "actiontemplate",
      "params": [],
      "version": "1.0",
      "appIcon": "res/add.png",
      "appPublisherId": "zineone.com",
      "appId": "udc.system.core.ActionTemplateOOTB:1.0",
      "isNSTemplate": false,
      "name": "Blank Alert Template",
      "isML": false,
      "id": "udc.system.core.ActionTemplateOOTB:alert:a_blank:1.0",
      "tag": {
        "Device Type": [
          {
            "value": "Phone"
          },
          {
            "value": "Tablet"
          },
          {
            "value": "Desktop"
          }
        ],
        "Device SDK": [
          {
            "value": "Android"
          },
          {
            "value": "iOS"
          },
          {
            "value": "HTML5"
          }
        ]
      },
      "appTag": {},
      "class": [
        "actiontemplate"
      ]
    },
    {
      "appState": "READY",
      "images": {
        "appbox0.jpg": {
          "url": "appbox.jpg",
          "order": 0
        }
      },
      "appName": "ZineOne OOTB Action Templates",
      "appPriceModel": {},
      "appInfo": {
        "overview": "Version 1.0 is the beginning. It takes a wise man to understand that the beginning is the most important part of the work.",
        "versionDetail": "{'description':'version 1.0','jar':'ActionTemplateOOTB-1.0.jar'}",
        "version": "1.0"
      },
      "appDescription": "Out-of-the-box templates to enhance action definition with predefined rules and readily available runtime rule processing",
      "appCategory": "any",
      "description": "Create a custom appbox message with inlined CSS classes.",
      "type": "actiontemplate",
      "params": [],
      "version": "1.0",
      "appIcon": "res/add.png",
      "appPublisherId": "zineone.com",
      "appId": "udc.system.core.ActionTemplateOOTB:1.0",
      "isNSTemplate": false,
      "name": "Blank App Box Template",
      "isML": false,
      "id": "udc.system.core.ActionTemplateOOTB:appbox:ab_blank:1.0",
      "tag": {
        "Device Type": [
          {
            "value": "Phone"
          },
          {
            "value": "Tablet"
          },
          {
            "value": "Desktop"
          }
        ],
        "Device SDK": [
          {
            "value": "Android"
          },
          {
            "value": "iOS"
          },
          {
            "value": "HTML5"
          }
        ]
      },
      "appTag": {},
      "class": [
        "actiontemplate"
      ]
    },
    {
      "appState": "READY",
      "images": {
        "banner0.jpg": {
          "url": "banner.jpg",
          "order": 0
        }
      },
      "appName": "ZineOne OOTB Action Templates",
      "appPriceModel": {},
      "appInfo": {
        "overview": "Version 1.0 is the beginning. It takes a wise man to understand that the beginning is the most important part of the work.",
        "versionDetail": "{'description':'version 1.0','jar':'ActionTemplateOOTB-1.0.jar'}",
        "version": "1.0"
      },
      "appDescription": "Out-of-the-box templates to enhance action definition with predefined rules and readily available runtime rule processing",
      "appCategory": "any",
      "description": "Create your banner with inlined CSS classes.",
      "type": "actiontemplate",
      "params": [],
      "version": "1.0",
      "appIcon": "res/add.png",
      "appPublisherId": "zineone.com",
      "appId": "udc.system.core.ActionTemplateOOTB:1.0",
      "isNSTemplate": false,
      "name": "Blank Banner Template",
      "isML": false,
      "id": "udc.system.core.ActionTemplateOOTB:banner:b_blank:1.0",
      "tag": {
        "Device Type": [
          {
            "value": "Phone"
          },
          {
            "value": "Tablet"
          },
          {
            "value": "Desktop"
          }
        ],
        "Device SDK": [
          {
            "value": "Android"
          },
          {
            "value": "iOS"
          },
          {
            "value": "HTML5"
          }
        ]
      },
      "appTag": {},
      "class": [
        "actiontemplate"
      ]
    },
    // rawData - dummy template
    {
      "appState": "READY",
      "images": {
      },
      "appName": "ZineOne OOTB Action Templates",
      "appPriceModel": {},
      "appInfo": {
        "overview": "Version 1.0 is the beginning. It takes a wise man to understand that the beginning is the most important part of the work.",
        "versionDetail": "{'description':'version 1.0','jar':'ActionTemplateOOTB-1.0.jar'}",
        "version": "1.0"
      },
      "appDescription": "Out-of-the-box templates to enhance action definition with predefined rules and readily available runtime rule processing",
      "appCategory": "any",
      "description": "Create your banner with inlined CSS classes.",
      "type": "actiontemplate",
      "params": [],
      "version": "1.0",
      "appIcon": "res/add.png",
      "appPublisherId": "zineone.com",
      "appId": "udc.system.core.ActionTemplateOOTB:1.0",
      "isNSTemplate": false,
      "name": "Data Object",
      "isML": false,
      "isNotTemplate": true, // not template indicator
      // "id": "udc.system.core.ActionTemplateOOTB:dummy:rawData:1.0",
      "id": "rawData",
      "tag": {
        "Device Type": [
          {
            "value": "Phone"
          },
          {
            "value": "Tablet"
          },
          {
            "value": "Desktop"
          }
        ],
        "Device SDK": [
          {
            "value": "Android"
          },
          {
            "value": "iOS"
          },
          {
            "value": "HTML5"
          }
        ]
      },
      "appTag": {},
      "class": [
        "actiontemplate"
      ]
    }
  ];


  /**
   * Creates a experience template selector.
   */
  return declare(BaseWidget,
  {
    templateString : templateStr,
    Res: Res,

    /**
     * Creates an instance of this page.
     * @param {Object}
     */
    constructor : function(params)
    {
      this.inherited(arguments);
      params = params || {};
 
      //this._parent = params._parent;

      if(!params._idSfx)
      {
        params._idSfx = "_eca_" + Math.ceil(Math.random()*1000) + "_eca_";
      }

      // hide field label (default: false)
      params.hideLabel = typeof params.hideLabel === "boolean" ? params.hideLabel : false;

      // readOnlyMode (view only)
      params.readOnly = (typeof params.readOnly == "boolean") ?
          params.readOnly : false;
      
      params._templates = params._templates || [];
      params._isPNForSIEnabled = false;
    },

    // ////////////////////////////////////////////////////////////////////////////
    // Implementations

    postCreate : function()
    {
      this.inherited(arguments);

      if (this.hideLabel) this._lblCont.remove();
    },

    // .....................................................
    startup : function()
    {
      this.inherited(arguments);

      this.initData();

//      //-----------------------
//      // Debug - create template instance flow
//      let dv = document.createElement("button");
//      //dv.classList.add("c3_hide");
//      dv.textContent = "Banner Template";
//      this.own(
//        on(dv, "click", () => {
//          let ctx = {
//          };
//          // blank template id
//          ctx.itemId = "udc.system.core.DriveToSecondPurchase:DriveToSecondPurchase:1.0";
//        })
//      );
//      this._bodyCont.append(dv);
//      // end - Debug
//      //---------------------------------------

      this._setEvents();
      this._subscribe();
    },

    //.........................................
    initData: function ()
    {
      // async - fetch data
      // then ->
      this.populate();
      this._checkPushNotificationSetting();
    },

    //.........................................
    populate: function()
    {
      this._showAxnTpltSelector();
      // this._fetchAxnTemplates().then(() => {
      //   this._showAxnTpltSelector();
      // });
    },

    //............................................
    _checkPushNotificationSetting: function()
    {
      var deferr = new Deferred();

      SystemConfigurations.api.getBooleanPropByName({
        propName: "z1.push.si",
        cacheOk: true
      }).then(lang.hitch(this, function(cfgData){
        if (cfgData && typeof cfgData.value === "boolean")
        {
          this._isPNForSIEnabled = cfgData.value;
        }
        deferr.resolve(cfgData);
      }), lang.hitch(this, function(error){
        udc.log(error);
        deferr.reject(error);
      }));

      return deferr.promise;
    },

    //..............................................
    _fetchAxnTemplates: function ()
    {
      let deferr = new Deferred();
      // reset
      this._templates = [];

      // set by EditExpActionTypes
      // let groupDisplayName = ActionDefineCtx.aData.getSelectedGrpDispName();
      // let tmpGroups = ["Alert", "Banner"];
      let tmpGroups = CampaignCtx.jData.getActionGroups();
      this._noTmpData(true);
      ModulesData.api.fetchActionTemplatesForActionType({id: tmpGroups, cacheOk: true}).then(
        lang.hitch(this, function(data){
          if (data && data.status == 'fail')
          {
            this._noTmpData(true);
            deferr.resolve([]);
            return deferr.promise;
          }

          if (data && Array.isArray(data) && data.length)
          {
            this._noTmpData(false);

            let oldAxnTemps = CampaignCtx.jData.getUpgradableAxns();

            if (oldAxnTemps && oldAxnTemps.length > 0) 
            {
              //const updData = data.map(latestTemplate => {
              //  let item = latestTemplate;
              //  let sid = this._idWithoutVersion(latestTemplate.id);
              //
              //  oldAxnTemps.forEach(oldTemplate => {
              //    // ignore version, compare id, replace latest template with old
              //    let oid = this._idWithoutVersion(oldTemplate.moduleId);
              //    if (oid === sid) 
              //    {
              //      item = oldTemplate;
              //    }
              //  });
              //  return item;
              //});
              // data = updData;
            } 
          }
          else
          {
            // this._noTmpData(true);
          }
          this._templates = data;
          deferr.resolve(this._templates);
        }),
        lang.hitch(this, function(err){
          this._noTmpData(true);
          udc.log(err);
          deferr.resolve([]);
        })
      );
      return deferr.promise;
    },

    //............................................
    _noTmpData: function (v = true)
    {
    },

    //............................................
    _noTmpUse: function (v = true)
    {
    },

    //............................................
    constructTopRowFromActionGroup: function ()
    {
      // Construct top blank templates rows based on
      // types in actionGroup. Use a hard coded
      // map of which blank templates need to be
      // added for which action types
      this._tList = [];
      let axnGroups = CampaignCtx.jData.getActionGroups();
      // CpgnData.api.actionGroupAndBlankTemplatesMap
      if (Array.isArray(axnGroups) && axnGroups.length
        // && Array.isArray(this._mData) && this._mData.length
      )
      {
        axnGroups.forEach((a, i) => {
          if (CpgnData.api.actionGroupAndBlankTemplatesMap[a])
          {
            let tmpIds = CpgnData.api.actionGroupAndBlankTemplatesMap[a];
            // filter templates
            if (Array.isArray(tmpIds))
            {
              tmpIds.forEach(tid => {
                let t;
                if (tid === "rawData")
                {
                  this._tList.push(rawDataTmp);
                  t = rawDataTmp;
                }
                else if (tid === "sms")
                {
                  this._tList.push(smsTmp);
                  t = smsTmp;
                }
                else if (tid === "article")
                {
                  this._tList.push(articleTmp);
                  t = articleTmp;
                }
                else
                {
                  let j = this._mData.findIndex(t => t.id.startsWith(tid));
                  if (j !== -1)
                  {
                    let tmp = this._mData[j];
                    this._tList.push(this._mData[j]);
                    this._mData.splice(j, 1);
                    // Put blank templates at top of list
                    this._mData = [tmp, ...this._mData];
                  }
                }
                // Put blank templates at top of list
                if (t != void(0)) this._mData = [t, ...this._mData];
              })
            }
          }
        });
      }
      else
      {
        // no action groups
      }
    },

    //............................................
    _onUseTemplate: function ()
    {
      // this._selectedTemplate.id
      udc.log("Selected template: ", this._selectedTemplate);
      if (this.onChange)
      {
        this.onChange(this._selectedTemplate);
      }
    },


    // .......................................................
    // Preview
    // .......................................................

    //..........................................
    _setActionType: function (tmp = {}, actionCtx)
    {
      let aType = "";
      if (!tmp)
      {
      }
      else if (tmp.id === "rawData")
      {
        aType = "rawData";
      }
      else if (tmp.id === "sms")
      {
        aType = "sms";
      }
      else
      {
        let tId = tmp.id;
        let s = tmp.id.split(":");
        if (s.length && s[1])
        {
          aType = s[1];
        }
      }
      let pAType = actionCtx.getActionType();
      if (pAType !== aType)
      {
        // _actionType changed
        actionCtx.setActionType(aType);
      }
    },


    ///////////////////////////////////////////////////////////////
    // Action template selector drop down
    ///////////////////////////////////////////////////////////////

    //..........................................
    // creates C3SearchBox selector
    // 1. Initially drop down list is empty
    // 2. populate list when drop down card is shown
    _showAxnTpltSelector: function (ctx = {}, node, dispNode)
    {
      if (this.sel && typeof this.sel.destroy === 'function')
      {
        // this.sel.destroy();
        // instace already exists - no need to recreate
        return;
      }

      // container
      if (!this._srchC)
      {
        this._srchC = document.createElement("div");
        this._srchC.classList.add("c3_cols_nowrap");
        this._cont.prepend(this._srchC);
      }

      let sel;
      let sCtx = {
        _parent: this,
        ...ctx,
        cls: "c3_axnTpltSelDD c3_flex_1 c3_html_fld c3_fld_deco",
        clsInput: "c3_ffm-inh c3_html_fld_div_h c3_pd_1v",
        placeholder: Res.placeholder,
        // typeahead/autocomplete
        showTypeahead: true,
        // setFocusOnReady: true,
        // doNotShowListOnVeryFirstFocus: true,
        // destroyOnLeave: true,
        closeListOnScroll: true,
        // icon on left
        showIcon: true,
        showDownArrowOnHover: true,
        // do not clear widget after
        // onChange is called.
        // clearAfterSelection: true,
        onChange: () => {
          udc.log(sel.value);
          // cleared input field
          // if (sel.value === "") return;
          this._onSelectionChange(ctx, sel);
        },
        onBlurCB: () => {
          // if (dispNode) dispNode.classList.remove("c3_hide");
        },
        // If not using custom createCardContent then
        // pass suggestion/drop-down list data as _ssList
        // _sList: this._mData,
        createCardContent: (cCtx = {}) => {
          let dv = document.createElement("div");
          dv.classList.add(...["c3_srchList", "c3_flex_1", "c3_cols_nowrap", "c3_y_auto", "c3_pd_8"]);

          this._fetchAxnTemplates().then(lang.hitch(this, function (data){
            // prepare list data
            if (this.actionCtx._usedIn === "campaignonce")
            {
              let actionTypeKeys = Object.keys(this.actionCtx._actionTypesDispList);
              this._mData = data.filter(x => {if (actionTypeKeys.includes(x.id.split(":")[1])){ return x;}})
              this._mData.push(smsTmp)
            } else {
              this._mData = data;
            }
            this.constructTopRowFromActionGroup();
            // populate card
            // let lst = sel.searchList(cCtx, data);
            let lst = sel.searchList(cCtx, this._mData);
            lst.forEach(e => {
              let opn = document.createElement("div");
              // 'c3_srchOpn' - generic class added to C3SearchBox
              //     drop-down's selectable options (list items).
              // 'c3_srchOpnEM' - [optional] class name specific to
              //     'Event Mapping' list page.
              opn.classList.add(...["c3_srchOpn", "c3_srchOpnAxnTplt", "c3_pd_8", "c3_cols_nowrap", "c3_gp2", "c3_btn"]);
              opn.value = e.name;
              opn.dataset.srchOpnV = e.name;
              let opnName = document.createElement("div");
              opnName.classList.add("c3_srchDDname", "c3_lh20", "c3-c-primary", "c3_fs14");
              opnName.textContent = e.name;
              opn.append(opnName);
              // the list shows both name and displayName
              if (e.displayName && e.name != e.displayName)
              {
                let disp = document.createElement("div");
                disp.classList.add("c3_srchDDDispN", "c3_lh20", "c3-c-secondary", "c3_fs12");
                disp.textContent = e.displayName;
                opn.append(disp);
              }
              if (e.description)
              {
                let descr = document.createElement("div");
                descr.classList.add("c3_srchDDDescr", "c3_lh20", "c3-c-secondary", "c3_fs12");
                descr.textContent = e.description;
                opn.append(descr);
              }
              this.own(on(opn, "mousedown", (evt) => {
                evt.preventDefault();
                // not left click
                if (evt.button !== 0) return;
                // update value in drop down input field
                sel.onValueSelected(e.name);
              }));
              // this.own(on(opn, "click", (evt) => {
              //   evt.preventDefault();
              //   // update value in drop down input field
              //   sel.onValueSelected(e.name);
              // }));
              dv.append(opn);
            });
          }),lang.hitch(this, function (err){
            udc.log(err);
          }));

          return dv;
        },
        isValidValue: (e) => {
          // 0. check entered event exists
          return this._isValidValue(e);
        },
      };
      // value
      if (this._selectedTemplate && this._selectedTemplate.name)
      {
        udc.log("_selectedTemplate: ", this._selectedTemplate);
        sCtx.value = this._selectedTemplate.name;
      }
      sel = new C3SearchBox(sCtx);
      this._srchC.append(sel.domNode);
      sel.startup();
      this.own(sel);
      this.sel = sel;
    },

    //............................................
    // check typed value exists in the drop down
    // list
    _isValidValue: function (_val)
    {
      // 0. check entered value exists
      if (_val && _val.length &&
        !this._mData.find(item => item.name === _val))
      {
        // entered value does not exist
        return false;
      }
      return true;
    },

    //................................................
    _onSelectionChange: function (ctx, sel)
    {
      udc.log("_onSelectionChange ", sel);
      let _val = sel.value;

      //// 1. check if value is changed
      //if (_e === this.prevEvt)
      //{
      //  udc.log("not changed");
      //  return;
      //}

      // 2. check entered value exists
      if (_val && _val.length &&
        !this._mData.find(itm => itm.name === _val))
      {
        // entered value does not exist
        if (sel && sel.domNode.classList) sel.domNode.classList.add("c3_txt_red");
        return;
      }

      // object
      let item = this._mData.find(itm => itm.name === _val);

      let selectedTemplateType = item?.id?.split(":")?.[1];

      if (this.actionCtx._usedIn === "campaignonce" && selectedTemplateType === 'push' && !this._isPNForSIEnabled)
      {
        udc.info(Res.vEnablePNForSIMsg);
        sel.onValueSelected("", true);
        return;
      }

      // 3. check item is not already selected
      if (item && this._selectedTemplate && item.id === this._selectedTemplate.id)
      {
        // already selected
        udc.log("'" + _val + " is already selected");
        return;
      }

      this._selectedTemplate = item;
      this._onUseTemplate({item: item});
    },


    //................................
    _setEvents: function()
    {
    },

    //...................................
    _subscribe: function()
    {
    },

    //.....................................................
    destroy: function()
    {
      this.inherited(arguments);
    },
    
    
    _end : 0
  });
  

});