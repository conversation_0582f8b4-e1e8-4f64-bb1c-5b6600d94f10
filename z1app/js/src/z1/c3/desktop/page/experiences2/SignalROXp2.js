/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define([
  "dojo/_base/declare", 
  "dojo/_base/lang",
  "dojo/on",
  "udc/udc",
  "udc/core/commons/loader",
  "z1/c3/desktop/start",
  "udc/core/rt/widgets/_BaseWidget",
  "dojo/text!z1/c3/desktop/page/templates/experiences2/SignalROXp2.html",
  "dojo/i18n!z1/c3/desktop/page/nls/SignalROXp2Res",
  "z1/c3/desktop/data/CampaignCtx",
  "z1/c3/desktop/data/C3Context",
  "z1/common/C3Util",
  "dojo/topic",
  "dojo/dom-construct",
  "dojo/Deferred",
  "dojo/promise/all",
  "z1/c3/utils/validator",
],
function(declare, lang, on, udc, loader, z1, BaseWidget, templateStr, Res,
  CampaignCtx, C3Context,
  C3<PERSON><PERSON>, topic, domConstruct, Deferred, all, validator
)
{

  /**
   * List Signals used in the campaign.
   * 
   * Assumption:
   * - Signals data is already fetched
   */
  return declare(BaseWidget,
  {
    templateString: templateStr,
    Res: Res,

    /**
     * Creates an instance of this page.
     * 
     * @param {Object}
     */
    constructor : function(params)
    {
      this.inherited(arguments);

      // custom class or class with no attributes.
      params.cls = params.cls || "";

      if (!params.itemId && params._templateDetails && params._templateDetails.id)
      {
        // module / template id
        params.itemId = params._templateDetails.id;
      }
    },

    // ////////////////////////////////////////////////////////////////////////////
    // Implementations

    postCreate : function()
    {
      this.inherited(arguments);

    },

    // .....................................................
    startup : function()
    {
      this.inherited(arguments);

      this._init();

      // this._subscribe();
      this._setEvents();
    },

    // ..................................
    _reset: function()
    {
      this.populate();
    },

    //.......................................
    _init: function()
    {
      let refStr = CampaignCtx.jData.getTITriggerRef();
      if (!refStr) return;
      this._itemList = refStr.split(",");
      if (Array.isArray(this._itemList) && this._itemList.length)
      {
        this.populate();
      }
    },

    //.........................................
    populate: function()
    {
      this._showSignals();
    },

    //..........................................
    _showSignals: function ()
    {
      // empty the configured signals
      if (this._cfgSegsCont)
      {
        domConstruct.empty(this._cfgSegsCont);
      }

      let refStr = CampaignCtx.jData.getTITriggerRef();
      if (!refStr) return;
      this._itemList = refStr.split(",");

      //this._itemList = CampaignCtx.jData.getSegmentRefList({segState: "target"});
      if (!Array.isArray(this._itemList) || !this._itemList.length) return;

      if (!this._cfgSegsCont)
      {
        this._cfgSegsCont = document.createElement("div");
        this._cfgSegsCont.classList.add("c3_srchVCrdsC", "c3_cols_nowrap", "c3_gpR8", "c3_pd_8h", "c3_pd_16v");
        this._cont.append(this._cfgSegsCont);
      }

      let lst = C3Context.goalData.getSignalsByIdList({idList: this._itemList});
      lst.forEach((s, i) => {
        let dv = document.createElement("div");
        dv.classList.add("c3_srchVCrd2", "c3_cols_nowrap");
        let dvIn = document.createElement("div");
        dvIn.classList.add("c3_srchVCrdIn", "c3_rows_nowrap_start", "c3_align_center");
        // // icon
        // let icnDv = document.createElement("div");
        // icnDv.classList.add("c3_srchVCrdIcnC", "c3_flex_center_1", "c3_pd_6", "c3-c-gray90");
        // let iCls = s.icon || "fa-users";
        // let icn = document.createElement("div");
        // icn.classList.add("c3_srchVCrdIcn", iCls);
        // icnDv.append(icn);
        // dvIn.append(icnDv);
        // name
        let nm = document.createElement("div");
        nm.classList.add("c3_srchVCrdTxt", "c3_flex_1", "c3_wordBreak");
        nm.classList.add("c_txt_lnk", "c3-c-blue40", "c3_btn");
        nm.textContent = s.displayName || s.name;
        this.own(on(nm, "click", () => {
          topic.publish("/trigger/summary", {_d: s});
        }));
        dvIn.append(nm);
        dv.append(dvIn);
        this._cfgSegsCont.append(dv);
      });
    },

    //.........................................
    _setEvents: function()
    {
    },

    //.....................................................
    destroy: function()
    {
      this.inherited(arguments);
    },

    _end : 0
  });
  

});
