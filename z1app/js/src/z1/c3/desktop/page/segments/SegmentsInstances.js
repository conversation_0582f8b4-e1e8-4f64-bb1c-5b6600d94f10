/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 *
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define([
  "dojo/_base/declare",
  "dojo/_base/lang",
  "dojo/on",
  "udc/udc",
  "z1/c3/desktop/start",
  "udc/core/rt/widgets/_BaseWidget",
  "dojo/text!z1/c3/desktop/page/templates/segments/SegmentsInstances.html",
  "dojo/i18n!z1/c3/desktop/page/nls/SegmentsInstancesRes",
  "z1/c3/desktop/page/modules/InstancesGallery",
  "z1/c3/desktop/data/modules/ModulesData",
  "z1/c3/desktop/page/interactions/segments/SegmentsUtil",
  "z1/c3/desktop/page/modules/modulesMigration",
  "z1/common/C3Util",
  "dojo/topic",
  "dojo/dom-construct",
  "z1/c3/desktop/page/C3AlertBox",
  "z1/c3/desktop/data/C3Context",
  "z1/c3/utils/Permissions",
  "z1/c3/desktop/page/auditTrail/AuditTrailDef"
],
function(declare, lang, on, udc, z1, BaseWidget, templateStr, Res,
  InstanceGallery, ModulesData, SegmentsUtil, modulesMigration, C3Util,
  topic, domConstruct, AlertBox, C3Context, UserACL, AuditTrailDef
)
{

  const _buttonSlot = (caller, d, el) => {
    el.classList.remove("c3_flex_center_1");
    el.classList.add("c3_cols_nowrap");
    let dv = document.createElement("div");
    dv.classList.add(...["btn-icn-sq", "btn-icn-sq-no-bdr", "btn-icn-sq-26",
                         "c3-c-icn-btn-blue", "c3_btn"]);
    let btnDiv = C3Util.useSvgDef("zi-ellipsis-h");

    dv.appendChild(btnDiv);
    el.appendChild(dv);

    caller.own(
      on(dv, "click", () => { _launchDropdownMenu(caller, d, btnDiv); })
    )
  }

  const _onEditTriggerByActivity = (caller, d) => {
    d["itemId"] = d.itemData.id;
    let feCtx = {
      ...d,
      _filter: (caller._filter) ? JSON.parse(JSON.stringify(caller._filter)) : void(0),
      FEvents: {"configureSegmentPalette": z1.Events.configureSegmentPalette2}
    };
    if (caller.instFltrType) feCtx.instFltrType = caller.instFltrType;
    z1.fireEvent(z1.Events.editSegmentPalette1, feCtx);
  }

  // edit
  const _onEdit = (caller, d) => {
    d["itemId"] = d.id;
    let feCtx = {
      cmd: "edit",
      editorType: d.isUpload ? 'upload' : 'define',
      itemData: d,
      viewType: "bu",
      _filter: (caller._filter) ? JSON.parse(JSON.stringify(caller._filter)) : void(0),
      FEvents: {"configureSegmentPalette": z1.Events.configureSegmentPalette2}
    };
    if (caller.instFltrType) feCtx.instFltrType = caller.instFltrType;
    if (caller.instFltrType === 'insession')
    {
      let ctx = {
        cmd: "edit",
        itemData: d,
        instFltrType: caller.instFltrType
      }
      z1.fireEvent(z1.Events.showSegmentEditor, ctx);
    }
    else if(caller.instFltrType === 'audience')
    {
      let ctx = {
        cmd: "edit",
        itemData: d,
        instFltrType: caller.instFltrType,
        journeyPage: "old",
      }
      z1.fireEvent(z1.Events.showCmdCenterAudienceSegment, ctx);
    } else {
      z1.fireEvent(z1.Events.editSegmentPalette1, feCtx);
    }

  }

  const _createEditConfig = (caller, d) => {
    return {
      name: Res.mnEditConfig,
      cls: [],
      cb: (params) => {
        if (params.action === "click")
        {
          _onEdit(caller, d);
        }
      }
    }
  }

  const _createShowAuditTrail = (caller, d) => {
    let name = Res.mnAuditTrail;
    return {
      name: name,
      cls: [],
      cb: (params) => {
        if (params.action === "click")
        {
          caller.auditTrailBtn._onAuditTrail(d.name);
        }
      }
    }
  }

  const _createDelete = (caller) => {
    const onClick = (params) => {
      let d = params.data;
      let feCtx = {"type": d.type, "id": d.id, "name": d.name};
      if (caller.instFltrType) feCtx.instFltrType = caller.instFltrType;
      caller._onDelete(feCtx);
    }

    return {
      name: Res.mnDelete,
      cls: ["c3_txt_red"],
      cb: (params) => {
        if (params.action === "click") onClick(params);
      }
    }
  }

  const _launchDropdownMenu = (caller, d, refContainer) => {
    // provide menu items list
    let mList = [
      _createEditConfig(caller, d),
      _createShowAuditTrail(caller, d)
    ];

    if (d.isEditable) {
      mList.push(_createDelete(caller));
    }

    require(["z1/common/components/PanelDropdownMenu"], (PanelDropdownMenu) => {
      const ddMenu = new PanelDropdownMenu({
        parent: caller,
        refContainer: refContainer,
        mList: mList,
        data: d
      });
      //refContainer.append(ddMenu.domNode);
      ddMenu.startup();
      caller.own(ddMenu);
    });
  }

  /**
   * Creates a widget to show Segments page.
   */
  return declare([BaseWidget, modulesMigration],
  {
    templateString : templateStr,
    Res: Res,

    /**
     * Creates an instance of this page.
     *
     * @param {Object}
     */
    constructor : function(params)
    {
      this.inherited(arguments);

      // Dynamic Segments or Uploaded Segments flow
      // "dynamic"|"uploaded"
      // default: dynamic
      params.instFltrType = params.instFltrType;

      if( params && params.idSfx )
      {
        this._idSfx = params.idSfx;
      }
      else
      {
        this._idSfx = "_id_c3fltr" + Math.ceil(Math.random()*100);
      }

      // title
      params._title = Res["_title_" + params.instFltrType] || Res._title;
      // custom class or class with no attributes.
      params.cls = params.cls || "c3_c3segsGlry_nocls";
      params.hideSort = params.hideSort === false ? false : true; // default true
      params.hideFilter = params.hideFilter === false ? false : true; // default true
      params.hideSwitch = params.hideSwitch === false ? false : true; // default true
      // ZMOB-15935
      params.hideFilterByInstances = params.hideFilterByInstances === false ? false : true; // default true

      // 'All Users' itemData placeholder
      params._allUsersItemData = {};
    },

    // ////////////////////////////////////////////////////////////////////////////
    // Implementations

    postCreate : function()
    {
      this.inherited(arguments);
      
      UserACL.checkPermission(this, this._cont, 'seg', ['c', 'r']);
      C3Util.setPgBGColorGrey();

      // open up the flag to allow creation of
      // timers that batch calls to the platform
      SegmentsUtil.sData.allowBatchCalls = true;
      // end all stale batch calls on the platform
      SegmentsUtil.sData.endBatchRecountStatus();
      // this.instFltrType == "dynamic" ? C3Util.handleUserTracking('viewed_listing_dynamic_segs', {}) : C3Util.handleUserTracking('viewed_listing_uploaded_segs', {});
      if (this.instFltrType == "dynamic")
      {
        C3Util.handleUserTracking('viewed_listing_dynamic_segs', {})
      }
      else if (this.instFltrType == "insession")
      {
        C3Util.handleUserTracking('viewed_listing_insession_segs', {})
      }
      else if (this.instFltrType == "uploaded")
      {
        C3Util.handleUserTracking('viewed_listing_uploaded_segs', {})
      }
      else if (this.instFltrType == "audience")
      {
       C3Util.handleUserTracking('viewed_listing_audience_segs', {})
      }
      if (this.instFltrType == "uploaded")
      {
        this.checkUpdateBtn.classList.add("c3_hide");
      }
    },

    // .....................................................
    startup : function()
    {
      this.inherited(arguments);

      this.populate();

      // let usdIn = (this.instFltrType === "uploaded") ? "uploadedSegment" : "segment";

      this._subscribe();
      this._setEvents();
    },

    // ..................................
    _reset: function()
    {
      domConstruct.empty(this._galleryCont);
      this.populate();
    },

    //.........................................
    populate: function()
    {
      this.fetchData();
      this._showGallery();

      if (this?.userCtx?.triggerBy === 'segmentUsers')
      {
        _onEditTriggerByActivity(this, {
          ...this.userCtx,
          cmd: "edit",
          viewType: "bu",
          editorType: "defined",
          itemData: {
            ...this?.userCtx?.itemData,
            id: `segment+${this?.userCtx.segmentId}`,
            name: this?.userCtx.segmentName,
            isML: false,
            custom: "sgement"
          }
        })
      }
      let usdIn = this.instFltrType == "audience" ? "audience" : "segment";
      AuditTrailDef.showAuditTrailBtn({_parent: this, _usedIn: usdIn, _type: "type", _attachPoint: "_subHCont1"});
    },

    //.........................................
    fetchData: function()
    {
      this._mData = [
      ];
    },

    //.........................................
    _showGallery: function()
    {
      let type4NoDataMsg = "segment";
      
      if (!this._filter)
      {
        // this._filter = this.instFltrType == "uploaded" ?  : {"Targeting Logic":["InSession"]};
        if (this.instFltrType == "dynamic")
        {
          this._filter = {"Targeting Logic":["Dynamic"]};
        }
        else if (this.instFltrType == "insession")
        {
          this._filter = {"Targeting Logic":["InSession"]};
        }
        else if (this.instFltrType == "uploaded")
        {
          this._filter = {"Targeting Logic":["File Upload"]}
          type4NoDataMsg = "segmentUploaded";
        }
      }
      
      let ctx = {
        _parent: this,
        _parentPgContainer: this._parentPgContainer,
        // segment|trigger|model|template
        type: "segment",
        type4NoDataMsg,
        cls: "c3_segsLv c3_segsOvLv c3_lv2 c3_lvNoSideBdr c3_lvNoBottomBdr",
        _filter: (this._filter) ? JSON.parse(JSON.stringify(this._filter)) : {},
        hideSort: this.hideSort,
        hideFilter: this.hideFilter,
        showAuditBtnOnNoData: true,
        hideFilterByInstances : this.hideFilterByInstances,
        hideSwitch: this.hideSwitch,
        _mData: JSON.parse(JSON.stringify(this._mData)),
        postFetchAll: (pfCtx) => {
          // fetched and processed all instances data
          this.markIfNeedsUpgrade({data: pfCtx.data});
        },
        _glryMix: {
          grdCls: "c3_mngGrd c3_mngGrdSeg",
          _mSortInfo: {
            colName: "lastUpdatedTime",
            isDesc: false
          },
          _filter: (this._filter) ? JSON.parse(JSON.stringify(this._filter)) : {},
          _nameSlot: (d, el) => {
            el.classList.add(...["c3_gpR6", "npc_ro"]);
            // add name as a tooltip text data attribute
            el.dataset.tooltipTxt = d.name;

            // Name
            var ttlDv = document.createElement("div");
            ttlDv.classList.add(...["c3_lvExpTtl", "c3_txt_blue2", "c3_txt_lnk", "c3_wordBreak", "c3_2l", "c3_clamp"]);
            ttlDv.title = d.name;
            ttlDv.textContent = d.name;
            el.append(ttlDv);

            if(this._ypc && d.id !== "system:allUsers")
            {
              this.own(on(ttlDv, "click", lang.hitch(this, function(){
                udc.log(d);
                _onEdit(this, d);
              })));
            }
            else
            {
              // Read-only
              ttlDv.classList.remove(...["c3_txt_blue2", "c3_txt_lnk"]);
              ttlDv.classList.add("c3_t4");
            }

            // version update
            if (d.isUpgradeable &&
              d.latestVersion !== void(0) && d.version != d.latestVersion)
            {
              let u = document.createElement("div")
              u.classList.add(...["c3_updBtn", "c3_fs-xs"]);
              u.title = "Using v" + d.version + ", new version " + d.latestVersion + " is available";
              if(this._ypc)
              {
                u.textContent = "update to v" + d.latestVersion;
                u.classList.add(...["c3_btn", "c3_txt_lnk", "c3_txt_blue"]);
                this.own(on(u, "click", lang.hitch(this, function(){
                  // udc.log(d);
                  this._showUpdateForSingle(d, "segment");
                })));
              }
              else
              {
                // Read-only
                u.textContent = "new v" + d.latestVersion + " is available";
              }
              el.appendChild(u);
            }
          },
//          _nameSlotGrd: (d, el, cont) => {
//            var b = document.createElement("div");
//            b.classList.add(...["c3_grdIB", "c3_flex_1", "c3_cols_nowrap", "c3_cols_spacing", "c3_hidden"]);
//
//            let dv = document.createElement("div");
//            dv.classList.add(...["c3_grdNameDesc", "c3_rows_nowrap_start", "c3_rows_spacing", "c3_align_center"]);
//
//            let nameCont = document.createElement("div");
//            nameCont.classList.add(...["c3_grdITtlC", "c3_flex_1", "c3_rows_nowrap", "c3_rows_spacing_xxe", "c3_align_center", "c3_hidden"]);
//            let name = document.createElement("div");
//            name.classList.add(...["c3_grdITtl", "c3_text_ellipsis"]);
//            name.textContent = d.name;
//            name.title = d.name;
//            nameCont.appendChild(name);
//            dv.appendChild(nameCont);
//
//            b.appendChild(dv);
//            el.appendChild(b);
//
//            if (this._ypc && d.id != "system:allUsers")
//            {
//              el.classList.add(...["c3_btn"]);
//              this.own(on(el, "click", lang.hitch(this, function(){
//                d["itemId"] = d.id;
//                udc.log(d);
//                z1.fireEvent(z1.Events.editSegmentPalette1, {
//                  cmd: "edit",
//                  editorType: d.isUpload ? 'upload' : 'define',
//                  itemData: d,
//                  viewType: "bu",
//                  FEvents: {"configureSegmentPalette": z1.Events.configureSegmentPalette2}
//                });
//              })));
//            }
//          },
//          _tagSlot: (d, el) => {
//            if (d.tag && d.tag.list && Array.isArray(d.tag.list) && d.tag.list.length > 0)
//            {
//              var dv = document.createElement("div");
//              dv.classList.add(...["c3_rows_wrap", "c3_align_center", "c3_justify_center", "c3_lvTags"]);
//              let tags = d.tag.list;
//              for (let i = 0; i < tags.length; i++)
//              {
//                let t = document.createElement("div");
//                t.classList.add(...["c3_lvTag", "c3_lvTag" + tags[i]]);
//                t.textContent = tags[i];
//                dv.appendChild(t);
//              }
//              el.classList.add("c3_j_c");
//              el.appendChild(dv);
//            }
//          },
//          _tagSlotGrd: (d, el) => {
//            if (d.tag && d.tag.grid && Array.isArray(d.tag.grid) && d.tag.grid.length > 0)
//            {
//              var dv = document.createElement("div");
//              dv.classList.add(...["c3_rows_wrap", "c3_align_center"]);
//              let tags = d.tag.grid;
//              // changing the order of display for grid view
//              for (let i = tags.length - 1; i > -1; i--)
//              {
//                let t = document.createElement("div");
//                t.classList.add(...["c3_lvTag", "c3_lvTagGrd", "c3_lvTag" + tags[i]]);
//                t.textContent = tags[i];
//                dv.appendChild(t);
//              }
//              el.appendChild(dv);
//            }
//          },
//          _imageSlotGrd: (d, el, cont) => {
//            // image
//            if (d.id === "system:allUsers")
//            {
//              let img = document.createElement("img");
//              img.classList.add(...["c3_grdCardImg"]);
//              img.loading = "lazy";
//              img.src = "/res/seg_all_users.png";
//              el.appendChild(img);
//            }
//            else if (d.imageUrl)
//            {
//              let img = document.createElement("img");
//              img.classList.add(...["c3_grdCardImg"]);
//              img.loading = "lazy";
//              img.src = d.imageUrl;
//              el.appendChild(img);
//            }
//            else
//            {
//              el.classList.add("c3_grdImgBg");
//            }
//
//            if (this._ypc && d.id != "system:allUsers")
//            {
//              el.classList.add(...["c3_btn"]);
//              this.own(on(el, "click", lang.hitch(this, function(){
//                d["itemId"] = d.id;
//                udc.log(d);
//                z1.fireEvent(z1.Events.editSegmentPalette1, {
//                  cmd: "edit",
//                  editorType: d.isUpload ? 'upload' : 'define',
//                  itemData: d,
//                  viewType: "bu",
//                  FEvents: {"configureSegmentPalette": z1.Events.configureSegmentPalette2}
//                });
//              })));
//            }
//          },
//          _descriptionSlotGrd: (d, el, cont) => {
//            // description
//            let desc = document.createElement("div");
//            desc.classList.add(...["c3_grdDescLbl"]);
//            let descIn = document.createElement("div");
//            descIn.classList.add(...["c3_grdIDescrIn", "c3_clamp", "c3_5l"]);
//            descIn.textContent = d.description;
//            desc.append(descIn);
//            el.appendChild(desc);
//
//            if (this._ypc && d.id != "system:allUsers")
//            {
//              el.classList.add(...["c3_btn"]);
//              this.own(on(el, "click", lang.hitch(this, function(){
//                d["itemId"] = d.id;
//                udc.log(d);
//                z1.fireEvent(z1.Events.editSegmentPalette1, {
//                  cmd: "edit",
//                  editorType: d.isUpload ? 'upload' : 'define',
//                  itemData: d,
//                  viewType: "bu",
//                  FEvents: {"configureSegmentPalette": z1.Events.configureSegmentPalette2}
//                });
//              })));
//            }
//          },
//          _populationSlotGrd: (d, el) => {
//            let dv = document.createElement("div");
//            dv.classList.add(...["c3_align_center", "c3_rows_spacing", "c3_rows_nowrap_start"]);
//            let st = document.createElement("div");
//            st.dataset.lvK = el.dataset.lvK;
//            // move lvK from top level
//            if (el.dataset.lvK)
//            {
//              delete el.dataset.lvK
//            }
//            st.classList.add(...["c3_grdStats", "c3_grdStatsActions"]);
//            st.textContent = d.actions;
//            dv.appendChild(st);
//            let lbl = document.createElement("div");
//            lbl.classList.add(...["c3_grdStatsLbl", "c3_flex_1", "c3_text_ellipsis"]);
//            lbl.textContent = Res.statsMessage;
//            lbl.title = Res.statsMessage;
//            dv.appendChild(lbl);
//            el.appendChild(dv);
//
//            if (this._ypc && d.id != "system:allUsers")
//            {
//              el.classList.add(...["c3_btn"]);
//              this.own(on(el, "click", lang.hitch(this, function(){
//                d["itemId"] = d.id;
//                udc.log(d);
//                z1.fireEvent(z1.Events.editSegmentPalette1, {
//                  cmd: "edit",
//                  editorType: d.isUpload ? 'upload' : 'define',
//                  itemData: d,
//                  viewType: "bu",
//                  FEvents: {"configureSegmentPalette": z1.Events.configureSegmentPalette2}
//                });
//              })));
//            }
//          },
          _creationTimeSlot: (d, el) => {
            // create child div to show both Time and Name in one column
            let t = (!isNaN(d.creationTime))
              ? new Date(new Date(d.creationTime)).toLocaleString("default", {
                year: 'numeric', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit'
              }) : "-";
            let tArr = t.split(",");
            t = tArr.slice(0, -1).join(", ") + " - " + tArr[tArr.length - 1];

            let timeDiv = document.createElement("div");
            timeDiv.textContent = t;

            let nameDiv = document.createElement("div");
            nameDiv.classList.add(...["c3_fs12", "c3-c-secondary"])
            if (d.createdByName != void(0)) nameDiv.classList.add(...["c3_txt_cap"]);
            let authorVal = d.createdByName || d.createdBy;
            nameDiv.textContent = typeof authorVal === 'object' ? "" : authorVal;

            el.classList.add(...["c3_cols_nowrap", "c3_cols_spacing"]);
            el.appendChild(timeDiv);
            el.appendChild(nameDiv);
          },
//          _creationTimeSlotGrd: (d, el) => {
//            el.textContent = (!isNaN(d.creationTime))
//                ? new Date(new Date(d.creationTime)).toLocaleString("default", {
//                  year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit'
//                }) : "-";
//          },

          _lastUpdatedTimeSlot: (d, el) => {
            // create child div to show both Time and Name in one column
            let t = (!isNaN(d.lastUpdatedTime))
              ? new Date(new Date(d.lastUpdatedTime)).toLocaleString("default", {
                year: 'numeric', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit'
              }) : "-";
            let tArr = t.split(",");
            t = tArr.slice(0, -1).join(", ") + " - " + tArr[tArr.length - 1];

            let timeDiv = document.createElement("div");
            timeDiv.textContent = t;
            let nameDiv = document.createElement("div");
            nameDiv.classList.add(...["c3_fs12", "c3-c-secondary"])
            if (d.lastUpdatedByName != void(0)) nameDiv.classList.add(...["c3_txt_cap"]);
            let authorVal = d.lastUpdatedByName || d.lastUpdatedBy;
            nameDiv.textContent = authorVal === 'Unknown' ? "" : authorVal;

            el.classList.add(...["c3_cols_nowrap", "c3_cols_spacing"]);
            el.appendChild(timeDiv);
            el.appendChild(nameDiv);
          },
//          _lastUpdatedTimeSlotGrd: (d, el) => {
//            el.textContent = (!isNaN(d.lastUpdatedTime))
//                ? new Date(new Date(d.lastUpdatedTime)).toLocaleString("default", {
//                  year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit'
//                }) : "-";
//          },
//          _lastUpdatedBySlot: (d, el) => {
//            el.title = d.lastUpdatedBy;
//            el.textContent = d.lastUpdatedBy;
//          },
//          _lastUpdatedBySlotGrd: (d, el) => {
//            el.title = d.lastUpdatedBy;
//            el.textContent = d.lastUpdatedBy;
//          },
          ___c3btnsSlot: (d, el) => {
            if (d.id === "system:allUsers") return;
            if(d.custom == "segment")
            {
              _buttonSlot(this, d, el);
              return;
            }
            let dv = document.createElement("div");
            dv.classList.add(...["c3_rows_nowrap", "c3_rows_spacing_x", "c3_align_center"]);
            // expand
            //let expandBtn = document.createElement("div");
            //expandBtn.classList.add(...["c3_btn", "ti-arrows-corner"]);
            //on(expandBtn, "click", lang.hitch(this, function(){
            //  udc.log(d);
            //  //console.log(d);
            //  //z1.fireEvent(z1.Events.editSegmentPalette, {
            //    //cmd: "edit",
            //    //itemData: d
            //  //});
            //}))
            //dv.appendChild(expandBtn)
            //clone
            //let cloneBtn = document.createElement("div");
            //cloneBtn.classList.add(...["c3_btn", "ti-files"]);
            //on(cloneBtn, "click", lang.hitch(this, function(){
            //  udc.log(d);
            //  //console.log(d);
            //  //z1.fireEvent(z1.Events.editSegmentPalette, {
            //    //cmd: "edit",
            //    //itemData: d
            //  //});
            //}))
            //dv.appendChild(cloneBtn)
            // edit 
            // let editBtn = document.createElement("div");
            // editBtn.classList.add(...["c3_btn", "ti-pencil"]);
            // this.own(on(editBtn, "click", lang.hitch(this, function(){
            //   d["itemId"] = d.id;
            //   udc.log(d);
            //   z1.fireEvent(z1.Events.editSegmentPalette1, {
            //     cmd: "edit",
            //     itemData: d,
            //     viewType: "bu",
            //     FEvents: {"configureSegmentPalette": z1.Events.configureSegmentPalette2}
            //   });
            // })));
            // dv.appendChild(editBtn)
            // delete
            if (this._ypc)
            {
              let delBtn = document.createElement("div");
              delBtn.classList.add(...["c3_flex_center_1", "c3_lvBtn_i", "c3_btn", "npc_h"]);
              delBtn.title = "Delete";

              let s = document.createElementNS('http://www.w3.org/2000/svg','svg');
              s.classList.add(...["c3i", "c3i_txt", "btn-icn-danger"]);
              let u = document.createElementNS('http://www.w3.org/2000/svg','use');
              u.classList.add(...["c3i_u"]);
              u.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', "#trash");
              s.append(u);
              delBtn.append(s);

              this.own(on(delBtn, "click", lang.hitch(this, function(){
                this._onDelete({"type": d.type, "id": d.id, "name": d.name});
                udc.log(d);
              })));
              dv.appendChild(delBtn);
            }
            el.appendChild(dv);
          },
//          ___c3btnsSlotGrd: (d, el) => {
//            if (d.id === "system:allUsers") return;
//
//            // removing padding div
//            // let dv1 = document.createElement("div");
//            // dv1.classList.add(...["c3_grdIF1"]);
//            // el.appendChild(dv1);
//
//            let dv = document.createElement("div");
//            dv.classList.add(...["c3_grdIF", "btns-h", "c3_rows_nowrap", "c3_rows_spacing", "c3_align_center"]);
//
//            // delete
//            if (this._ypc)
//            {
//              let delBtn = document.createElement("div");
//              delBtn.classList.add(...["c3_grdIDtlBtn", "c3_grdBtn_i", "c3_btn", "btn-n", "btn-fx", "ti-trash", "btn-danger-outline", "btn-no-outline"]);
//              delBtn.title = "Delete";
//              dv.appendChild(delBtn);
//
//              this.own(on(delBtn, "click", lang.hitch(this, function(){
//                this._onDelete({"type": d.type, "id": d.id, "name": d.name});
//                udc.log(d);
//              })));
//            }
//
//            // edit - removing edit button and making the whole card clickable
////            let editBtn = document.createElement("div");
////            editBtn.classList.add(...["c3_grdIDtlBtn", "c3_grdFBtnEdit", "c3_btn", "btn-fx", "btn-icn", "btn-sm", "btn-lightgrey", "c3_folder_btn_wrapper"]);
////            editBtn.classList.add(...["ti-pencil"]);
////            editBtn.title = "Edit";
////            this.own(on(editBtn, "click", lang.hitch(this, function(){
////              d["itemId"] = d.id;
////              udc.log(d);
////              z1.fireEvent(z1.Events.editSegmentPalette1, {
////                cmd: "edit",
////                itemData: d,
////                viewType: "bu",
////                FEvents: {"configureSegmentPalette": z1.Events.configureSegmentPalette2}
////              });
////            })));
////            dv.appendChild(editBtn);
////
//            el.appendChild(dv);
//          },

          // system:allUser shows at bottom of the list
          handleAllUser: function ()
          {
            let i = this._mData.findIndex(m => m.id === "system:allUsers");
            if (i !== -1)
            {
              let au = this._mData[i];
              this._mData.splice(i, 1);
              this._mData.push(au);
            }
          },
          // sorts
          _nameSort: function (sCtx)
          {
            let sortDescending = this._isSortDescending({fieldRef: sCtx.fieldRef});
            if(sCtx._mData && sCtx._mData.length)
            {
              sCtx._mData.sort(function(a, b)
              {
                // All Users segment shows either at top or bottom according to sort order
                var aBy = a.name || "";
                if(a.id === "system:allUsers")
                {
                  aBy = " - ";
                }
                var bBy = b.name || "";
                if(b.id === "system:allUsers")
                {
                  bBy = " - ";
                }
                return aBy.localeCompare(bBy) * [-1, 1][+!!sortDescending];
              });
              this.handleAllUser();
              this.sortNodes();
            }
          },
          _tagSort: function(sCtx)
          {
            let sortDescending = this._isSortDescending({fieldRef: sCtx.fieldRef});
            if(sCtx._mData && sCtx._mData.length)
            {
              sCtx._mData.sort(this._sortBy(sCtx.name, function(a)
              {
                if (!a.list) return "";
                var tagStr = "";
                if (Array.isArray(a.list))
                {
                  tagStr = a.list.join("");
                  // reduce resultant ASCII value for ML based tags
                  // to emphasize the ML based instances during ascending sort
                  if (a.length > 1 && a[a.list.length - 1] === "ML")
                  {
                    tagStr = "#" + tagStr;
                  }
                }
                return tagStr.toUpperCase();
              }, sortDescending));
              this.handleAllUser();
              this.sortNodes();
            }
          },
          _populationSort: function (sCtx)
          {
            let sortDescending = this._isSortDescending({fieldRef: sCtx.fieldRef});
            if(sCtx._mData && sCtx._mData.length)
            {
              sCtx._mData.sort(function(a, b)
              {
                var A = a.population || 0;
                var B = b.population || 0;

                return ((A < B) ? -1 : ((A > B) ? 1 : 0)) * [-1, 1][+!!sortDescending];
              });
              this.handleAllUser();
              this.sortNodes();
            }
          },
          _createdBySort: function(sCtx)
          {
            let sortDescending = this._isSortDescending({fieldRef: sCtx.fieldRef});
            if(sCtx._mData && sCtx._mData.length)
            {
              sCtx._mData.sort(this._sortBy(sCtx.name, function(a)
              {
                if (!a) return "";
                return a.toUpperCase();
              }, sortDescending));
              this.handleAllUser();
              this.sortNodes();
            }
          },
          _creationTimeSort: function(sCtx)
          {
            var sortDescending = this._isSortDescending({fieldRef: sCtx.fieldRef});
            if(sCtx._mData && sCtx._mData.length)
            {
              sCtx._mData.sort(function(a, b)
              {
                // If one-time-campaign is not run/started yet
                // then ' - ' is shown instead of the started-at (lastUpdatedTime)
                // This is checked based on 'isComplete' value
                // CampaignRow widget shows ' - ' for not-run yet campaigns.
                //
                // date is in long millisecond form : number
                // if missing value then use 1 as dummy
                var aTime = a.creationTime || 1;
                if(a.creationTime === "Invalid Date"){
                  aTime = 1;
                }
                var bTime = b.creationTime || "";
                if(b.creationTime === "Invalid Date"){
                  bTime = 1;
                }
                return (aTime - bTime) * [-1, 1][+!!sortDescending];
              });
              this.handleAllUser();
              this.sortNodes();
            }
          },
          _lastUpdatedBySort: function(sCtx)
          {
            let sortDescending = this._isSortDescending({fieldRef: sCtx.fieldRef});
            if(sCtx._mData && sCtx._mData.length)
            {
              sCtx._mData.sort(this._sortBy(sCtx.name, function(a)
              {
                if (!a) return "";
                return a.toUpperCase();
              }, sortDescending));
              this.handleAllUser();
              this.sortNodes();
            }
          },
          _lastUpdatedTimeSort: function(sCtx)
          {
            var sortDescending = this._isSortDescending({fieldRef: sCtx.fieldRef});
            if(sCtx._mData && sCtx._mData.length)
            {
              sCtx._mData.sort(function(a, b)
              {
                // If one-time-campaign is not run/started yet
                // then ' - ' is shown instead of the started-at (lastUpdatedTime)
                // This is checked based on 'isComplete' value
                // CampaignRow widget shows ' - ' for not-run yet campaigns.
                //
                // date is in long millisecond form : number
                // if missing value then use 1 as dummy
                var aTime = a.lastUpdatedTime || 1;
                if(a.lastUpdatedTime === "Invalid Date"){
                  aTime = 1;
                }
                var bTime = b.lastUpdatedTime || "";
                if(b.lastUpdatedTime === "Invalid Date"){
                  bTime = 1;
                }
                return (aTime - bTime) * [-1, 1][+!!sortDescending];
              });
              this.handleAllUser();
              this.sortNodes();
            }
          },
          // ~
          // stats
          populateStats: function (mData = [])
          {
            // Show abbreviated number
            // 1000 => 1K
            function abbrevNumber (el, num){
              el.textContent = (!isNaN(num))
                ? C3Util._abbreviateNumber(num) : "-";
              if (num != C3Util._abbreviateNumber(num))
              {
                el.title = num;
                el.classList.add("c3_abbrv" + C3Util._abbreviateNumberSfx(num));
              }
            }
            // 10000 => 10,000
            function numberWithCommas (el, num){
              el.textContent = (!isNaN(num))
                ? C3Util._numberWithCommas(num) : "-";
              // if (num != C3Util._numberWithCommas(num)) el.title = num;
            }

            this._mList.forEach((m) => {
              let i = mData.findIndex((md) => m.id === md.id);
              if (i !== -1)
              {
                // population
                let a = m.node.querySelector("[data-lv-k='population']");
                if (a)
                {
                  if (!m.node.classList.contains("c3_grdBx"))
                  {
                    // table
                    a.classList.remove("c3_waitBx");
                  }
                  else
                  {
                    // card
                    let aDv = m.node.querySelector(".c3_grdStat");
                    if (aDv) aDv.classList.remove("c3_waitBx");
                  }
                  let sPop = mData[i].population;
                  // if population value is unavl, 
                  // then count metric has not been recorded yet
                  // since no user must have converted yet.
                  numberWithCommas(a, sPop ? sPop : 0);
                }
                // ~
              }
            });
          },
          postCreateRowGrd: function (id, d, el)
          {
            
          }
        }
      };
      if (this._ypc === true)
      {
        ctx.createCallback = () => {
          if(this.instFltrType == "dynamic")
          {
            this._createAdvSegment('define')
          }
          else if (this.instFltrType == "insession")
          {
            this._createAdvSegment('insession')
          }
          else if (this.instFltrType == "audience")
          {
            this._createAdvSegment('audience')
          }
          else
          {
            this._createAdvSegment('upload')
          }
        };
      }
      this.instanceGallery = new InstanceGallery(ctx);
      this.instanceGallery.startup();
      domConstruct.place(this.instanceGallery.domNode, this._galleryCont);
      this.own(this.instanceGallery);
    },

    // .........................................
    _onDelete: function(dCtx)
    {
      function _onDeleteConfirm ()
      {
        udc.showBusy(this._galleryCont);
        let ctx = {
          id: dCtx.id,
          type: "segment"
        };
        ModulesData.api.deleteData(ctx).then(lang.hitch(this, function(data)
        {
          udc.log(data);
          udc.hideBusy();
          // udc.success("Deleted");
          this._reset();
        }), lang.hitch(this, function(error)
        {
          udc.hideBusy();
          C3Util.handleServerError(error);
        }));
      }
      
      var messages = {
        popupTitle: Res.deleteSegmentTitle,
        popupMsg: Res.deleteSegmentMsg,
        popupInfo: Res.deleteSegmentInfo,
        popupFailed: Res.deleteFailed,
        popupFailedInfo: Res.deleteFailedInfo
      };
      
      var expType= "segment";
      
      C3Context.goalData.ifSegmentIsNotInUse(dCtx.id).then(
        lang.hitch(this, function(result)
        {
          // Ok to delete Segment
          udc.log(result);
          ModulesData.api.deleteMessage(this, expType, dCtx.id, dCtx.name, _onDeleteConfirm, messages);
        }), lang.hitch(this, function(result)
        {
          var alertPopup = function(componentRef)
          {
            udc.log(result);
            // Segment is in use
            let rList = "";
            if (Array.isArray(result) && result.length)
            {
              result.sort((a, b) => {return a.type > b.type;});
              let nameMap = {
                "c1": "Scheduled Interaction",
                "campaign": "Triggered Interaction",
                "journey": "Journey"
              };
              rList = result.map((refItem) => {
                let obj = {};
                return nameMap[refItem.type] + ": '" + refItem.name + "'";
              }).join(", ");
            }
            var alertParams = {
              "dialog_title": Res.alertDelSegTitle,
              // message - is in use in either Goal, Campaign or Journey..
              "dialog_msg": Res.alertDelSegMsg + ((rList && rList !== "") ? (" Used by: " + rList) : "")  
            };
            var alertDialog = new AlertBox(alertParams);
            alertDialog.startup();
            alertDialog.setParent(componentRef);
            componentRef.own(alertDialog);
          };
          ModulesData.api.deleteMessage(this, expType, dCtx.id, dCtx.name, _onDeleteConfirm, messages, alertPopup);
          return;
        }));
    },

    ////......................................
    //// show/hide items based on the counting
    //// status
    //// @param segments/all data
    //_setCountingStatus: function (data)
    //{
    //  SegmentsUtil.sData.countingStatus().then((rCtx) => {
    //    // exited this screen
    //    if (!this._cont) return;
    //
    //    if (this.firstCountStatus !== 1)
    //    {
    //      // first time got countStatus result
    //      this.firstCountStatus = 1;
    //      this._cont.dataset.firstCountStatus = this.firstCountStatus;
    //    }
    //
    //    // All Users
    //    this._displayRecountStatus();
    //    // individual segments
    //    this._displayIndvRecountStatus();
    //  });
    //},

    //........................................
    // /countStatus gives totalSoFar value
    // show it in segment tile
    _displayIndvRecountStatus: function ()
    {
      var rsList = SegmentsUtil.sData.recountStatuses;
      if (!Array.isArray(rsList)) return;

      if (this.instanceGallery)
      {
        this.instanceGallery.gotStatsData({statsData: rsList});
      }

      //rsList.forEach((rs) => {
      //
      //  // Add stats attributes to the
      //  // data
      //  if(rs.id === 'system:allUsers')
      //  {
      //    // 'All Users'
      //    lang.mixin(this._allUsersItemData, rs);
      //  }
      //  if(rs.id && this._data[rs.id])
      //  {
      //    lang.mixin(this._data[rs.id], rs);
      //  }
      //
      //  // show the all users count in the header
      //  this._showTotalCustomers([this._allUsersItemData]);
      //
      //  var matchedPBItem = this._paletteItems.find((pbItemTmp) => {
      //    return (pbItemTmp.itemData && pbItemTmp.itemData.itemId === rs.id);
      //  });
      //
      //  // Individual Segment
      //  if(matchedPBItem)
      //  {
      //    // set stats on itemData
      //    lang.mixin(matchedPBItem.itemData, rs);
      //    // update population and recount icon
      //    matchedPBItem._setStatistics();
      //  }
      //});
    },

    //....................................
    _displayRecountStatus: function ()
    {
      // mark if any segment or all-users segment is counting
      // Here not using isIndvCounting as that might conflict
      // with All User's dom items shown/hidden.
      // isAnyCounting is used in /countStatus poll ending
      // decision.
      this._cont.dataset.isAnyCounting = (SegmentsUtil.sData.isAnyRecounting()) ? "1" : "0";

      // compare with previous counting status.
      if(this._countingCtx) udc.log("ctx " + JSON.stringify(this._countingCtx, null, 2));
      if(SegmentsUtil.sData.recountStatus) udc.log("util " + JSON.stringify(SegmentsUtil.sData.recountStatus, null, 2));
      if (this._countingCtx
          && this._countingCtx.isCounting === true
          && SegmentsUtil.sData.recountStatus.isCounting === false)
      {
        // finished counting
        // Get /segments/all and get the updated
        // lastRunStart, lastRunEnd
        udc.log("finished counting");
        this._getAllSegments();
      }

      // copy locally recount status
      this._countingCtx = JSON.parse(JSON.stringify(SegmentsUtil.sData.recountStatus));
      // if (SegmentsUtil.sData.isAnyRecounting())
      if (SegmentsUtil.sData.recountStatus && typeof SegmentsUtil.sData.recountStatus.isCounting === 'boolean')
      {
        this._cont.dataset.isCounting = (SegmentsUtil.sData.recountStatus.isCounting) ? "1" : "0";
        // set counting percent
        // this._recountPercentC.textContent = SegmentsUtil.sData.recountStatus.percentCompletion;
        let lastRecountTimeCtx = SegmentsUtil.sData.lastRecountDateDisplayStr();
        this._cont.dataset.rctBfr = "1";
        if(lastRecountTimeCtx
          && lastRecountTimeCtx.msg !== "never-run"
          && lastRecountTimeCtx.lastRunStart)
        {
          //if(lastRecountTimeCtx.lastRunStar !== this._recountTimeC.textContent)
          //{
          //  this._recountTimeC.textContent = lastRecountTimeCtx.lastRunStart;
          //}
          //if(lastRecountTimeCtx.duration !== this._recountDurationC.textContent)
          //{
          //  this._recountDurationC.textContent = lastRecountTimeCtx.duration;
          //}
        }
        else if(lastRecountTimeCtx
          && lastRecountTimeCtx.msg === "never-run")
        {
          // never ran recount before, new namespace
          this._cont.dataset.rctBfr = "0";
        }
      }
    },

    //..........................................
    _createDropdownButton: function(ddCont)
    {
      // create new
      let createNew = document.createElement("div");
      createNew.classList.add(...["c3_btn", "btn-n", "btn-primary", "c3_rows_nowrap", "c3_rows_spacing", "c3_align_center"]);
      createNew.title = "Create New";
      createNew.textContent = "Create New";
      this.own(on(createNew, "click", lang.hitch(this, function(){
        if(this.instFltrType == "dynamic")
        {
          this._createAdvSegment('define');
        }
        else if (this.instFltrType == "insession")
        {
          this._createAdvSegment('insession');
        }
        else
        {
          this._createAdvSegment('upload');
        }
      })));
      ddCont.appendChild(createNew);
    },

    // ..........................................
    _createAdvSegment: function(editorType)
    {
      if (!this._ypc) return;

      if (editorType === "insession")
      {
        var ctx = {
          cmd: 'new',
          instFltrType: this.instFltrType,
        };
        z1.fireEvent(z1.Events.showSegmentEditor, ctx);
        udc.sendContextHelpTopic();
      }
      else if (editorType === "audience")
      {
        var ctx = {
          cmd: 'new',
          instFltrType: this.instFltrType,
          _parent: this,
          cancelCb: () => {
            z1.fireEvent(z1.Events.showCmdCenterAudienceSegment, {});
          }
        };
        z1.fireEvent(z1.Events.showCmdCenterAudienceSegment, ctx);
        udc.sendContextHelpTopic();
      }
      else
      {
        var ctx = {
          cmd: "new",
          editorType: editorType,
          viewType: "bu",
          instFltrType: this.instFltrType,
          FEvents: {"configureSegmentPalette": z1.Events.configureSegmentPalette2}
        };
        z1.fireEvent(z1.Events.editSegmentPaletteAdvnc, ctx);
        udc.sendContextHelpTopic();
      }

    
    },

    //.........................................
    _setEvents: function()
    {
      //this.own(
      //);
      if (this._ypc === true && this.checkUpdateBtn)
      {
        this.own(
          on(this.checkUpdateBtn, "click", lang.hitch(this, function () {
            this._checkForUpdate({type: "segment", container: this._cont});
          }))
        );
      }
    },

    //.........................................
    _subscribe: function()
    {
      this.own(
        // recount status
        topic.subscribe("/segments/recountstatus", lang.hitch(this, function (ctx){
          if (!this._cont) return;
          if (this.firstCountStatus !== 1)
          {
            // first time got countStatus result
            this.firstCountStatus = 1;
            this._cont.dataset.firstCountStatus = this.firstCountStatus;
          }
          this._displayRecountStatus();
          // individual segments
          this._displayIndvRecountStatus();
          if (this._rctIterator === void(0))
          {
            this._rctIterator = 1;
          }
          else if (this._rctIterator === 3)
          {
            // After every two /countStatuses call
            // reset
            this._rctIterator = 1;
            if(SegmentsUtil.sData.isAnyRecounting())
            {
              //// refresh list, if still recounting
              //this._getAllSegments();
              // refresh population of segments
              // Note the /countStatuses response data
              // have 'totalSofar' value which is used
              // to update the population of recounted
              // segments in the skipped iterations
              // when the /stats call is not
              // made.
              // commented-out - using /countStatuses
              // 'totalSoFar' + percentCompletion for
              // population.
              // this._fetchAllSegmentsStats();
            }
          }
          else
          {
            this._rctIterator++;
          }
        })),
        // end recount
        topic.subscribe("/segments/endcounting", lang.hitch(this, function (ctx){
          // refresh list, if still recounting
          //this._getAllSegments();
        })),
        // recount
        topic.subscribe("/segments/recount", lang.hitch(this, function (ctx){
          if (!this._cont) return;
          if (ctx && ctx.status === "processing")
          {
            this._displayRecountStatus();
            // recounting started
            if (this.totalCustomers) this.totalCustomers.textContent = "...";
            // all segments
            this._showBusyIcon();
          }
        })),
        // filter
        topic.subscribe("/filterctrl/change", lang.hitch(this, function (ctx){
          // type, value
          if (ctx.type === "segment")
          {
            this._filter = ctx.value;
          }
        })),
        // instances upgraded
        topic.subscribe("/instances/upgraded", lang.hitch(this, function (ctx = {}){
          // type
          if (ctx.type === "segment")
          {
            if (this._galleryCont) domConstruct.empty(this._galleryCont);
            this.populate();
          }
        }))
      );
    },

    //.....................................................
    destroy: function()
    {
      // close the flag to block creation of
      // timers that batch calls to the platform
      SegmentsUtil.sData.allowBatchCalls = false;
      // end all future batch calls to the platform
      SegmentsUtil.sData.endBatchRecountStatus();

      this.inherited(arguments);
      C3Util.removePgBGColorGrey();
    },

    _end : 0
  });


});
