/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define([
  "dojo/_base/declare", 
  "dojo/_base/lang",
  "dojo/on",
  "udc/udc",
  "udc/core/commons/loader",
  "z1/c3/desktop/start",
  "udc/core/rt/widgets/_BaseWidget",
  "dojo/text!z1/c3/desktop/page/templates/featureGallery/FeatureGallery.html",
  "dojo/i18n!z1/c3/desktop/page/nls/FeatureGalleryRes",
  "z1/common/components/PanelFilter",
  "z1/c3/desktop/data/modules/ModulesData",
  "z1/c3/desktop/page/featureGallery/FeatureGallery2",
  "z1/c3/desktop/page/util/C3SearchBoxPg",
  "z1/c3/desktop/page/util/SearchTableUtil",
  "z1/common/C3Util",
  "dojo/topic",
  "dojo/Deferred",
  "dojo/promise/all",
  "dojo/dom-class",
  "dojo/dom-construct"
],
function(declare, lang, on, udc, loader, z1, BaseWidget, templateStr, Res,
  PanelFilter, ModulesData, FeatureGallery2, C3SearchBoxPg, SearchTableUtil,
  C3Util, topic, Deferred, all, domClass, domConstruct
)
{

  /**
   * Creates a gallery widget
   * Usage:
   *   FeatureGallery({
   *     _parent [required]: this,
   *     _parentPgContainer [optional],
   *     // segment|trigger|model|template
   *     type [required]: "segment",
   *     // retail|banking|..
   *     category [optional]: "retail",
   *     cls [optional]: "c3_segsFtrGlry" - custom class name
   *     hideSort {boolean} [optional] [default: false]
   *        - true - hides the sort button
   *        and sort widget is not created
   *     showSearch {boolean} [default: true]
   *     Shows search box to filter table rows.
   *   });
   * 
   * Data is loaded from:
   * ModulesData.js::getCoreModulesByType() [optional]
   * ModulesData.js::fetchAllModules()
   */
  return declare(BaseWidget,
  {
    templateString : templateStr,
    Res: Res,
    
    /**
     * Creates an instance of this page.
     * 
     * @param {Object}
     */
    constructor : function(params)
    {
      this.inherited(arguments);

      params._idSfx = params._idSfx || "_id_c3ftrGlry" + Math.ceil(Math.random()*100);

      // custom class or class with no attributes.
      params.cls = params.cls || "c3_c3ftrGlry_nocls";

      params.unqLnkFltr = params.unqLnkFltr || "fltr" + C3Util.getRandomStr(3);

      // filter value
      params._filter = {};
      // search
      params.showSearch = params.showSearch || true;
      // references to gallery item nodes
      params._mList = [];
      params.customBehaviorForIds = ["campaign", "campaignonce"];
      // fetch requests deferred references
      params._dfrList= [];
    },

    // ////////////////////////////////////////////////////////////////////////////
    // Implementations

    postCreate : function()
    {
      this.inherited(arguments);
      
      if (this.showSearch !== true) this._srchC.remove();
    },

    // .....................................................
    startup : function()
    {
      if (this._hRHS) this.createHeadRHS(this._hRHS);
    
      this.inherited(arguments);

      this._showFilter().then(() => {
        this._filter = this.filterCtrl._getLocalStorageFilter();
        this._fetchAll();
      });
      
      this._subscribe();
      this._setEvents();
    },

    //..................................
    _reset: function()
    {
      this._mList = [];

      domConstruct.empty(this._bodyCont);
    },

    //.........................................
    populate: function()
    {
      this._prepareTags();
      this._showGallery();
      
      if (this.showSearch) this._searchList();
    },

    _prepareTags: function()
    {
      if (!Array.isArray(this._mData) || !this._mData.length)
      {
        return;
      }
      this._mData.forEach(item => {
        if(item.tag && item.tag["Targeting Logic"] && Array.isArray(item.tag["Targeting Logic"]))
        {
          let targetLogicTags = item.tag["Targeting Logic"];
          targetLogicTags.forEach((tag) => {
            if (tag.value && tag.value === "ML Driven")
            {
              item["class"].push("ml");
            }
          });
        }
      })
    },

    //.........................................
    _showFilter: function()
    {
      let fCtx = {};
      if (this.type) fCtx.type = this.type;
      if (this.category) fCtx.category = this.category;
      if (this.group) fCtx.group = this.group;
      
      return ModulesData.api.getFilterByType(fCtx).then(lang.hitch(this, function(fData){
        this._filterList = (Array.isArray(fData)) ? fData : [];

        let fCtx = {
          _parent: this,
          cls: "c3_glryFltr",
          unqLnkFltr: this.unqLnkFltr,
          // optional
          hideSort: this.hideSort,
          type: this.type,
          filterList: this._filterList,
          useLocalStorage: true,
          storageName: this._usedFor,
          // callback
          onChange: (f = {}) => {
            udc.log(f);
            this._filter = (f) ? JSON.parse(JSON.stringify(f)) : {};
            // process filter change
            this._reset();
            this._fetchAll();
          }
        };
        if (this._fltrBtnsContainer)
        {
          fCtx["_fltrBtnsContainer"] = this._fltrBtnsContainer;
          if (this._fltrBtnsContainerPos)
          {
            fCtx["_fltrBtnsContainerPos"] = this._fltrBtnsContainerPos;
          }
        }
        if (this._selFltrCont)
        {
          fCtx["_fltrSelItemsCont"] = this._selFltrCont;
          if (this._fltrSelItemsContPos)
          {
            fCtx["_fltrSelItemsContPos"] = this._fltrSelItemsContPos;
          }
        }
        this.filterCtrl = new PanelFilter(fCtx);
        this.filterCtrl.startup();
        domConstruct.place(this.filterCtrl.domNode, this._fltrCont);
        this.own(this.filterCtrl);
      }));
    },

    //.........................................
    _showGallery: function()
    {
      domConstruct.empty(this._bodyCont);

      if (!Array.isArray(this._mData)
        || !this._mData.length
      )
      {
        // no - data
        // show no data div
        this.noDataDiv.classList.remove("c3_hide");
        return;
      }
      this.noDataDiv.classList.add("c3_hide");

      // fetch and show stats
      this._getCommonStats();
      this._getStatsByType(this.type);
      
      var glryCtx = {};
      
      if(this.viewType === "list")
      {
        glryCtx = {
          _parent: this,
//        _parentPgContainer: this._parentPgContainer,
          _usedFor: this._usedFor,
          type: "actiontemplate",
          cls: this.cls,
          hideSort: true,
          _cbObj: this._cbObj,
          _customPublicTypeList: this._customPublicTypeList,
          typeMap: this.typeMap,
          viewType: this.viewType,
          hideSort: this.hideSort,
          hideFilter: this.hideFilter,
          hideSwitch: this.hideSwitch,
          _searchList: this._searchList,
          _mData: JSON.parse(JSON.stringify(this._mData)),
          createHeadRHS: this.createHeadRHS,
          _glryMix: (this._glryMix) ? this._glryMix : void(0)
        }
      }
      else
      {
        glryCtx = {
          _parent: this,
          // segment|trigger|model|template
          type: this.type,
          // category: "banking",
          cls: this.cls,
          _mData: JSON.parse(JSON.stringify(this._mData)),
          cbObj: this._cbObj,
          customPublicTypeList: this._customPublicTypeList
        };
        if (this._glryMix) lang.mixin(glryCtx, this._glryMix);
      }
      this.featureGallery2 = new FeatureGallery2(glryCtx);
      this.featureGallery2.startup();
      domConstruct.place(this.featureGallery2.domNode, this._bodyCont);
      this.own(this.featureGallery2);
    },

    //.............................................
    // get gallery item's HTML element by id
    _sort: function ()
    {
    },

    //.............................................
    // get gallery item's HTML element by id
    _getMItemDomById: function (mId)
    {
      var m = this._getMItemById(mId);
      if (m) return m.node;
      return this._cont.querySelector("[data-glry-m-id=' + mId + ']");
    },

    //.......................................................
    /**
     * Add custom buttons etc to right hand side on top bar.
     * 
     * @param {HTMLElement} rhsCont right hand side container
     */

    createHeadRHS: function (rhsCont)
    {
      // Note, leave this empty
      // Create this method in parent and
      // pass it in constructor
    },

    //.............................................
    // get gallery item by id
    // @return {id, data, node} - data is the module
    // data
    _getMItemById: function (mId)
    {
      let i = this._mList.findIndex((m) => {
        return mId === m.id;
      });
      if (i !== -1) return this._mList[i];
      return;
    },

    //.............................................
    // Gallery item list
    _fetchAll: function ()
    {
      var deferr = new Deferred();

      // get static core modules (SI/TI)
      this._mData = ModulesData.api.getCoreModulesByType(this.type);

      let pCtx = {};
      pCtx["type"] = this.type;
      if (this.category) pCtx["category"] = this.category;
      if (this.group) pCtx["group"] = this.group;
      // filter
      pCtx["filterObj"] = {};
      if (this._filter && Object.keys(this._filter).length)
        Object.assign(pCtx["filterObj"], this._filter);

      udc.showBusy(this._cont);
      
      var df = ModulesData.api.fetchAllModules(pCtx).then(lang.hitch(this, function(data){
        if (Array.isArray(data) && data.length)
        {
          this._mData = [...this._mData, ...data];
        }
        udc.hideBusy();
        this.populate();
        deferr.resolve(data);
      }), lang.hitch(this, function (err){
        udc.hideBusy();
        udc.log(err);
        if (err === "cancelled") deferr.resolve(true);
        else
        {
          this.populate();
          if (err && (err.message || err.reason)) udc.info(err.message || err.reason);
          deferr.reject(err);
        }
      }));
      this._dfrList.push({k: "_fetchAll", deferr: df});

      return deferr.promise;
    },

    //............................................
    // Fetch all stats
    _fetchAllStats: function()
    {
      var deferr = new Deferred();
      all([
        this._getCommonStats()
        // , this._getStatsByType(this.type)
      ]).then(lang.hitch(this, function(results)
      {
        // got all stats
        // sort gallery
        // this._sort();
        deferr.resolve(true);
      }), lang.hitch(this, function(err)
      {
        udc.log(err);
        deferr.reject(err);
      }));

      return deferr.promise;
    },

    //.............................................
    // Stats used in all type of modules
    _getCommonStats: function ()
    {
      var deferr = new Deferred();
      // implement
      // ModulesData.getCommonStats().then(lang.hitch(this, function(data){
      //   // show in module items
      //   this._showAllCommonStats(data)
      //   deferr.resolve(data);
      // }));
      deferr.resolve(true);
      return deferr.promise;
    },

    //......................................
    _showAllCommonStats: function(data)
    {
      if (!Array.isArray(data)) return;
      data.forEach((s) => {
        var m = this._getMItemById(s.id);
        m.stats = s;
        this._showCommonStats(s);
      });
    },

    //......................................
    _showCommonStats: function(s)
    {
      // this._getMItemDomById(s.id);
    },

    //.............................................
    // Stats by 'type'
    // Module/gallery type specific stats
    _getStatsByType: function (mType)
    {
      var deferr = new Deferred();
      // implement
      // ModulesData.getStatsByType(mType).then(lang.hitch(this, function(data){
      //   // show in module items
      //   if (typeof this["_" + mType + "ListStats"] === 'function')
      //   {
      //     this["_" + mType + "ListStats"](data);
      //   }
      //   deferr.resolve(data);
      // }));
      deferr.resolve(true);
      return deferr.promise;
    },

    //.............................................
    // segment

    //.............................................
    _segmentListStats: function (tStats)
    {
      if (!Array.isArray(tStats)) return;
      tStats.forEach((s) => {
        this._segmentStats(s);
      });
    },

    //.............................................
    _segmentStats: function (s)
    {
      // show stats in gallery item
      // ~
    },

    // end - segment
    //.............................................

    //.............................................
    // template

    //.............................................
    _templateListStats: function (tStats)
    {
      if (!Array.isArray(tStats)) return;
      tStats.forEach((s) => {
        this._templateStats(s);
      });
    },

    //.............................................
    _templateStats: function (s)
    {
      // show stats in gallery item
      // ~
    },
    
    //..............................................................
    _searchList: function (ctx = {})
    {
      domConstruct.empty(this._srchC);
      if (this.sel && typeof this.sel.destroy === 'function')
      {
        this.sel.destroy();
        // // instace already exists - no need to recreate
        // return;
      }
      // No data to filter.
      if (!Array.isArray(this._mData) || !this._mData.length)
      {
        this._srchC.classList.remove("c3_pd_l-16");
        return;
      }

      // // container
      // if (!this._srchC)
      // {
      //   this._srchC = document.createElement("div");
      //   this._srchC.classList.add("c3_cols_nowrap");
      //   this._cont.prepend(this._srchC);
      // }

      let sel;
      let sCtx = {
        _parent: this,
        ...ctx,
        cls: "c3_tblSrchWidth c3_tblSrch c3_flex_1 c3_html_fld c3_fld_deco",
        clsInput: "c3_ffm-inh c3_html_fld_h c3_pd_1v",
        placeholder: Res.searchPlaceholder,
        // typeahead/autocomplete
        showTypeahead: true,
        // setFocusOnReady: true,
        doNotShowListOnVeryFirstFocus: true,
        // destroyOnLeave: true,
        closeListOnScroll: true,
        // icon on left
        showIcon: true,
        showDownArrowOnHover: true,
        // Do not clear widget after
        // onChange is called.
        clearAfterSelection: false,
        // Filter list by typed word.
        // Overwriting default filter behavior.
        filterData: (cCtx = {}) => {
          this.filterData(cCtx);
        },
        isValidValue: (e) => {
          // // 0. check entered item exists
          // return this._isValidValue(e);
          return true;
        },
      };
      // value
      if (this.e !== void(0) && this.e.length) sCtx.value = this.e;
      sel = new C3SearchBoxPg(sCtx);
      this._srchC.append(sel.domNode);
      sel.startup();
      this.own(sel);
      this.sel = sel;
    },

    //.........................................................
    /**
     * Filtering criteria used in Search box to filter
     * table data.
     * 
     * @param {*} cCtx 
     */
    filterData: function (cCtx = {})
    {
      //---------------------------------------
      // Note: this method can be overidden by
      // the parent component. Pass it as
      // part of params in constructor
      //---------------------------------------

      let srchCtx = {
        // C3SearchBoxPg instance
        sel: this.sel,
        ...cCtx
      };
      // If list view
      if(this.viewType === "list")
      {
        // C3Table2 instance 
        srchCtx.table2 = this.featureGallery2.listView;
      }
      else
      {
        srchCtx.table2 = this.featureGallery2;
      }
      // if its card view
      // 
      SearchTableUtil.filterData(srchCtx);
    },
    // end - template
    //.............................................

    //.........................................
    _setEvents: function()
    {
      //this.own(
      //);
    },

    //.........................................
    _subscribe: function()
    {
      this.own(
        // sort
        topic.subscribe("/sortctrl/change", lang.hitch(this, function(sortCtx){
          if (this.type !== sortCtx.type)
          {
            // not for this gallery
            return;
          }
          this._sortCriteria = sortCtx.value;
          // this._sort();
        })),
        // refresh - page refresh
        topic.subscribe("/interactions/refresh", lang.hitch(this, function()
        {
          this._reset();
          this._fetchAll();
        }))
      );
    },

    //.....................................................
    destroy: function()
    {
      try
      {
        if (Array.isArray(this._dfrList) && this._dfrList.length)
        {
          this._dfrList.forEach((df) => {
            if (df.deferr && typeof df.deferr.isFulfilled == 'function')
            {
              if (!df.deferr.isFulfilled()) df.deferr.cancel("cancelled");
            }
          })
        }
      }
      catch(er){}

      this.inherited(arguments);
    },

    _end : 0
  });
  

});
