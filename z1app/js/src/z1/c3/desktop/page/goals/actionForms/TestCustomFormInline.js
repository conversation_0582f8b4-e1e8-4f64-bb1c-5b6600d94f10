/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define([
  "dojo/_base/declare", 
  "dojo/_base/lang",
  "dojo/on",
  "udc/udc",
  "z1/c3/desktop/start",
  "dijit/registry",
  "udc/core/rt/widgets/_BaseWidget",  
  "dojo/text!z1/c3/desktop/page/templates/goals/actionForms/TestCustomFormInline.html",
  "dojo/i18n!z1/c3/desktop/page/nls/TestCustomFormInlineRes",
  "dojo/dom-style",
  "dojo/dom-construct",
  "dijit/Dialog",
  "dijit/form/TextBox",
  "dijit/form/Textarea",
  "dijit/form/Button",
  "dojo/dom-class",
  "dojo/dom-style"
],
function(declare, lang, on, udc, z1, registry, BaseWidget,  templateStr, Res, style, domConstruct, 
		Dialog, Textbox, Textarea, Button, domClass, domStyle)
{

  /**
   * Creates a widget to show C3 landing page.
   */
  return declare(BaseWidget,
  {
    templateString : templateStr,
    Res: Res,
    _cb: null,
    _parentDijit: null,
    // supported Form controls
    _controlTypesObj: {
      INPUT: "input",
      TEXT: "text",
      TEXTAREA: "textarea"
    },
    _controlTypes: null,
    formDialog: null,
    _elements: null,
    /**
     * Creates an instance of this page.
     * 
     * @param {Object}
     * @param {Object}
     *          parentNode
     */
    constructor : function(params, parentNode)
    {
      this.inherited(arguments);
      this._parent = parentNode;
      udc.log("params" , params);
      udc.alert("testing: ZMOB-45, Action specific Form.")
      
      this._controlTypes = [
                            this._controlTypesObj.INPUT, 
                            this._controlTypesObj.TEXT, 
                            this._controlTypesObj.TEXTAREA
                            ];
      this._rgControlTypes = new RegExp("^(" + this._controlTypes.join("|") + ")$", "i");
      this._elements = [];
    },

    // ////////////////////////////////////////////////////////////////////////////
    // Implementations

    postCreate : function()
    {
      this.inherited(arguments);    

    },

    /**
     * Create the formDialog which holds the input parameters for the action
     * that has been dropped.
     */
    startup : function()
    {
      this.inherited(arguments);

      // popup form.
      //this.formDialog = new Dialog({
      //    title: this.dialog_title,
      //    style: "width: " + this._calcWidth() + ";height:300px;"
      //});
      
      // iterate over the params array to create the inputs dynamically
      for(var i=0; i < this.formParam.length; i++) 
      {
    	  if(this.formParam[i].name && this.formParam[i].name !== "")
    	  {
    	    this._createControlWidget(this.formParam[i], i);
        } 
      }
      // popup form
      //dojo.place(this.dialogBody, this.formDialog.containerNode);
      //this.formDialog.show();
      //this._elements.push(this.formDialog);
    },

    //..........................................................
    _calcWidth: function()
    {
      if(this.c3DialogSize)
      {
        if(this.c3DialogSize.toLowerCase() === "large")
        {
          var bodyWidth = parseInt(domStyle.get(document.body, "width"));
          // 80% of doc
          return Math.floor( .8 * bodyWidth ) + "px";
        }
      }
      // default
      return "500px";
    },

    //..........................................................
    _createControlWidget: function (formData, index)
    {
      if(!formData.control || formData.control.search(this._rgControlTypes) == -1)
      {
        // If no 'control' or unknown type then default <input>
        this._inputRow(formData, index);
        return;
      }
      this["_" + formData.control.toLowerCase() + "Row"](formData, index);
    },

    //..........................................................
    // <textarea>
    _textareaRow: function(formData, index)
    {
      var outerDiv = document.createElement("div");
      var labelSpan = document.createElement("span");
      var labelDiv = document.createElement("label");
      labelDiv.innerHTML = (formData.display && formData.display !== "") ? formData.display
          : formData.name;
      domClass.add(labelDiv, "c3_formDialog_item_stretch_actions_in");
      var inputSpan = document.createElement("span");
      var inputDiv = document.createElement("textarea");
      inputDiv.setAttribute("data-control-type", formData.control.toLowerCase());
      inputDiv.setAttribute("data-dojo-type", "dijit/form/Textarea");
      inputDiv.setAttribute("id", "param__input__" + index);
      var placeHolderTxt = (formData.description && formData.description !== "") ? formData.description
          : formData.name;
      inputDiv.setAttribute("placeHolder", placeHolderTxt);
      inputDiv.setAttribute("title", placeHolderTxt);
      // domClass.add(inputDiv, "dialogDesc");
      domClass.add(inputDiv, "c3_form_dialog_desc");
      domClass.add(inputDiv, "c3_formDialog_item_stretch_actions_in");
      if (null != formData.value && formData.value != undefined
        && (typeof (formData.value) == "string" || typeof (formData.value) == "number"))
      {
        inputDiv.value = formData.value;
      }
      // disable enter key event:navigate in textarea
      // this._handle(inputDiv, "keyup", lang.hitch(this, "_onKeyup"));
      dojo.place(inputDiv, inputSpan);
      dojo.place(labelDiv, labelSpan);
      dojo.place(labelSpan, outerDiv);
      dojo.place(inputSpan, outerDiv);
      dojo.place(outerDiv, this.dialogContentPane);
    },

    //..........................................................
    // <input>
    _textRow: function(formData, index)
    {
      // text is same as input control.
      this._inputRow(formData, index);
    },

    //..........................................................
    // <input>
    _inputRow: function(formData, index)
    {
      this._createInputRow(formData, index);
    },

    /**
     * Create input items dynamically. 
     */
   _createInputRow: function(formData, index) 
   {
      var outerDiv = document.createElement("div");
      var labelSpan = document.createElement("span");
      var labelDiv = document.createElement("label");
      labelDiv.innerHTML = (formData.display && formData.display !== "") ? formData.display
          : formData.name;
      domClass.add(labelDiv, "c3_formDialog_item_stretch_actions_in");
      var inputSpan = document.createElement("span");
      var inputDiv = document.createElement("input");
      inputDiv.setAttribute("data-control-type", (formData.control) ? formData.control.toLowerCase() : this._controlTypesObj.INPUT );
      inputDiv.setAttribute("data-dojo-type", "dijit/form/TextBox");
      inputDiv.setAttribute("id", "param__input__" + index);
      var placeHolderTxt = (formData.description && formData.description !== "") ? formData.description
          : formData.name;
      inputDiv.setAttribute("placeHolder", placeHolderTxt);
      inputDiv.setAttribute("title", placeHolderTxt);
      // domClass.add(inputDiv, "dialogDesc");
      domClass.add(inputDiv, "c3_form_dialog_desc");
      domClass.add(inputDiv, "c3_formDialog_item_stretch_actions_in");
      if (null != formData.value && formData.value != undefined
        && (typeof (formData.value) == "string" || typeof (formData.value) == "number"))
      {
        inputDiv.setAttribute("value", formData.value);
      }
      this._handle(inputDiv, "keyup", lang.hitch(this, "_onKeyup"));
      dojo.place(inputDiv, inputSpan);
      dojo.place(labelDiv, labelSpan);
      dojo.place(labelSpan, outerDiv);
      dojo.place(inputSpan, outerDiv);
      dojo.place(outerDiv, this.dialogContentPane);
   },

   //.........................................................
   _getElementValueById: function (id)
   {
     var inputNode = document.getElementById(id);
     if(inputNode)
     {
       // check control type using dataset attribute
       var controlType;
       if(inputNode.dataset && inputNode.dataset.controlType)
       {
         controlType = inputNode.dataset.controlType;
       }
       else if(inputNode.getAttribute("data-control-type"))
       {
         controlType = inputNode.getAttribute("data-control-type");
       }
       if(!controlType)
       {
         // default
         return inputNode.value;
       }
       if(controlType === this._controlTypesObj.INPUT || controlType === this._controlTypesObj.TEXT)
       {
         return inputNode.value;
       }
       //
       // add more types here ..
       //
       if(controlType === this._controlTypesObj.TEXTAREA)
       {
         return inputNode.value;
       }
       return inputNode.value;       
     }
     return;
   },

   /**
    * On cancel, hide the dialog and destory the widget.
    */
    onCancel: function()
    {
      //this.formDialog.hide();
      this.getCallback().call(this.getParent(), {
        "formParam": this.formParam
      });
      this._destroyWidgets();
    },  
   
   /**
    * On save, hide the dialog, destro the widget and send the entered 
    * values back to the parent page. 
    */
    onOk: function()
    {
//      this.formDialog.hide();
      for (var i = 0; i < this.formParam.length; i++)
      {
        this.formParam[i].value = this._getElementValueById("param__input__" + i);
      }
      this.getCallback().call(this.getParent(), {
        "formParam": this.formParam
      });
      this._destroyWidgets();
    },

   _onKeyup: function (event) {
     if(event && event.keyCode == 13 ) {
       this.onOk();
     }
   },
   
   setParent: function(parent) {
	   this._parentDijit = parent;
   },
   
   getParent: function() {
	   return this._parentDijit;   
   },
   
   setCallback: function(cb) {
	   this._cb = cb;
   },
   
   getCallback: function() {
	 return this._cb;  
   },

    destroy: function()
    {
      this._destroyWidgets();
      this.inherited(arguments);
    },
    
    _destroyWidgets: function()
    {
      // Delete all created Dijit widgets.
    	this.dialogContentPane.innerHTML = "";
    	this.dialogBody.innerHTML = "";
    	for (var i = 0; i < this._elements.length; i++)
        {
          if( typeof( this._elements[i].destroyRecursive ) === 'function' )
          {
              this._elements[i].destroyRecursive();
              
          }
        }
   	    this._elements = [];
    },
    _end : 0
  });
  

});
