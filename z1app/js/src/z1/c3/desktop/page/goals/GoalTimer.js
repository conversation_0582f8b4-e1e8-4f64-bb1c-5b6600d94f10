/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define([
  "dojo/_base/declare", 
  "dojo/_base/lang",
  "dojo/on",
  "udc/udc",
  "udc/core/commons/loader",
  "z1/c3/desktop/start",
  "dijit/registry",
  "udc/core/rt/widgets/_ComplexBaseWidget",  
  "dojo/text!z1/c3/desktop/page/templates/goals/GoalTimer.html",
  "dojo/i18n!z1/c3/desktop/page/nls/GoalTimerRes",
  "z1/c3/desktop/page/C3AlertBox",
  "dojo/dom-style",
  "dojo/dom-class",
  "dojo/dom-construct",
  "dijit/Dialog",
  "dijit/form/TextBox",
  "dijit/form/Button",
  "dijit/form/DateTextBox",
  "dijit/form/TimeTextBox",
  "dijit/form/RadioButton",
  "z1/common/C3Util",
],
function(declare, lang, on, udc, loader, z1, registry, BaseWidget,  templateStr, Res, AlertBox,
         style, domClass, domConstruct, Dialog, Textbox, Button, C3Util)
{

  /**
   * Creates a widget to show C3 landing page.
   */
  return declare(BaseWidget,
  {
    templateString : templateStr,
    Res: Res,
    _cb: null,
    _parentDijit: null,
    c3Dialog: null,
    
    _dateType: "",
    _timeType: "",
    hasTimer: "",
    _elements:null,
    
    DateVal : {
      allDay : "allDay",
      someDt : "someDt",
      dtRange : "dtRange"
    },
    
    TimeVal : {
      allTime : "allTime",
      someTime : "someTime",
      timeRange : "timeRange"
    },

  
    /**
     * Creates an instance of this page.
     * 
     * @param {Object}
     * @param {Object}
     *          parentNode
     */
    constructor : function(params, parentNode)
    {
      this.inherited(arguments);
      this._parent = parentNode;
      this._elements= [];
      this.hasTimer = "";
    },

    // ////////////////////////////////////////////////////////////////////////////
    // Implementations

    postCreate : function()
    {
      this.inherited(arguments);    

    },

    // .....................................................
    startup : function()
    {
      this.inherited(arguments);
      //open the dialog containing timer
      this.c3Dialog = new Dialog({
          title: Res.goalTimerTitle,
          style: "width: 500px;height:500px;"
      });
      dojo.place(this.dialogBody, this.c3Dialog.containerNode);
      
      this.c3Dialog.show();
      
      //attach click handlers for radio buttons
      this._handle(this.okBtn, "click", lang.hitch(this, "onOk"));
      this._handle(this.allDate, "click", lang.hitch(this, "_showDateSelection",this.DateVal.allDay));
      // this._handle(this.someDate, "click", lang.hitch(this, "_showDateSelection",this.DateVal.someDt));
      this._handle(this.dateRange, "click", lang.hitch(this, "_showDateSelection",this.DateVal.dtRange));
      
      this._handle(this.allTime, "click", lang.hitch(this, "_showTimeSelection", this.TimeVal.allTime));
      // this._handle(this.someTime, "click", lang.hitch(this, "_showTimeSelection", this.TimeVal.someTime));
      this._handle(this.timeRange, "click", lang.hitch(this, "_showTimeSelection", this.TimeVal.timeRange));
      
      //set the timer message
      this._setMessage();
      
      //show the default / previously saved selection
      this._showDateSelection(this.DateVal.allDay);
      this._showTimeSelection(this.TimeVal.allTime);
      
      //destroy widgets
      this._addWidgets();
    },
   
    /**
     * Set the message informing user what the timer does.
     */
    _setMessage: function() {
      this.timer_msg.innerHTML  = "'" + this.goalItem.name + "'" + Res.timerMsg;
    },
    
    /**
     * Click handler for date radio buttons
     * Sets the type of date selection.
     * Show the input controls according to the selection
     */
   _showDateSelection: function(dtVal){
     switch(dtVal)
     {
       case this.DateVal.allDay:
         this._setDateType(this.DateVal.allDay);
         this._toggleDateView();
         break;
       case this.DateVal.someDt:
         this._setDateType(this.DateVal.someDt);
         this._toggleDateView();
         break;
       case this.DateVal.dtRange:
         this._setDateType(this.DateVal.dtRange);
         this._toggleDateView();
         break;
     }
   },
   
   /**
    * Click handler for time radio buttons
    * Sets the type of time selection.
    * Show the input controls according to the selection
    */
   _showTimeSelection: function(timeVal){
     switch(timeVal)
     {
       case this.TimeVal.allTime:
         this._setTimeType(this.TimeVal.allTime);
         this._toggleTimeView();
         break;
       case this.TimeVal.someTime:
         this._setTimeType(this.TimeVal.someTime);
         this._toggleTimeView();
         break;
       case this.TimeVal.timeRange:
         this._setTimeType(this.TimeVal.timeRange);
         this._toggleTimeView();
         break;
     }
   },
   
 
   /**
    * Hide/ show the date input controls based on what date type got selected.
    */
   _toggleDateView: function() {
     switch(this._getDateType())
     {
       case this.DateVal.allDay:
         domClass.add(this.dateDiv,"c3_hide");
         break;
       case this.DateVal.someDt:
         domClass.add(this.endDate.domNode,"c3_hide");
         domClass.add(this.endDateLbl,"c3_hide");
         domClass.remove(this.stDate.domNode,"c3_hide");
         domClass.remove(this.stDateLbl,"c3_hide");
         domClass.remove(this.dateDiv,"c3_hide");
         break;
       case this.DateVal.dtRange:
         domClass.remove(this.endDate.domNode,"c3_hide");
         domClass.remove(this.endDateLbl,"c3_hide");
         domClass.remove(this.stDate.domNode,"c3_hide");
         domClass.remove(this.stDateLbl,"c3_hide");
         domClass.remove(this.dateDiv,"c3_hide");
         break;
     }
   },
   
   /**
    * Hide/ show the time input controls based on what time type got selected.
    */
   _toggleTimeView: function() {
     switch(this._getTimeType())
     {
       case this.TimeVal.allTime:
         domClass.add(this.timeDiv,"c3_hide");
         break;
       case this.TimeVal.someTime:
         domClass.add(this.endTime.domNode,"c3_hide");
         domClass.add(this.endTimeLbl,"c3_hide");
         domClass.remove(this.stTime.domNode,"c3_hide");
         domClass.remove(this.stTimeLbl,"c3_hide");
         domClass.remove(this.timeDiv,"c3_hide");
         break;
       case this.TimeVal.timeRange:
         domClass.remove(this.endTime.domNode,"c3_hide");
         domClass.remove(this.endTimeLbl,"c3_hide");
         domClass.remove(this.stTime.domNode,"c3_hide");
         domClass.remove(this.stTimeLbl,"c3_hide");
         domClass.remove(this.timeDiv,"c3_hide");
         break;
     }
   },
   
   /**
    * Show previously saved date / time values if data exists
    */
   setScreenValues: function(data) {
     if(data){
       //if no goal timer data exists , set the timer UI to default selection of date/time
       if(JSON.stringify(data) == "{}")
       {
         this._showDateSelection(this.DateVal.allDay);
         return;
       }
       else{
         //if date value exists then show the date inputs populated with saved data
         if(data.dayRange){
           if(data.dayRange.start && data.dayRange.end) {
             var parts1 = data.dayRange.start.split("-");
             //parse the backend date and create a Date obj from it 
             var d1 = new Date(parseInt(parts1[0]),
                              parseInt(parts1[1]) ,
                              parseInt(parts1[2]));
             this.stDate.setValue(d1);
             
             
             var parts2 = data.dayRange.end.split("-");
             d2 = new Date(parseInt(parts2[0]),
                              parseInt(parts2[1]) ,
                              parseInt(parts2[2]));
             this.endDate.setValue(d2);
             this.dateRange.setChecked(true);
             this.dateRange.checked =true;
             this._showDateSelection(this.DateVal.dtRange);
           }
           //if previously date range was selected then show the saved values accordingly
           else if(data.dayRange.start && data.dayRange.start !== ""){
             var parts = data.dayRange.start.split("-");
             var d = new Date(parseInt(parts[0]),
                              parseInt(parts[1]) ,
                              parseInt(parts[2]));
             this.stDate.setValue(d);
             this.someDate.setChecked(true);
             this._showDateSelection(this.DateVal.someDt);
           }
         }
         //if time value exists then show the time inputs populated with saved data
         if(data.timeRange){
           //for time range show the start and end time
           if(data.timeRange.start && data.timeRange.end) {
             var parts1 = data.timeRange.start.split("-");
            
             // dojo time input takes the format "THH:MM:SS" so put it in that format.
             var timeStr1 = "T" + parts1[0] + ":" + parts1[1] + ":" + "00";
             this.stTime.setValue(timeStr1);
             parts2 = data.timeRange.end.split("-");
             var timeStr2 = "T" + parts2[0] + ":" + parts2[1] + ":" + "00";
             this.endTime.setValue(timeStr2);
             this.timeRange.setChecked(true);
             this._showTimeSelection(this.TimeVal.timeRange);
           }
           else if(data.timeRange.start && data.timeRange.start !== ""){
             var parts = data.timeRange.start.split("-");
             var timeStr = "T" + parts[0] + ":" + parts[1] + ":" + "00";
             this.stTime.setValue(timeStr);
             this.someTime.setChecked(true);
             this._showTimeSelection(this.TimeVal.someTime);
           }
         }
       }
     }
   },
   
   /**
    * Click handler for saving the selected date and time
    */
   onOk: function() {
     //validate the date and time before saving it
     var isDateValid = this._validateDate();
     var isTimeValid = this._validateTime();
     if(isDateValid && isTimeValid) {
       //create the payload and save the goal timer data
       var timerPayload = this._createPayload();
       this._savePayload(timerPayload);
       this.getCallback().call(this.getParent(), {id:this.goalItem.id, hasTimer:this.hasTimer});
     }
     
   },
   
   /**
    * Date should be in the correct format and a future date.
    */
   _validateDate: function() {
     var sDate = this.stDate.value;
     var eDate = this.endDate.value;
     var todayDate = new Date();
     
     var strTodayDt = (todayDate.getMonth() + 1) + "/" + todayDate.getDate() + "/" + todayDate.getFullYear();
     var flag = true;
     switch(this._getDateType())
     {
       case this.DateVal.allDay:
         flag = true;
         break;
       case this.DateVal.someDt:
         if(sDate == "Invalid Date") {
           var alertParams1 = {
             "dialog_title":Res.invalidDate, 
             "dialog_msg": Res.invalidDateMsg
            };
            var alertDialog1 = new AlertBox(alertParams1);
            alertDialog1.startup();
            alertDialog1.setParent(this)
            //self._elements.push(alertDialog1);
            flag = false;
            return;
         }
         if(this.stDate.displayedValue != strTodayDt &&  sDate < todayDate) {
           var alertParams2 = {
             "dialog_title":Res.invalidDate, 
             "dialog_msg": Res.oldDateMsg
           };
            var alertDialog2 = new AlertBox(alertParams2);
            alertDialog2.startup();
            alertDialog2.setParent(this);
            flag = false;
            return;
         }
         
         break;
       case this.DateVal.dtRange:
         if(sDate ==  "Invalid Date" || eDate == "Invalid Date") {
           var alertParams3 = {
             "dialog_title":Res.invalidDate, 
             "dialog_msg": Res.invalidDateMsg1
           };
            var alertDialog3 = new AlertBox(alertParams3);
            alertDialog3.startup();
            alertDialog3.setParent(this);
           flag = false;
           return;
         }
         if((this.stDate.displayedValue != strTodayDt && sDate < todayDate) || 
             (this.endDate.displayedValue != strTodayDt && eDate < todayDate)) {
           var alertParams4 = {
             "dialog_title":Res.invalidDate, 
             "dialog_msg": Res.oldDateMsg
           };
            var alertDialog4 = new AlertBox(alertParams4);
            alertDialog4.startup();
            alertDialog4.setParent(this);
           flag = false;
           return;
         }
         if(sDate > eDate) {
           var alertParams5 = {
             "dialog_title":Res.invalidDate, 
             "dialog_msg": Res.compareDateMsg
           };
            var alertDialog5 = new AlertBox(alertParams5);
            alertDialog5.startup();
            alertDialog5.setParent(this);
            flag = false;
            return;
         }
         
         break;
     }
     return flag;
     
   },
   
   /**
    * Validate the entered start and end time.
    * Users should have entered time, it cannot be empty
    */
   _validateTime: function() {
     var sTime = this.stTime.displayedValue;
     var eTime = this.endTime.displayedValue;
     var flag = true;
     switch(this._getTimeType())
     {
       case this.TimeVal.allTime:
         flag = true;
         break;
       case this.TimeVal.someTime:
         if(sTime.toLowerCase() === "") {
           var alertParams5 = {
             "dialog_title":Res.invalidTime, 
             "dialog_msg": Res.invalidTimeMsg
           };
            var alertDialog5 = new AlertBox(alertParams5);
            alertDialog5.startup();
            alertDialog5.setParent(this);
            flag = false;
         }
        break;
       case this.TimeVal.timeRange:
         if(sTime.toLowerCase() === "" || eTime.toLowerCase() === "") {
           var alertParams6 = {
             "dialog_title":Res.invalidTime, 
             "dialog_msg": Res.invalidTimeMsg1
           };
            var alertDialog6 = new AlertBox(alertParams6);
            alertDialog6.startup();
            alertDialog6.setParent(this);
           flag = false;
         }
        break;
     }
     return flag;
   },
   
   
   /**
    * Create the payload to be sent to the backend for timer info.
    * 
    */
   _createPayload: function() {
     var timerPayload = {};
     var dayRangeObj = {};
     var timeRangeObj = {};
     //set the date payload if selected
     switch(this._getDateType())
     {
       //if default option is selected send an empty payload
       case this.DateVal.allDay:
         break;
       
       case this.DateVal.someDt:
         var dt = new Date(this.stDate.value);
         var dtDisplay = dt.getFullYear() + "-" + (dt.getMonth() + 1) + "-" + dt.getDate() ;
         dayRangeObj = {"start": dtDisplay};
         timerPayload.dayRange = dayRangeObj;
         break;
       //if date range is selected then send both dates in the format YYYY-MM-DD
       case this.DateVal.dtRange:
         var dt1 = new Date(this.stDate.value);
         var dtDisplay1 = dt1.getFullYear() + "-" + (dt1.getMonth() + 1) + "-" + dt1.getDate() ;
         var dt2 = new Date(this.endDate.value);
         var dtDisplay2 = dt2.getFullYear() + "-" + (dt2.getMonth() + 1) + "-" + dt2.getDate() ;
         
         dayRangeObj = {"start":dtDisplay1, "end": dtDisplay2};
         timerPayload.dayRange = dayRangeObj;
         break;
     }
     //set the time payload if selected
     switch(this._getTimeType())
     {
       //send empty time if no time is chosen
       case this.TimeVal.allTime:
         break;
       case this.TimeVal.someTime:
         var tm = new Date(this.stTime.value);
         var hours = tm.getHours().toString();
         var min = tm.getMinutes().toString();
         hours = (hours.length === 1) ? "0" + hours : hours;
         min = (min === "0") ? "00": min;
         min = (min === "3") ? "30": min;
         var tmDisplay = hours + "-" + min;
         
         timeRangeObj = {"start":tmDisplay};
         timerPayload.timeRange = timeRangeObj;
        break;
       //if time range has been selected 
       case this.TimeVal.timeRange:
         //time to be sent in HH-MM format
         var tm1 = new Date(this.stTime.value);
         var hours1 = tm1.getHours().toString();
         var min1 = tm1.getMinutes().toString();
         hours1 = (hours1.length === 1) ? "0" + hours1 : hours1;
         min1 = (min1 === "0") ? "00": min1;
         min1 = (min1 === "3") ? "30": min1;
         var tmDisplay1 = hours1 + "-" + min1;
         
         var tm2 = new Date(this.endTime.value);
         var hours2 = tm2.getHours().toString();
         var min2 = tm2.getMinutes().toString();
         hours2 = (hours2.length === 1) ? "0" + hours2 : hours2;
         min2 = (min2 === "0") ? "00": min2;
         min2 = (min2 === "3") ? "30": min2;
         var tmDisplay2 = hours2 + "-" + min2;
         timeRangeObj = {"start":tmDisplay1, "end": tmDisplay2};
         timerPayload.timeRange = timeRangeObj;
        break;
     }
     //set the thasTimer flag which will set the goal's timer flag.
     //if no payload / default selection then no timer is set
     //if some payload being sent  then set the timer flag to true
     if(JSON.stringify(timerPayload) === "{}") {
       this.hasTimer = false;
     }
     else {
       this.hasTimer = true;
     }
     return timerPayload;
   },
   
   /**
    * Save the payload to backend
    */
   _savePayload: function(data) {       
       var targetUrl = "c3/data/goals/updateTimeInterval?gid=" + this.goalItem.id;
       loader.POST(targetUrl, {payload: data}).then(lang.hitch(this, function(data)
         {
           
           this.c3Dialog.hide();
         }), lang.hitch(this, function(error)
         {
          C3Util.handleServerError(error);
         }));
   },
   
   _addWidgets: function() {
     this._elements.push(this.c3Dialog);
     this._elements.push(this.stDate);
     this._elements.push(this.endDate);
     this._elements.push(this.stTime);
     this._elements.push(this.endTime);
   },
   
   
   setParent: function(parent) {
	   this._parentDijit = parent;
   },
   
   getParent: function() {
	   return this._parentDijit;   
   },
   
   setCallback: function(cb) {
	   this._cb = cb;
   },
   
   getCallback: function() {
     return this._cb;  
   },
   
   _setDateType: function(val) {
     this._dateType = val;
   },
   
   _getDateType: function() {
     return this._dateType;
   },
   
   _setTimeType: function(val) {
     this._timeType = val;
   },
   
   _getTimeType: function() {
     return this._timeType;
   },
    
   destroy: function() {
      this.inherited(arguments);
      //this._destroyWidgets();
    },
    
    _destroyWidgets: function()
    {
      // Delete all created Dijit widgets.
      for (var i = 0; i < this._elements.length; i++)
      {
        if( typeof( this._elements[i].destroyRecursive ) === 'function' )
        {
            this._elements[i].destroyRecursive();
            
        }
      }
    this._elements = [];
    },
    
    _end : 0
  });
  

});
