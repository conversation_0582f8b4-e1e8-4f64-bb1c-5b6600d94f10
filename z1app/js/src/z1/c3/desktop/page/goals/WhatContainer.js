/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define([
  "dojo/_base/declare", 
  "dojo/_base/lang",
  "udc/udc",
  "z1/c3/desktop/start",
  "udc/core/rt/widgets/_BaseWidget",  
  "dojo/text!z1/c3/desktop/page/templates/goals/WhatContainer.html",
  "dojo/i18n!z1/c3/desktop/page/nls/WhatContainerRes",
  "z1/c3/desktop/page/goals/Palette",
  "z1/c3/desktop/data/C3Context",
  "dojo/query",
  "dojo/topic",
  "dojo/dom-class",
  "dijit/Tooltip"
],
function(declare, lang, udc, z1, BaseWidget,  templateStr, Res, Palette,	C3Context, query, topic, domClass, Tooltip)
{

  /**
   * Creates a widget to show C3 landing page.
   */
  return declare(BaseWidget,
  {
    templateString : templateStr,
    
    Res: Res,
    
    _lastSelectedSignal: "",
    
    _paletteItems: null,
    
    /**
     * Creates an instance of this page.
     * 
     * @param {Object}
     * @param {Object}
     *          parentNode
     */
    constructor : function(params, parentNode)
    {
      this.inherited(arguments);
      this._parent = parentNode;
      this._goalParent = params.goalParent;
      this._paletteItems = [];
    },

    postCreate : function()
    {
      this.inherited(arguments);      
    },

   /**
    * Fetch Actions Palette Data from backend.
    * Show all the signal groups in accordion and select the first signal group
    */
    startup : function()
    {
      this.inherited(arguments);

      this._infoTooltip();

      //C3Context.goalData.fetchActionsPaletteData({
      //  container: dojo.query('.c3_col_right-inner')[0],
      //  jsonUrl: "c3/data/actions/all", 
      //  showPalette: lang.hitch(this, this._showPalette)
      //});
      this._showPalette(C3Context.goalData.copyJObj(C3Context.goalData.getActionsPaletteData()));

      this._showSignalGroups();
      this._selectFirstSignalGroup();

      this._subscribe();
    },
    
    
    /**
     *Create and show RHS Palette.
     */ 
    _showPalette: function(paletteData)
    {
      udc.log("signals _showPalette, paletteData : ", paletteData );
      var palette = new Palette( { items: paletteData, title: Res.paletteTitle } );
      palette.addPaletteBoxes( paletteData );
      palette.placeAt( this.rightContent );
      //palette.startup();
      //this._paletteItems.push(palette);
      this.own(palette);
    },
    
  
    /**
     * Iterate through the goalData.which to show all signal groups in the accordion panel.
     */
    _showSignalGroups: function()
    {
      if( !C3Context.goalData.getData() || !C3Context.goalData.getData().which || !C3Context.goalData.getData().which.length)
      {
        return;
      }
      var actionsPane = dijit.byId("what");
      var wData = C3Context.goalData.getData().which;
      
      for( var i = 0; i < wData.length; i++ )
      {
        if( !wData[i].signalGroupId )
        {
          continue;
        }
        
        this._createSignalGroups(wData[i].signalGroupId + "__action",wData[i].name,actionsPane);
      }
      
      this._updateSignalGroupBtns();
    },
    
    /**
     * Create the Div's for Signal Groups
     */
    _createSignalGroups: function(id, name, pane)
    {
      var divWrp = this._createElement("div", pane.containerNode, {}, "c3_acrd_btn_wrp");
      //var btnsPanel = this._createElement("div", divWrp, {}, "c3_acrd_btns_pnl");
      
      var actionsDiv = this._createElement("div", divWrp, {}, "c3_goals_accordion_content_div c3_accordion_btn_container");
      actionsDiv.setAttribute("id", id);
      var spnName = this._createElement("span", actionsDiv, {
        innerHTML: name
      }, "c3_actions_sg_name");
      var icon = this._createElement("i", actionsDiv, {}, "c3_actions_sg_state_icon");
      var spacerDiv = this._createElement("div", actionsDiv, {}, "c3_acrd_spacer");
      var iconDiv = this._createElement("div", spacerDiv, {}, "arrow-left-blue c3_hide");
      this._handle(actionsDiv, "click", lang.hitch(this, "_onSelectSignalGroup", id));
    },
    
    /**
     * Set the css for the selected segment and remove 
     * the highlighted segment's css. 
     * Fire the event to show the canvas with the signal group related data.
     */
    _onSelectSignalGroup: function(signalGrpId)
    {
      var signalDiv = dojo.byId(signalGrpId);
      var signalName = (signalDiv.children && signalDiv.children[0]) ? signalDiv.children[0].innerHTML: "";
      this.what_header_title.innerHTML = (signalName !== "")? Res.whatTitle + "'" + signalName  + "'": Res.paletteTitle;
      domClass.remove( signalDiv, "c3_goals_accordion_content_div");
      domClass.add( signalDiv, "c3_goals_selected_accordion_content_div");
      //hide all the selected arrows(knots) from the div under customer group
      dojo.forEach( query(".arrow-left-blue"), function(elem) {
          // remove selection indicator
    	  domClass.add( elem, "c3_hide");
      });
      //only show the knot for the selected div
      var elmnts = query(".arrow-left-blue", signalDiv);
      if(elmnts && elmnts.length)
      {
        var iconDiv = elmnts[0];
        domClass.remove(iconDiv, "c3_hide");
      }
      if (this._lastSelectedSignal === "")
      {
        this._lastSelectedSignal = signalGrpId;
      }
      else if (this._lastSelectedSignal != signalGrpId)
      {
        var lastDiv = dojo.byId(this._lastSelectedSignal);
        if (lastDiv)
        {
          domClass.remove( lastDiv, "c3_goals_selected_accordion_content_div");
          domClass.add( lastDiv, "c3_goals_accordion_content_div");
        }
        this._lastSelectedSignal = signalGrpId;
      }
      var sigGrpId = signalGrpId.substring(0,signalGrpId.indexOf("__action"));
      var param = {
        "signalGroupId": sigGrpId
      };
      z1.fireEvent(z1.Events.showAction, param);
    },
    
    /**
     * Call to select the first default signal group that is created ("Untitled Signal Group")
     */
    _selectFirstSignalGroup: function()
    {
      var goalData = C3Context.goalData.getData();
      if (goalData && goalData.which)
      {
        this._onSelectSignalGroup(goalData.which[0].signalGroupId + "__action");
      }
    },

    /**
     * Item is dropped on Actions panel.
     */
    _onActionsChange: function ( ctx )
    {
      if( !ctx || !ctx.signalGroup )
      {
        return;
      }
      
      // this._updateSignalGroupBtnProp( ctx );
      this._updateSignalGroupBtns();
    },

    /**
     * Update display icon state {edit, finished}.
     */
    _updateSignalGroupBtnProp: function ( ctx )
    {
      if( !ctx || !ctx.signalGroup )
      {
        return;
      }
      
      var btn = dojo.byId( ctx.signalGroup.signalGroupId + "__action" );
      if( btn )
      {
        var el = btn.querySelector(".c3_actions_sg_state_icon");
        if( el )
        {
          if( ctx.signalGroup.actions.length )
          {
            domClass.remove( el, "fa-edit" );
            domClass.add( el, "fa-check-square-o" );
          }
          else
          {
            // no actions assigned.
            domClass.remove( el, "fa-check-square-o" );
            domClass.add( el, "fa-edit" );
          }
        }
      }
    },

    /**
     * Check all signal groups and update their button states.
     */
    _updateSignalGroupBtns: function ()
    {
      if( !C3Context.goalData.getData() || !C3Context.goalData.getData().which || !C3Context.goalData.getData().which.length)
      {
        return;
      }

      var isAllComplete = true;
      var wData = C3Context.goalData.getData().which;
      
      for( var i = 0; i < wData.length; i++ )
      {
        if( !wData[i].signalGroupId )
        {
          continue;
        }

        if( !wData[i].actions || !wData[i].actions.length )
        {
          isAllComplete = false;
        }
        if( C3Context.goalData.isSignalGroupEmpty({ signalGroup: wData[i] }) )
        {
          // udc.log("signal is empty");
          isAllComplete = false;
        }
        this._updateSignalGroupBtnProp( { signalGroup: wData[i] } );
      }

      var rIcon;
      var whatTabBtn = dijit.byId("what_button");
      if( whatTabBtn )
      {
        rIcon = whatTabBtn.domNode.querySelector(".c3_actions_sg_btn_icon_right");
      }
      
      //hide the publish button if there are no actions on the canvas. 
      if( isAllComplete) 
      {
    	  domClass.remove(this.publishBtn, "c3_hide");
      }
      else 
      {
    	  domClass.add(this.publishBtn, "c3_hide");
      }
      if( isAllComplete && wData.length )
      {
        // Mark - "What Actions" complete.
        udc.log("actions - complete");
        
        if( rIcon )
        {
          domClass.add( rIcon, "fa-check" );
        }
      }
      else
      {
        if( rIcon )
        {
          domClass.remove( rIcon, "fa-check" );
        }
      }
    },

    /**
     * Done. Go back to Goals page.
     * Fire Goal menu click event.
     */
    _onDone: function()
    {
     /* var btn = dojo.byId("top_menuGoal");
      if (dojo.isIE)
      {
        btn.fireEvent("onclick");
      }
      else
      { 
        // Not IE
        var event = document.createEvent("HTMLEvents");
        event.initEvent("click", false, true);
        btn.dispatchEvent(event);
      }*/
      topic.publish("csb/goals", {});
    },
    
    _onPublish: function() {
    	C3Context.goalData.publishGoal(true);
    	this._onDone();
    },
    
    _showTooltip: function(){
    	var myTip = new Tooltip({
    	    connectId: "gettingStarted",
    	    "class": "c3_tip"
    	});
    	dojo.place(this.tooltipContent, myTip.domNode);
    	myTip.defaultPosition =['above', 'below'];
    	domClass.remove(this.tooltipContent, "dijitHidden");
    },

    /**
     * Attach info tool tip.
     */
    _infoTooltip: function()
    {
      var tipHtml = [];
      tipHtml.push("<div class='c3_goal_tooltip_title fa-info-circle'><span>" + Res.gettingStarted + "</span></div>");
      tipHtml.push("<div class='c3_goal_tooltip_row'><span class='c3_goal_tooltip_topic'>" + Res.newToolTipLbl + "</span><span class='c3_goal_tooltip_descr'>" + Res.newToolTip + "</span></div>");
      tipHtml.push("<div class='c3_goal_tooltip_row'><span class='c3_goal_tooltip_topic'>" + Res.saveToolTipLbl + "</span><span class='c3_goal_tooltip_descr'>" + Res.saveToolTip + "</span></div>");
      tipHtml.push("<div class='c3_goal_tooltip_row'><span class='c3_goal_tooltip_topic'>" + Res.deleteToolTipLbl + "</span><span class='c3_goal_tooltip_descr'>" + Res.deleteToolTip + "</span></div>");
      
      new Tooltip({
        connectId: ["infoWhoTooltip"],
        position: ['below'],
        label: tipHtml.join(' ')
      });
    },

    //.....................................................
    // Subscribe to events.
    _subscribe: function ()
    {
      // OnDnDrop, onDnDClear.
      this._handleSub = topic.subscribe( "actions/ondrop", lang.hitch( this, function( ctx ){
        this._onActionsChange( ctx );
      }) );
      // onDnD-delete
      // Actions palette item is deleted. Update goal status.
      this._handleDelSub = topic.subscribe( "/signalgroup/actions/updated", lang.hitch( this, function( ctx ){
        this._onActionsChange( ctx );
      }) );
    },

    destroy: function()
    {
      this._destroyWidgets();
      this.content.innerHTML = "";
      //this.what_tooltip.innerHTML = "";
      this.inherited(arguments);
      if(this._handleSub)
      {
        this._handleSub.remove();
      }
      if(this._handleDelSub)
      {
        this._handleDelSub.remove();
      }
    },

    _destroyWidgets: function()
    {
      // Delete all created Dijit widgets.
	  for (var i = 0; i < this._paletteItems.length; i++)
      {
        if( typeof( this._paletteItems[i].destroyRecursive ) === 'function' )
        {
            this._paletteItems[i].destroyRecursive();
            this._paletteItems[i] = null;
        }
      }
    },
    
    _end : 0
  });
  

});
