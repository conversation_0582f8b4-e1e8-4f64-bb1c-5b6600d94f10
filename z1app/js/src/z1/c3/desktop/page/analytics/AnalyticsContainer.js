/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define(["dojo/_base/declare", "dojo/_base/lang", "dojo/on", "udc/udc", "z1/c3/desktop/start",
  "udc/core/rt/widgets/_ComplexBaseWidget",
  "dojo/text!z1/c3/desktop/page/templates/analytics/AnalyticsContainer.html",
  "dojo/i18n!z1/c3/desktop/page/nls/AnalyticsContainerRes", "dojo/dom-class", "dojo/topic","z1/c3/utils/Permissions",
  "dijit/layout/BorderContainer", "dojox/layout/ExpandoPane", "dijit/layout/ContentPane",
  "z1/c3/desktop/page/settings/account/AccountDisplay"
],
  function(declare, lang, on, udc, z1, BaseWidget, templateStr, Res,
    domClass, topic, UserACL, AccountDisplay)
  {

    /**
     * Creates a widget to show C3 landing page.
     */
    return declare(BaseWidget, {
      templateString: templateStr,
      Res: Res,

      Menu: {
        ARTICLES: Res.articles,
        PUSH_NOTIFICATIONS: Res.pushNotifications,
        FAQ: Res.faq,
        FSCREEN_MSGS: Res.fScreenMsgs,
        BANNERS: Res.banners,
        ALERTS: Res.alerts,
        APPBOX: Res.appbox,
        IMAGE_CONTENT: Res.imageContent,
        DEEP_LINKS: Res.deepLinks,
        CUSTOM_CSS: Res.customCss,
        CONTENT_SOURCES: Res.kb
      },

      _selectedMenu: "",

      /**
       * Creates an instance of this page.
       * 
       * @param {Ob-ject}
       * @param {Object}
       *          parentNode
       */
      constructor: function(params, parentNode)
      {
        this.inherited(arguments);
        this.access = params.access;
        this._openMsg = params.openMsg;
        this._parent = parentNode;
        
        this.helpId = "";
      },

      // ////////////////////////////////////////////////////////////////////////////
      // Implementations

      postCreate: function()
      {
        this.inherited(arguments);
      },

      // .....................................................
      startup: function()
      {
        this.inherited(arguments);
        this._setEvents();   
        // this page is currently inactive - commenting out old check
        // this._checkUserPermission();
        this._onMenuItemClicked(this.Menu.PUSH_NOTIFICATIONS, this._openMsg);
        this._showAccountDisplay();
      },
      
      _sendContextHelpTopic: function()
      {
        udc.sendContextHelpTopic({"alwaysShow": true, "messageType": this.helpId});
      },
      
      _checkUserPermission: function()
      {
        var hasPermission = UserACL.hasPermission("content", "v");
        if(!hasPermission){
          domClass.add(this.pushMenu, "c3_hide");
          domClass.add(this.fscreenMenu, "c3_hide");
          domClass.add(this.bannerMenu, "c3_hide");
          domClass.add(this.alertMenu, "c3_hide");
          domClass.add(this.appboxMenu, "c3_hide");
          domClass.add(this.articlesMenu, "c3_hide");
          domClass.add(this.faqMenu, "c3_hide");
        }
      },

      /**
       * Handles the click event when insights menu is clicked. 1) Sets the current setting which is
       * selected. 2) Sets the css to highlight selected tab 3) Fires the event to show the
       * respective page that was selected.
       */
      _onMenuItemClicked: function(id, viewExistingMsg)
      {
        switch (id)
        {
          case this.Menu.ARTICLES:
            this.setSelectedMenu(this.Menu.ARTICLES);
            if (this.content) this.content.innerHTML = "";
            domClass.add(this.articlesMenu, "c3_csb_menu_selected");
            this.helpId = "";
            z1.fireEvent(z1.Events.configureContent);
            udc.sendContextHelpTopic();
          break;
          case this.Menu.CONTENT_SOURCES:
              this.setSelectedMenu(this.Menu.CONTENT_SOURCES);
              if (this.content) this.content.innerHTML = "";
              domClass.add(this.contentSourcesMenu, "c3_csb_menu_selected");
              this.helpId = "";
              z1.fireEvent(z1.Events.showKnowledgeBase);
            break;
          case this.Menu.PUSH_NOTIFICATIONS:
            this.setSelectedMenu(this.Menu.PUSH_NOTIFICATIONS);
            if (this.content) this.content.innerHTML = "";
            domClass.add(this.pushMenu, "c3_csb_menu_selected");
            this.helpId = "push";
            z1.fireEvent(z1.Events.showMessages, {
              type: "pushMsg",
              openMsg: viewExistingMsg
            });
            udc.sendContextHelpTopic({messageType: "push"});
          break;
          case this.Menu.IMAGE_CONTENT:
              this.setSelectedMenu(this.Menu.IMAGE_CONTENT);
              if (this.content) this.content.innerHTML = "";
              domClass.add(this.imageMenu, "c3_csb_menu_selected");
              this.helpId = "";
              z1.fireEvent(z1.Events.showImageContent);
              udc.sendContextHelpTopic();
            break;
          case this.Menu.FSCREEN_MSGS:
            this.setSelectedMenu(this.Menu.FSCREEN_MSGS);
            if (this.content) this.content.innerHTML = "";
            domClass.add(this.fscreenMenu, "c3_csb_menu_selected");
            this.helpId = "fullscreen";
            z1.fireEvent(z1.Events.showMessages, {
              type: "fullScreenMsg"
            });
            udc.sendContextHelpTopic({messageType: "fullscreen"});
          break;
          case this.Menu.BANNERS:
            this.setSelectedMenu(this.Menu.BANNERS);
            if (this.content) this.content.innerHTML = "";
            domClass.add(this.bannerMenu, "c3_csb_menu_selected");
            this.helpId = "banner";
            z1.fireEvent(z1.Events.showMessages, {
              type: "banner"
            });
            udc.sendContextHelpTopic({messageType: "banner"});
          break;
          case this.Menu.ALERTS:
            this.setSelectedMenu(this.Menu.ALERTS);
            if (this.content) this.content.innerHTML = "";
            domClass.add(this.alertMenu, "c3_csb_menu_selected");
            this.helpId = "alert";
            z1.fireEvent(z1.Events.showMessages, {
              type: "alert"
            });
            udc.sendContextHelpTopic({messageType: "alert"});
          break;
          case this.Menu.APPBOX:
            this.setSelectedMenu(this.Menu.APPBOX);
            if (this.content) this.content.innerHTML = "";
            domClass.add(this.appboxMenu, "c3_csb_menu_selected");
            this.helpId = "appbox";
            z1.fireEvent(z1.Events.showMessages, {
              type: "appbox"
            });
            udc.sendContextHelpTopic({messageType: "appbox"});
          break;
          case this.Menu.FAQ:
            this.setSelectedMenu(this.Menu.FAQ);
            if (this.content) this.content.innerHTML = "";
            domClass.add(this.faqMenu, "c3_csb_menu_selected");
            this.helpId = "";
            z1.fireEvent(z1.Events.showFAQ);
            udc.sendContextHelpTopic();
          break;
          case this.Menu.CUSTOM_CSS:
              this.setSelectedMenu(this.Menu.CUSTOM_CSS);
              if (this.content) this.content.innerHTML = "";
              domClass.add(this.cssMenu, "c3_csb_menu_selected");
              this.helpId = "";
              z1.fireEvent(z1.Events.customCSS);
            break;
          case this.Menu.DEEP_LINKS:
              this.setSelectedMenu(this.Menu.DEEP_LINKS);
              if (this.content) this.content.innerHTML = "";
              domClass.add(this.deepLinkMenu, "c3_csb_menu_selected");
              this.helpId = "";
              z1.fireEvent(z1.Events.showDeepLinks);
            break;
        }
//        this._changeHeaderText(id);
        this._setMenuInactive();
      },

      /**
       * Create the "Create New Btn" and append it to headerDiv.
       */
      _changeHeaderText: function(headerTxt)
      {
        if (headerTxt) this.headerText.innerHTML = Res.anlyticsHeader
          + " &nbsp;&nbsp;> &nbsp;&nbsp;" + headerTxt;
      },

      _setMenuInactive: function()
      {
        var menuArray = [this.Menu.ARTICLES, this.Menu.PUSH_NOTIFICATIONS, this.Menu.DEEP_LINKS, this.Menu.CUSTOM_CSS,
          this.Menu.FSCREEN_MSGS, this.Menu.IMAGE_CONTENT, this.Menu.BANNERS, this.Menu.ALERTS, this.Menu.APPBOX, 
          this.Menu.FAQ,  this.Menu.CONTENT_SOURCES];
        for (var i = 0; i < menuArray.length; i++)
        {
          if (menuArray[i] !== this.getSelectedMenu())
          {
            switch (menuArray[i])
            {
              case this.Menu.ARTICLES:
                domClass.remove(this.articlesMenu, "c3_csb_menu_selected");
              break;
              case this.Menu.PUSH_NOTIFICATIONS:
                domClass.remove(this.pushMenu, "c3_csb_menu_selected");
              break;
              case this.Menu.IMAGE_CONTENT:
                  domClass.remove(this.imageMenu, "c3_csb_menu_selected");
                break;
              case this.Menu.FSCREEN_MSGS:
                domClass.remove(this.fscreenMenu, "c3_csb_menu_selected");
              break;
              case this.Menu.BANNERS:
                domClass.remove(this.bannerMenu, "c3_csb_menu_selected");
              break;
              case this.Menu.ALERTS:
                domClass.remove(this.alertMenu, "c3_csb_menu_selected");
              break;
              case this.Menu.APPBOX:
                domClass.remove(this.appboxMenu, "c3_csb_menu_selected");
              break;
              case this.Menu.FAQ:
                domClass.remove(this.faqMenu, "c3_csb_menu_selected");
              break;
              case this.Menu.DEEP_LINKS:
                  domClass.remove(this.deepLinkMenu, "c3_csb_menu_selected");
                break;
              case this.Menu.CUSTOM_CSS:
                  domClass.remove(this.cssMenu, "c3_csb_menu_selected");
                break;
              case this.Menu.CONTENT_SOURCES:
                  domClass.remove(this.contentSourcesMenu, "c3_csb_menu_selected");
                break;
            }
          }
        }
      },

      setSelectedMenu: function(data)
      {
        this._selectedMenu = data;
      },

      getSelectedMenu: function()
      {
        return this._selectedMenu;
      },

      //...........................................
      _showAccountDisplay: function ()
      {
        var aCtx = {
          _parent: this,
          access: this.access
        };
        this._accountDisplay = new AccountDisplay(aCtx);
        this._accountDisplay.startup();
        domConstruct.place(this._accountDisplay.domNode, this._topHContR, "last");
        this.own(this._accountDisplay);
      },

      //..................................
      _setEvents: function()
      {
        this.own(
          on(this.articlesMenu, "click", lang.hitch(this, "_onMenuItemClicked",
            this.Menu.ARTICLES)),
          on(this.pushMenu, "click", lang.hitch(this, "_onMenuItemClicked",
            this.Menu.PUSH_NOTIFICATIONS)),
          on(this.faqMenu, "click", lang.hitch(this, "_onMenuItemClicked", this.Menu.FAQ)),
          on(this.imageMenu, "click", lang.hitch(this, "_onMenuItemClicked",
                    this.Menu.IMAGE_CONTENT)),
          on(this.fscreenMenu, "click", lang.hitch(this, "_onMenuItemClicked",
            this.Menu.FSCREEN_MSGS)),
          on(this.bannerMenu, "click", lang.hitch(this, "_onMenuItemClicked",
            this.Menu.BANNERS)),
          on(this.alertMenu, "click", lang.hitch(this, "_onMenuItemClicked",
            this.Menu.ALERTS)),
          on(this.appboxMenu, "click", lang.hitch(this, "_onMenuItemClicked",
            this.Menu.APPBOX)),
          on(this.deepLinkMenu, "click", lang.hitch(this, "_onMenuItemClicked",
                    this.Menu.DEEP_LINKS)),
          on(this.cssMenu, "click", lang.hitch(this, "_onMenuItemClicked",
                            this.Menu.CUSTOM_CSS)),
          on(this.contentSourcesMenu, "click", lang.hitch(this, "_onMenuItemClicked",
                                    this.Menu.CONTENT_SOURCES)),
         
          on(this.helpRequestBtn, "click", lang.hitch(this, "_sendContextHelpTopic")),
                                    
        );
      },

      // .....................................................
      destroy: function()
      {
        this.inherited(arguments);
      },

      _end: 0
    });

  });
