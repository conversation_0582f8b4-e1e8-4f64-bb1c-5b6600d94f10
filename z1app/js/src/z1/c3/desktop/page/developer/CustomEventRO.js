/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define(
  ["dojo/_base/declare", "dojo/_base/lang", "dojo/on", "udc/udc", "udc/core/commons/loader",
    "z1/c3/desktop/start", "dijit/registry", "udc/core/rt/widgets/_BaseWidget",
    "dojo/text!z1/c3/desktop/page/templates/developer/CustomEventRO.html",
    "dojo/i18n!z1/c3/desktop/page/nls/CustomEventRORes", "dojo/dom-style", "dojo/dom-construct",
    "dijit/form/TextBox", "dijit/form/Button", "dijit/form/Select",
    "z1/c3/desktop/page/C3DeleteDialogBox", "z1/c3/desktop/page/monaco/C3MonacoEditor",
    "dojo/dom-class", "z1/c3/utils/validator", "z1/c3/desktop/page/modules/ModuleSaveAs",
    "z1/c3/utils/Permissions", "z1/c3/desktop/data/modules/ModulesData",
    "dojo/text!z1/c3/desktop/data/modules/RestStructure.json",
    "dojo/text!z1/c3/desktop/data/modules/CustomScriptStructure.json"],
  function(declare, lang, on, udc, loader, z1, registry, BaseWidget, templateStr, Res, style,
    domConstruct, Textbox, Button, Select, DeleteDialogBox, C3MonacoEditor, domClass, validator,
    ModuleSaveAs, UserACL, ModulesData, RestStructure, CustomScriptStructure)
  {

  /**
   * Creates a widget to show C3 landing page.
   */
  return declare(BaseWidget, {
    templateString: templateStr,
    Res: Res,

    _actions: null,
    _action: null,
    _headerList: null,

    /**
     * Creates an instance of this page.
     * 
     * @param {Object}
     * @param {Object}
     *          parentNode
     */
    constructor: function(params, parentNode)
    {
      this.inherited(arguments);
      this._parent = parentNode;
      // view mode - add/ edit
      this._op = params.oper;
      this._actions = (params._actions) ? params._actions : [];
      this._action = (params._action) ? params._action : {};
      this._cbObj = (params._cbObj) ? params._cbObj : {};
      this._headerList = {};
    },

    // ////////////////////////////////////////////////////////////////////////////
    // Implementations

    postCreate: function()
    {
      this.inherited(arguments);

    },

    // .....................................................
    startup: function()
    {
      this.inherited(arguments);

      this._checkCreatePermission();
      
      this._paintUI();

      this._cmEditor = new C3MonacoEditor({
        _parent: this,
        // value: ..
        languages: ["javascript"],
        // tool bar
        toolButtons: [{
          "name": "js"
        }],
        placeholder: Res.lblcodePayload,
        _showSnippets: false,
        readOnly: true,
        onEditorReady: lang.hitch(this, function() {
          // set Form values after the code mirror editor is initialized.

          // set the title dynamically
          this._setTitle();

          this._headerList = (this._op === "edit") ? this._action.header : {};

          if (this._op === "edit")
          {
            if (this._action.type) this._type = this._action.type;

            if (this._action && this._action.header)
            {
              if (typeof this._action.header === "object")
              {
                this._headerList = this._action.header;
              }
              else if (typeof this._action.header === "string" && this._action.header.length)
              {
                this._headerList = JSON.parse(this._action.header);
              }
              else
              {
                this._headerList = {};
              }
            }
            else
            {
              this._headerList = {};
            }
            this._setCurrentAction();
          }
          else
          {
            // On create new, load the type:code specific UI first (for code-mirror rendering reasons)
            // and then change to appropriate UI based on type:(?)

            // show/hide the input fields based on rest / code value
            this._toggleInputFields();
          }
          this.actionType.textContent = this._getTypeName();
        })
      });
      domConstruct.place(this._cmEditor.domNode, this._cmCont, "last");
      // start after placing dom
      this._cmEditor.startup();
      this.own(this._cmEditor);
      this._cmEditor.init().then(lang.hitch(this, function()
      {
      }), lang.hitch(this, function(err)
      {
        udc.log(err);
      }));

    },

    _checkCreatePermission: function()
    {
    },
    
    // .........................
    
    _paintUI: function()
    {
      var t = null;
      if (this._cbObj)
      {
        t = this._cbObj.type ? this._cbObj.type.toLowerCase() : "defaultItem";
      }

      // this.actionDescLbl.textContent = Res.lblDesc + (Res[t] || Res.defaultItem);
      this.actionDescLbl.textContent = Res.lblDesc;
    },

    // .........................
    _setTitle: function()
    {
    },

    /**
     * Set the values for each field when you open the profile popup for editing an existing profile
     */
    _setCurrentAction: function()
    {
      if (this._action)
      {
        if (this._action.description)
        {
          this.actionDescCont.classList.remove("c3_hide");
          this.actionDesc.textContent = this._action.description;
        }
        // this.actionType.textContent = this._getTypeName();
        this.actionUrlValue.textContent = this._action.url;
        this.actionUrlType.textContent = this._action.verb;
        this.payloadValue.value = this._action.payload;
        // this.codePayload.value = this._action.statusParser;
        this._cmEditor.setValue(this._action.payload);
        this._showParams();
        this._toggleInputFields();
      }
    },

    _addHeader: function()
    {
    },

    _validateParam: function()
    {
      var flag = true;
      return flag;
    },

    // .............................
    _getTypeName: function()
    {
      if (!this._type) return;
      if (!this._customPublicTypeList || !this._customPublicTypeList.length) return this._type;
      for (var i = 0; i < this._customPublicTypeList.length; i++)
      {
        if (this._type.toLowerCase() === this._customPublicTypeList[i].type.toLowerCase())
        {
          var v = Res[this._customPublicTypeList[i].type.toLowerCase()];
          return v ? v : this._customPublicTypeList[i].name;
        }
      }
    },

    _showParams: function()
    {
      this.httpHeaderContainer.innerHTML = "";

      domClass.remove(this.httpHeaderContainer, "c3_hide");
      for ( var key in this._headerList)
      {
        if (this._headerList.hasOwnProperty(key))
        {
          var listingDiv = this._createElement("div", this.httpHeaderContainer, {
            innerHTML: ""
          }, "c3_rows_nowrap_start c3_gpC10");

          var headerNameSec = this._createElement("div", listingDiv, {
            innerHTML: key + ":"
          }, "c3_customAction_httpHeaderName");

          var headerValueSec = this._createElement("div", listingDiv, {
            innerHTML: this._headerList[key]
          }, "c3_customAction_httpHeaderValue");
        }

      }
    },

    _onParamDeleted: function(headerKey)
    {
    },

    _toggleInputFields: function()
    {
      if (this._type && this._type.toLowerCase() === "rest")
      {
        domClass.remove(this.actionUrl, "c3_hide");
        domClass.remove(this.actionUrlMethod, "c3_hide");
        domClass.remove(this.payload, "c3_hide");
        this._headerSecction.classList.remove("c3_hide");
        domClass.remove(this.httpHeaderContainer, "c3_hide");
        domClass.remove(this.httpLabel, "c3_hide");
        domClass.add(this._cmCont, "c3_hide");
      }
      else if (this._type && this._type.toLowerCase() === "code")
      {
        domClass.add(this.actionUrl, "c3_hide");
        domClass.add(this.actionUrlMethod, "c3_hide");
        domClass.add(this.payload, "c3_hide");
        this._headerSecction.classList.add("c3_hide");
        domClass.add(this.httpHeaderContainer, "c3_hide");
        domClass.add(this.httpLabel, "c3_hide");
        domClass.remove(this._cmCont, "c3_hide");
        // this to fix a glitch in cm
        if (this._cmEditor && this._cmEditor.getValue()) this._cmEditor.refresh();
      }
    },

    /**
     * Create the profile obj to be saved
     */
    _createCustomAction: function()
    {
    },

    _validateContent: function()
    {
      return true;
    },

    _createPayload: function()
    {
    },

    _actionExist: function(actionName)
    {
      var flag = false;
      var len = this._actions.length;
      if (this._actions && len > 0)
      {
        for (var i = 0; i < len; i++)
        {
          if (actionName.toLowerCase() === this._actions[i].name.toLowerCase())
          {
            flag = true;
            break;
          }
        }
      }
      return flag;
    },

    /**
     * REST call to save the profile to backend
     */
    _save: function(action)
    {
    },

    /**
     * Populate the event dd in the format - Event Attr (Event Name)
     */
    _populateEventAttrField: function(data)
    {
      domClass.remove(this.evtAttrHolder, "c3_hide");
      this.attrEvt.innerHTML = "";
      var optionHTML = "";
      if (data)
      {
        for ( var key in data)
        {
          var eventObj = data[key];
          if (eventObj)
          {
            for ( var innerKey in eventObj)
            {
              optionHTML += "<option>" + innerKey + "&nbsp;&nbsp;(Event: " + key + ") </option>";
            }
          }
        }
      }
      this.attrEvt.innerHTML = optionHTML;
    },

    _onClose: function()
    {
    },

    setSelectedType: function(opn)
    {
      this._type = opn;
    },

    getSelectedType: function()
    {
      return this._type;
    },

    destroy: function()
    {
      this.inherited(arguments);
    },

    _end: 0
  });

});
