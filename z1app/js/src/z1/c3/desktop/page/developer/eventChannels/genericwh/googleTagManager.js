define(["dojo/_base/lang", "dojo/dom-construct", "udc/core/commons/loader", "udc/udc",
  "dojo/text!z1/c3/desktop/page/templates/developer/eventChannels/genericwh/googleTagManager.html"],
  function(lang, domConstruct, loader, udc, templateHtml)
  {
    var custom = {
 
      // ................................................
      // helpCtx contains {_parent, content, MAX_COUNT (optional), MAX_TIME (optional}
      // usage: FTHelp.show({_parent: this, content: Res.bubbleInfo})
      attach: function(parentNode)
      {
        if (parentNode) {
          parentNode.innerHTML = templateHtml;
        }
      },
       
      onSave: function(saveData)
      {
        // Example: saveData["tealiumSig"] = "asadsada";
      },
       
//      getProcessingCode: function()
//      {
//        return "var inDataStr = Channel.getData();\nvar mpJSON = JSON.parse(inDataStr);\n\nvar z1JSON = {};\nz1JSON.events = [];\n\n// Use this to map mParticle fields to Session AI profile parameters\nvar profileAttributeMappings = [\n  {\"sourceT1\":\"user_identities\", \"sourceT2\":\"customerid\", \"destination\":\"customerId\",\"defaultValue\":\"\"},\n  {\"sourceT1\":\"user_identities\", \"sourceT2\":\"email\", \"destination\":\"email\",\"defaultValue\":\"\"},\n  {\"sourceT1\":\"user_attributes\", \"sourceT2\":\"$state\", \"destination\":\"state\",\"defaultValue\":\"\"}\n\n];\n\n// Set the profile parameters as specified in the mapping\nfor (var i = 0; i < profileAttributeMappings.length; i++){\n  var mappedOBJ = profileAttributeMappings[i];\n  migrateProfileAttribute(mappedOBJ.sourceT1, mappedOBJ.sourceT2, mappedOBJ.destination, mappedOBJ.defaultValue);\n}\n\n\n// Get all of the mParticle events and set them as Session AI events\nfor (var i = 0; i < mpJSON.events.length; i++){\n  var eventOBJ = mpJSON.events[i];\n  var eventType = eventOBJ.event_type;\n  var eventParameters = {};\n  var eventName;\n  switch (eventType){\n    case \"session_start\":\n      // Do apps_start event\n      eventName = \"appstart\";\n      eventParameters.eventName = eventName;\n      break;\n    case \"application_state_transition\":\n      eventName = \"stateTransition\";\n      eventParameters.eventName = eventName;\n      break;\n    case \"crash_report\":\n      eventName = \"crashReport\";\n      eventParameters.eventName = eventName;\n      break;\n    case \"session_end\":\n      eventName = \"sessionEnd\";\n      eventParameters.eventName = eventName;\n      break;\n    case \"custom_event\":\n      // Send an event\n      eventName = eventOBJ.data.custom_event_type;\n      var customEventType = eventOBJ.data.custom_event_type;\n\n      var eventParameters = eventOBJ.data.custom_attributes;\n      eventParameters.eventType = customEventType;\n      eventParameters.eventName = eventName;\n      break;\n    default: // do nothing\n      return;\n  }\n  \n  Channel.addEvent(eventName, eventParameters);\n\n  z1JSON.events.push(eventParameters);\n\n}\n\n// Send the z1JSON event payload \n// TODO: not sure how\n\n// Debug: return the Z1 JSON back in the response text\n// Doesn't work - not sure why\nvar testOutput = JSON.stringify(z1JSON);\nChannel.setResponseText(testOutput);\n\n\nfunction migrateProfileAttribute(sourceT1, sourceT2, destination, defaultValue){\n  var usedefault = true;\n  if (sourceT1 in mpJSON){\n    if (sourceT2 in mpJSON[sourceT1]){\n      if (mpJSON[sourceT1][sourceT2] != null){\n        //z1JSON[destination] = mpJSON[sourceT1][sourceT2];\n        var val = mpJSON[sourceT1][sourceT2];\n        Channel.addContext(destination, val);\n        usedefault = false;\n      }\n    }\n  }\n  if (usedefault){\n    //z1JSON[destination] = defaultValue;\n    Channel.addContext(destination, defaultValue);\n  }\n}";
//      },
 
      _end: 0
    };
 
    return custom;
  });