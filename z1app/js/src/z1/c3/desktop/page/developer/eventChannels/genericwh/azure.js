define(["dojo/dom","dojo/_base/declare", "dojo/_base/lang", "dojo/on", "udc/core/commons/loader",
  "dojo/dom-class", "udc/udc", "z1/c3/desktop/start", 
  "z1/c3/desktop/page/developer/eventChannels/genericwh/azureForm"],
  function(dom, declare, lang, on, loader, domClass, udc, z1, s3Form )
  {
    
    var custom = {
      
      // ................................................
      // helpCtx contains {_parent, content, MAX_COUNT (optional), MAX_TIME (optional}
      // usage: FTHelp.show({_parent: this, content: Res.bubbleInfo})
      
      attach: function(parentNode,data)
      {        
        if (parentNode) {
          data = data || {};
          this.widget = new s3Form(data, parentNode);
          this.widget.startup();
        }
      },
       
      onSave: function(saveData)
      {
        var dataObj = this.widget.getData();
        saveData.dataObj = dataObj;        
      },
       
      getProcessingCode: function()
      {
        return "";
      },
      _end: 0
    };
 
    return custom;
  });