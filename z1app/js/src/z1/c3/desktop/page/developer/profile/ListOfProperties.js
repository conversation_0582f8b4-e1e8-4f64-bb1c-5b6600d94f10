/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define(
["dojo/_base/declare", "dojo/_base/lang", "dojo/on", "udc/udc", "udc/core/commons/loader",
  "z1/c3/desktop/start", "dijit/registry", "udc/core/rt/widgets/_BaseWidget",
  "dojo/text!z1/c3/desktop/page/templates/developer/profile/ListOfProperties.html",
  "dojo/i18n!z1/c3/desktop/page/nls/ListOfPropertiesRes",
  "dojo/dom-style", "dojo/dom-construct",
  "z1/c3/desktop/page/C3AlertBox",
  "z1/c3/desktop/page/C3DeleteDialogBox",
  "dojo/dom-class","z1/c3/utils/validator","z1/common/C3Util",
],
function(declare, lang, on, udc, loader, z1, registry, BaseWidget, templateStr, Res, style,
  domConstruct, AlertBox, DeleteDialogBox, domClass, validator, C3Util
)
{

  /**
   * Creates a widget to show C3 landing page.
   */
  return declare(BaseWidget, {
    templateString: templateStr,
    Res: Res,


    /**
     * Creates an instance of this page.
     * 
     * @param {Object}
     * @param {Object}
     *          parentNode
     */
    constructor: function(params, parentNode)
    {
      this.inherited(arguments);
      params._parent = parentNode;
      // view mode - add/ edit
      params._op = params.oper;

      params._count = 1;
      // ready => ready to save.
      // edit => properties is edited so save btn is disabled.
      // whenever dataStateChange()
      params.dataState = "ready";
    },

    // ////////////////////////////////////////////////////////////////////////////
    // Implementations

    postCreate: function()
    {
      this.inherited(arguments);
    },

    // .....................................................
    startup: function()
    {
      this.inherited(arguments);

      this._populate();
      
      this._setEvents();
      this.showNoDataDiv(true);
    },

    showNoDataDiv(flag) {
      if (flag) {
        this._noDataCont.classList.remove("c3_hide");
      } else {
        this._noDataCont.classList.add("c3_hide");
      }
    },
  
    //..................................
    _createPropertyItem: function(ctx)
    {
      this.showNoDataDiv(false);
      if(!ctx || !ctx.k || ctx.v === void(0) || ctx.v === "") return;
      
      var bx = document.createElement("div");
      bx.classList.add(...["c3_lop_bx","c3_rows_nowrap","c3_align_center"])
      ctx.cls = "c3_lop_bx_" + this._count;
      bx.classList.add(ctx.cls);
      // Edit pencil
      var eDv = document.createElement("div");
      eDv.classList.add(...["c3_lop_x","c3_btn","c3_flex_center_1"]);
      eDv.title = Res.editTitle;

      var ie = document.createElement("i");
      ie.classList.add(...["c3_ti","ti-pencil","c3-c-blue50","c3_sq16"]);
      eDv.appendChild(ie);
      bx.appendChild(eDv);

      var kDv = document.createElement("div");
      kDv.classList.add(...["c3_lop_t", "c3_flex_1", "c3_text_ellipsis"]);
      kDv.title = ctx.k;
      kDv.textContent = ctx.k;
      bx.appendChild(kDv);
      var vDv = document.createElement("div");
      vDv.classList.add(...["c3_lop_t", "c3_lop_v", "c3_flex_1", "c3_wordBreak"]);
      vDv.textContent = ctx.v;
      bx.appendChild(vDv);
      var xDv = document.createElement("div");
      xDv.classList.add(...["c3_lop_x","c3_btn","c3_flex_center_1"])
    
      xDv.title = Res.deleteTitle;
      let iCS = document.createElementNS('http://www.w3.org/2000/svg','svg');
      iCS.classList.add("c3_ti","ti-trash", "c3-c-red40","c3_sq16");
      let iCU = document.createElementNS('http://www.w3.org/2000/svg','use');
      iCU.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', "#trash");
      iCS.append(iCU);
      xDv.append(iCS);

      if(!this.properties) this.properties = {};

      //Add to list of currently loaded props.
      this.properties[ctx.k]  =  ctx.v ;

      bx.appendChild(xDv);
      this.own(bx);

      this.own(
        // edit
        on(eDv, "click", lang.hitch(this, function(){
          udc.log(ctx);
          this.dataStateChange("edit");
          this._onEditProperty(ctx);
        }))
      );

      this.own(
        // delete
        on(xDv, "click", lang.hitch(this, function(){
          udc.log(ctx);
          this._onDeleteProperty(ctx);
        }))
      );

      if(ctx.isEdit)
      {
        this.lopContainer.insertBefore(bx, this.lopContainer.children[ctx.editIndex]);
      }
      else
      {
        this.lopContainer.appendChild(bx);
      }
      this._count++;
    },

    //...................................
    _onDeleteProperty: function (ctx)
    {
      udc.log(ctx);

      var _deleteProperty = function(){
        udc.log(ctx);
        if(ctx && ctx.cls)
        {
          let lopBtn = this.lopContainer.querySelectorAll(".btn-disabled-click");
          for(let i=0; i<lopBtn.length; i++)
          {
            lopBtn[i].classList.remove("btn-disabled-click");
          }
          
          var bxCont = this.lopContainer.querySelector("." + ctx.cls);
          if(bxCont)
          {
            this.lopContainer.removeChild(bxCont);
            // Remove prop from list too.
            this._removeKV(ctx.k);
          }
        }
      };

      var dialogParams = {
        "dialog_title": Res.deleteTitle,
        "dialog_msg": Res.deleteMsg + "'" + ctx.k + "'?",
         // show red or blue button
         primaryBehavior: "destructive"
      };
      var dialog = new DeleteDialogBox(dialogParams);
      dialog.startup();
      dialog.setParent(this);
      //dialog.hideCancel();
      dialog.setCallback(_deleteProperty);
      this.dataStateChange("ready");
      return;
    },

    _onEditProperty: function (ctx)
    {
      C3Util.disableBtn({ btns: [this._addPropertyBtn]});
      // this.dataState = "edit";
      this._hideError();
      if (ctx && ctx.cls) {
        // Set values to the 'Add Property' box.
        // this.lopName.value = ctx.k;
        // this.lopValue.value = ctx.v;
        let lopBtn = this.lopContainer.getElementsByClassName("c3_lop_x");
        for(let i=0; i<lopBtn.length; i++)
        {
          lopBtn[i].classList.add("btn-disabled-click");
        }

        //Delete existing prop.
        var bxCont = this.lopContainer.querySelector("." + ctx.cls);
        //Find indexOf 'ctx.cls'
        var editNodeIndex = Array.from(this.lopContainer.children).indexOf(bxCont);
        if (bxCont)
        {
          this.lopContainer.removeChild(bxCont);
        }
        
        var bx = document.createElement("div");
        bx.classList.add("c3_lop_bx", "c3_rows_nowrap", "c3_align_center", "c3_rows_spacing","c3-bg-c-yellow0");
        ctx.cls = "c3_lop_bx_" + this._count;
        bx.classList.add(ctx.cls);
        // Save checkmark
        var eDv = document.createElement("div");
        eDv.classList.add("c3_lop_x", "c3_btn", "c3_flex_center_1");
        eDv.title = Res.saveTitle;
        let iSS = document.createElementNS('http://www.w3.org/2000/svg','svg');
        iSS.classList.add("c3i_txt","c3-c-blue50","c3_sq16");
        let iSU = document.createElementNS('http://www.w3.org/2000/svg','use');
        iSU.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', "#checkmark");
        iSS.append(iSU);
        eDv.append(iSS);
        bx.appendChild(eDv);
        var kDv = document.createElement("INPUT");
        kDv.setAttribute("type", "text");
        kDv.setAttribute("data-dojo-attach-point", "editLopK");
        kDv.classList.add(...["c3_html_fld", "c3_flex_1", "c3_text_ellipsis"]);
        kDv.setAttribute("value", ctx.k);
        bx.appendChild(kDv);
        var vDv = document.createElement("INPUT");
        vDv.setAttribute("type", "text");
        vDv.setAttribute("data-dojo-attach-point", "editLopV");
        vDv.classList.add(...["c3_html_fld", "c3_lop_v", "c3_flex_1", "c3_wordBreak"]);
        vDv.setAttribute("value", ctx.v);
        bx.appendChild(vDv);
        var xDv = document.createElement("div");
        xDv.classList.add("c3_lop_x", "c3_btn", "c3_flex_center_1");
        xDv.title = Res.deleteTitle;
        let iCS = document.createElementNS('http://www.w3.org/2000/svg','svg');
        iCS.classList.add("c3_ti","ti-trash", "c3-c-red40","c3_sq16");
        let iCU = document.createElementNS('http://www.w3.org/2000/svg','use');
        iCU.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', "#trash");
        iCS.append(iCU);
        xDv.append(iCS);
  
        if(!this.properties) this.properties = {};

        //Add to list of currently loaded props.
        this.properties[ctx.k]  =  ctx.v ;

        bx.appendChild(xDv);
        this.own(bx);
        
        this.own(
          on(this.lopName, "input", lang.hitch(this, function(){
            C3Util.enableBtn({ btns: [this._addPropertyBtn]});
          }))
        );

        this.own(
          // edit
          on(eDv, "click", lang.hitch(this, function(){
            udc.log(ctx);
            this.dataStateChange("ready");
            let editLopK = kDv;
            let editLopV = vDv;
            
            if(editLopK.value && editLopV.value)
            {
              //Delete existing prop.
              var bxCont = this.lopContainer.querySelector("." + ctx.cls);
              if (bxCont)
              {
                this.lopContainer.removeChild(bxCont);
              }
              this._createPropertyItem({k: editLopK.value, v: editLopV.value, isEdit: true, editIndex: editNodeIndex});
              let lopBtn = this.lopContainer.querySelectorAll(".btn-disabled-click");
              for(let i=0; i<lopBtn.length; i++)
              {
                lopBtn[i].classList.remove("btn-disabled-click");
              }
            }
            else
            {
              var alertParams = {
                "dialog_title": "Error",
                "dialog_msg": "Fields are incomplete"
              };
              var alertDialog = new AlertBox(alertParams);
              alertDialog.startup();
              alertDialog.setParent(this);
            }
          }))
        );

        // this.own(
        //   // delete
        //   on(xDv, "click", lang.hitch(this, function(){
        //     udc.log(ctx);
        //     //Delete existing prop.
        //     var bxCont = this.lopContainer.querySelector("." + ctx.cls);
        //     if (bxCont)
        //     {
        //       this.lopContainer.removeChild(bxCont);
        //     }
        //     let lopBtn = this.lopContainer.querySelectorAll(".btn-disabled-click");
        //     for(let i=0; i<lopBtn.length; i++)
        //     {
        //       lopBtn[i].classList.remove("btn-disabled-click");
        //     }
        //     this._createPropertyItem(ctx);
        //   }))
        // );

        this.own(
          // delete
          on(xDv, "click", lang.hitch(this, function(){
            udc.log(ctx);
            this._onDeleteProperty(ctx);
          }))
        );

        this.lopContainer.insertBefore(bx, this.lopContainer.children[editNodeIndex]);
        // Remove prop from list too.
        this._removeKV(ctx.k);
      }
      return;
    },
    //...................................
    _populate: function()
    {
      if (!this.properties || Object.keys(this.properties).length === 0) return;
      for(var key in this.properties){
        this._createPropertyItem({ k: key, v: this.properties[key] });
      }
    },

    _removeKV: function(del_k)
    {
      if (this.properties && Object.keys(this.properties).length)
      {
        delete this.properties[del_k];
      }
    },

    //.............................
    // @return {key: value, ...} 
    getDataObject: function()
    {
      let editLopK = this.lopContainer.querySelector("[data-dojo-attach-point='editLopK']");
      let editLopV = this.lopContainer.querySelector("[data-dojo-attach-point='editLopV']");
      if(editLopK || editLopV)
      {
        var alertParams = {
          "dialog_title": "Error",
          "dialog_msg": "Fields are incomplete"
        };
        var alertDialog = new AlertBox(alertParams);
        alertDialog.startup();
        alertDialog.setParent(this);
        return false;
      }
      else
      {
        var strObject = {};
        var bxList = this.lopContainer.querySelectorAll(".c3_lop_bx");
        if(bxList && bxList.length)
        {
          var k, v;
          for(var i = 0; i < bxList.length; i++)
          {
            k = bxList[i].querySelector(".c3_flex_1");
            if(!k) continue;
            v = bxList[i].querySelector(".c3_lop_v");
            if(!v) continue;
            strObject[k.textContent] = v.textContent;
          }
        }
        return strObject;
      }
    },

    //...............................
    // @param { properties: k1=v1|k2=v2 }
    setProperties: function(ctx)
    {
      if (!ctx || !ctx.properties || Object.keys(ctx.properties).length === 0) return;
      this.properties = ctx.properties;
      this._populate();
    },

    //................................
    _addProperty: function()
    {
      
      if (this.lopName.value === void (0) || this.lopName.value === "") {
        this._showError(Res.errEmptyKey);
        return;
      }

      if (this.lopValue.value === void (0) || this.lopValue.value === "") {
        this._showError(Res.errEmptyVal);
        return;
      }

      if (!this._validateParam()) {
        this._showError(Res.errInvParams);
        return;
      }

      // Check if property already present
      if (this.properties && this.properties.hasOwnProperty(this.lopName.value)) {
        this._showError(Res.errDupKey);
        return;
      }

      this._hideError();

      // create property item
      this._createPropertyItem({k: this.lopName.value, v: this.lopValue.value});
      // reset
      this.lopName.value = "";
      this.lopValue.value = "";
      this.dataStateChange("ready");
      C3Util.disableBtn({ btns: [this._addPropertyBtn]});
    },

    _showError(errmsg){
      domClass.remove(this.errorDiv, "c3_hide");
      this.errorDiv.textContent = errmsg;
    },

    _hideError(errmsg){
      this.errorDiv.textContent = "";
      domClass.add(this.errorDiv, "c3_hide");
    },

    //............................
    _validateParam: function()
    {
      var flag = true;

      if (this.lopName.value && this.lopName.value !== "")
      {
        validator.setErrorMsg(Res.errorMsg);
        var result = validator.isValidOrShowAlert({
          vMethodName: "isValidName",
          vParam: this.lopName.value
          //,vErrorMessage: Res.paramErrorNameMsg
        });
        if (!result || !result.pass)
        {
          // failed - validation
          return false;
        }
      }

      return flag;
    },

    //..................................
    _setEvents: function()
    {
      this.own(
        on(this._addPropertyBtn, "click", lang.hitch(this, "_addProperty"))
      );
      
      this.own(
        on(this.lopName, "input", lang.hitch(this, function(){
          C3Util.enableBtn({ btns: [this._addPropertyBtn]});
          this.dataStateChange("edit");
        }))
      );
    },

    //....................................
    destroy: function()
    {
      this.inherited(arguments);
    },

    _end: 0
  });

});
