/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define(
[
 "dojo/_base/declare", 
 "dojo/_base/lang", 
 "dojo/on", 
 "udc/core/commons/loader", 
 "dojo/dom-class",
 "udc/udc", 
 "z1/c3/desktop/start",
 "dojo/topic",
 "udc/core/rt/widgets/_BaseWidget",
 "dojo/text!z1/c3/desktop/page/templates/developer/eventChannels/ChannelConfigManager.html",
 "dojo/i18n!z1/c3/desktop/page/nls/ChannelConfigManagerRes",
 "z1/c3/desktop/page/fields/C3ToggleBtn",
 "dojo/dom-construct",
 "z1/c3/utils/validator",
 "z1/c3/utils/Permissions",
 "z1/c3/desktop/data/sdk/SdkData",
 "z1/common/C3Util",
 ],
function(declare, lang, on, loader, domClass, udc, z1, topic,
  BaseWidget, templateStr, Res, C3ToggleBtn, domConstruct, validator,
  UserACL, SdkData, C3Util)
{

  /**
   * Creates a widget to show C3 landing page.
   */
  return declare(BaseWidget,
  {
    templateString : templateStr,
    Res: Res,
    
    _data: {},
    MALFORMED_DATA: false,
    
    OS: {
    	IOS: "ios",
    	ANDROID: "android",
    	HTML5: "html5"
    },

    /**
     * Creates an instance of this page.
     * 
     * @param {Object}
     * @param {Object}
     *          parentNode
     */
    constructor: function(params, parentNode)
    {

      this.inherited(arguments);
      params = params ? params : {};
      
      params._name = params.name;
      params._title = params._titleName ? params._titleName : "Desktop";
      params.description = params.description ? params.description : "Configure properties";
      params.device = params.device ? params.device : "desktop";
      params.os = params.os ? params.os : "html5";
      
      if (parentNode)
      {
        this._parent = parentNode;
      }
      
    },

    // ////////////////////////////////////////////////////////////////////////////
    // Implementations

    postCreate: function()
    {
      this.inherited(arguments);
      
      // the following call sets the value for this._ypc 
      // also adds dataset attributes to the top container
      UserACL.checkPermission(this, this.configPage, 'datachannels', ['c']);
    },

    // .....................................................
    startup: function()
    {
      this.inherited(arguments);
      
      if (this.os === this.OS.HTML5)
      {
        this._hideSelectedConfigOptions(true);
        domClass.add(this.androidCont, "c3_hide");
        domClass.remove(this.html5Cont, "c3_hide");
      }
      if (this.os === this.OS.ANDROID)
      {
        domClass.add(this.html5Cont, "c3_hide");
        domClass.remove(this.androidCont, "c3_hide");
        this._showTTLSettings();
      }
      if (this.os === this.OS.IOS)
      {
        domClass.add(this.androidCont, "c3_hide");
        domClass.add(this.html5Cont, "c3_hide");
        this._showTTLSettings();
      }

      this._setEvents();
      this._loadSavedActions();
    },
    
    _hideSelectedConfigOptions: function(flag)
    {
      domClass.remove(this.locationDetailsCont, "c3_hide");
      domClass.remove(this._geoCoarseLocCont,"c3_hide");
      this._showGfResponsiveness();

      if (flag)
      {
        domClass.add(this.locationDetailsCont, "c3_hide");
        domClass.add(this._geoCoarseLocCont,"c3_hide");
        this._hideGfResponsiveness();
      }
    },

    _hideGfResponsiveness: function()
    {
      if (this.os === this.OS.ANDROID)
      {
        domClass.add(this.gfRespCont,"c3_hide");
      }
    },

    _showGfResponsiveness: function()
    {
      if (this.os === this.OS.ANDROID)
      {
        domClass.remove(this.gfRespCont,"c3_hide");
      }
    },
    
    // ...........................
    
    /**
     * Called on load of this page
     * 
     * Track payload locally for CRUD
     * Create Tiles for each payload
     */
    _loadSavedActions: function()
    { 
      udc.showBusy(this._configManagerCont);

      let _reqPayload = {
        cacheOk: true,
        device: this.device,
        os: this.os,
      };

      SdkData.api.getSdkConfig(_reqPayload).then(lang.hitch(this, function(data){
        udc.hideBusy();
        var data2 = {};
        if (data && data.status === "fail")
        {
          var msgTxt = data.reason || data.message || "Failed to get config";
          udc.info(msgTxt);
        }
        else if (data)
        {
          data2 = data;
        }
        this._setData(data2);
        this._displayData();
      }),lang.hitch(this, function(error){
        udc.hideBusy();
        udc.log(error);
      }));
    },
    
    _save: function()
    {
      this._collectDataToSave();
      var _data = this._getData();

      if (this.MALFORMED_DATA) return;

      let _reqPayload = {
        device: this.device,
        os: this.os,
        payload: _data,
      };

      SdkData.api.saveSdkConfig(_reqPayload).then(lang.hitch(this, function(data)
      {
        udc.success(Res.successMsg);
      }), lang.hitch(this, function(error)
      {
        C3Util.handleServerError(error);
      }));
    },
    
    // ...........................
    // helper functions
    
    _updateData: function(ctx)
    {
      if (ctx && JSON.stringify(ctx) !== "{}")
      {
        var keys = Object.keys(ctx);
        for (var i = 0; i < keys.length; i++)
        {
          // update or create the keys in _data
          this._data[keys[i]] = ctx[keys[i]];
        }
      }
    },
    
    _collectDataToSave: function()
    {
      // get all the data populated on this page
      // get existing data set
      var _data = this._getData();
      // reset it to empty
      _data = {};
      
      this.MALFORMED_DATA = false;
      
      // create data set
      // Common to Android, iOS and HTML5
      _data["locationEnabled"] = (this._allowLocationInSDKTgl) ? this._allowLocationInSDKTgl.value : false;
  
      // Specific to Android and iOS
      if (this.os === this.OS.ANDROID || this.os === this.OS.IOS)
      {      
        _data["shouldNotCheckPermission"] = false;  
        
         // get time val and unit
        //default
        _data["reqRateValue"] = "30";
        _data["reqRateUnit"] = "minutes";

        if (this._allowLocationInSDKTgl && this._allowLocationInSDKTgl.value)
        {
          _data["reqRateValue"] = this._timeVal.value == "" ? "30" : this._timeVal.value;
          _data["reqRateUnit"] = this._timeUnit.value;
          
          _data["coarseLocationEnabled"] = (this._allowCoarseLocationInSDKTgl) ? this._allowCoarseLocationInSDKTgl.value : false;
          if(this._allowCoarseLocationInSDKTgl && this._allowCoarseLocationInSDKTgl.value){
            _data["coarseLocationKey"] = this._coarseApiKey.value;
          }
        }
      }
      
      // Specific to Android
      if (this.os === this.OS.ANDROID)
      {
        // open the app if the user touches a push
        _data["openAppByDefault"] = this._launchPnAndroidAppTgl ? this._launchPnAndroidAppTgl.value : false;

        _data["gfResponsiveness"] = "5";
        
        if (this._allowLocationInSDKTgl && this._allowLocationInSDKTgl.value) 
        {
          _data["gfResponsiveness"] = this._gfRespVal.value == "" ? "5" : this._gfRespVal.value;
        }
        
        // get pushURL
        //default
        _data["pushURL"] = "";
        if (this._launchPnAndroidAppTgl && this._launchPnAndroidAppTgl.value)
        {
          if (this._pushURL.value == "")
          {
            udc.alert("Provide an Application URL");
            this.MALFORMED_DATA = true;
            return;
          }
          if (this._validate("pushURL", this._pushURL.value))
          {
            _data["pushURL"] = this._pushURL.value == "" ? "": this._pushURL.value;
          }
          else
          {
            udc.alert(Res.invalidDL);
            this.MALFORMED_DATA = true;
            return;
          }
        }
      }
      
      // Specific to HTML5
      if (this.os === this.OS.HTML5)
      {
        // UI is now showing the positive statement for this feature
        // but platform and SDK are still looking for the negative "_disableURLParamCapture"
        // so send the opposite of the setting on the UI
        // <tgl btn: true> Capture URL params --send-> _disable: false
        // <tgl btn: false> Capture URL params -send-> _disable: true
        // default value on UI for "Capture URL params" is true
        // therefore default value for _disable on SDK and platform is false

        let tglBtnVal = this._disableParamCaptureTgl && this._disableParamCaptureTgl.value;
        // default value sent to BE is false
        let _disable = tglBtnVal != void(0) && (tglBtnVal == false || tglBtnVal == "false") ? true : false;
        _data["disableUrlParamCapture"] = _disable;
        
        if (this._allowLocationInSDKTgl && this._allowLocationInSDKTgl.value)
        {
          _data["geoCodeReqRateValue"] = this._gcLocTimeVal.value == "" ? "30" : this._gcLocTimeVal.value;
          _data["geoCodeReqRateUnit"] = this._gcLocTimeUnit.value;
          _data["geoCodeLocationKey"] = this._geoCodeLocApiKey.value;
        }
      }
      
      // save new data set
      this._setData(_data);
    },
    
    _displayData: function()
    {
      var _data = this._getData();
      
      this._createAllowLocationInSDK();
      
      if (_data)
      {
        // Common to all
        // ~

        // Specific to Android and iOS
        if (this.os === this.OS.ANDROID || this.os === this.OS.IOS)
        {
          this._createAllowCoarseLocationInSDK();
          this._permissionCheck.checked = _data.shouldNotCheckPermission ? _data.shouldNotCheckPermission : false;
          
          if (_data.locationEnabled)
          {
            domClass.remove(this.locationDetailsCont, "c3_hide");
            domClass.remove(this._geoCoarseLocCont,"c3_hide");
            this._showGfResponsiveness();


            if(_data.coarseLocationEnabled){
              domClass.remove(this.coarseLocationContDetail,"c3_hide");
              if(_data.coarseLocationKey !== undefined){
                this._coarseApiKey.value = _data.coarseLocationKey;
              }
            }
            this._timeVal.value = _data.reqRateValue;
            this._timeUnit.value = _data.reqRateUnit;
            this._gfRespVal.value = _data.gfResponsiveness;
          }
        }

        if (_data.ttl)
        {
          // default is # of mins in 24 hours - 24*60
          this.ttl = _data.ttl ? _data.ttl : 1440;
          this.ttlValue.value = this.ttl;
        }
        
        // Specific to Android
        if (this.os === this.OS.ANDROID)
        {
          // if there is no URL defined then leave the checkbox unchecked
          
          if (_data.openAppByDefault && _data.pushURL && _data.pushURL != '')
          {
            domClass.remove(this.defaultPushURL, "c3_hide");
            this._pushURL.value = _data.pushURL;
          }

          this._createAndroidPnAppLaunch();
        }
        
        // Specific to HTML5
        if (this.os === this.OS.HTML5)
        {
          this._createHTML5ParamCapture();
          
          if (_data.locationEnabled)
          {
            this._html5GeoLocCont.classList.remove("c3_hide");
            if (_data.geoCodeLocationKey) this._geoCodeLocApiKey.value = _data.geoCodeLocationKey;
            this._gcLocTimeVal.value = _data.geoCodeReqRateValue != void(0) ? _data.geoCodeReqRateValue : 30;
            this._gcLocTimeUnit.value = _data.geoCodeReqRateUnit || "minutes";
          }
        }
      }
    },
    
    _showTTLSettings: function()
    {
      domClass.remove(this.propsCont, "c3_hide");
    },
    
    _showLocationProperties: function()
    {
      domClass.remove(this.locCont, "c3_hide");
    },

    // ...........................................
    _createAndroidPnAppLaunch: function () {
      if (this._launchPnAndroidAppTgl || this.os != this.OS.ANDROID) return;

      let data = this._getData();
      let initVal = data.openAppByDefault === undefined ? true : data.openAppByDefault;

      this._launchPnAndroidAppTgl = new C3ToggleBtn({
        _parent: this,
        hideDescription: true,
        cls: "c3_cCMTglBtn",
        value: initVal,
        parentCallback: "_toggleAndroidPnAppLaunch"
      });
      this._launchPnAndroidAppTgl.startup();
      domConstruct.place(this._launchPnAndroidAppTgl.domNode, this._pnOpenAndroidAppTglC, "only");
      this.own(this._launchPnAndroidAppTgl);

      this._toggleAndroidPnAppLaunch(initVal);
    },

    // .........................................
    _toggleAndroidPnAppLaunch: function (flag = false) {
      if (this.os == this.OS.ANDROID) {
        domClass.remove(this.androidCont, "c3_hide");
        if (flag) {
          domClass.remove(this.defaultPushURL, "c3_hide");
        } else {
          domClass.add(this.defaultPushURL, "c3_hide");
        }

        this._updateData({ openAppByDefault: flag });
      }
    },

    // ...........................................
    _createHTML5ParamCapture: function () 
    {
      if (this._disableParamCaptureTgl || this.os != this.OS.HTML5) return;

      let data = this._getData();

      // UI is now showing the positive statement for this feature
      // but platform and SDK are still looking for the negative "_disableURLParamCapture"
      // so get the value from data and flip it when displaying it on the UI
      // data.disable: false -display-> <tgl btn: true> Capture URL params
      // data.disable: true  -display-> <tgl btn: false> Capture URL params
      // default value on UI for "Capture URL params" is true
      // therefore default value for _disable on SDK and platform is false

      let _disable = data && data.disableUrlParamCapture;
      // default value set on UI is true
      let initVal = _disable != void(0) && (_disable == true || _disable == "true") ? false : true;

      this._disableParamCaptureTgl = new C3ToggleBtn({
        _parent: this,
        hideDescription: true,
        cls: "c3_cCMTglBtn",
        value: initVal,
        parentCallback: "_toggleHTML5ParamCapture"
      });
      this._disableParamCaptureTgl.startup();
      domConstruct.place(this._disableParamCaptureTgl.domNode, this._disableUrlParamAppTglC, "only");
      this.own(this._disableParamCaptureTgl);

      this._toggleHTML5ParamCapture(initVal);
    },

    // .........................................
    _toggleHTML5ParamCapture: function (flag = false) {
      if (this.os == this.OS.HTML5) {
        this._updateData({ disableUrlParamCapture: flag });
      }
    },

    // ...........................................
    _createAllowLocationInSDK: function ()
    {
      if (this._allowLocationInSDKTgl) return;

      this._allowLocationInSDKTgl = new C3ToggleBtn({
        _parent: this,
        hideDescription: true,
        cls: "c3_cCMTglBtn",
        value: (this._data && this._data.locationEnabled === true) ? true : false,
        parentCallback: "_allowLocationInSDKChange"
      });
      this._allowLocationInSDKTgl.startup();
      domConstruct.place(this._allowLocationInSDKTgl.domNode, this._allowLocSDKTglC, "only");
      this.own(this._allowLocationInSDKTgl);
    },

    // .........................................
    _allowLocationInSDKChange: function (flag = false)
    {
      domClass.add(this.locationDetailsCont, "c3_hide");
      domClass.add(this._geoCoarseLocCont,"c3_hide");
      this._html5GeoLocCont.classList.add(...["c3_hide"]);
      this._hideGfResponsiveness();

      if (this.os !== this.OS.HTML5)
      {
        if(flag)
        {
            domClass.remove(this.locationDetailsCont, "c3_hide");
            domClass.remove(this._geoCoarseLocCont,"c3_hide");
            this._showGfResponsiveness();
        }
      }
      else 
      {
        if (flag) this._html5GeoLocCont.classList.remove("c3_hide");
      }
      this._updateData({locationEnabled: flag});
    },

    // ...........................................
    _createAllowCoarseLocationInSDK: function ()
    {
      if (this._allowCoarseLocationInSDKTgl) return;

      this._allowCoarseLocationInSDKTgl = new C3ToggleBtn({
        _parent: this,
        hideDescription: true,
        cls: "c3_cCMTglBtn",
        value: (this._data && this._data.coarseLocationEnabled === true) ? true : false,
        parentCallback: "_allowCoarseLocationInSDKChange"
      });
      this._allowCoarseLocationInSDKTgl.startup();
      domConstruct.place(this._allowCoarseLocationInSDKTgl.domNode, this._allowCoarseLocSDKTglC, "only");
      this.own(this._allowCoarseLocationInSDKTgl);
    },

    // .........................................
    _allowCoarseLocationInSDKChange: function (flag = false)
    {
      domClass.add(this.coarseLocationContDetail,"c3_hide");
      if(this.os !== this.OS.HTML5){
        if(flag)
        {
          domClass.remove(this.coarseLocationContDetail, "c3_hide");
        }
      }
      this._updateData({coarseLocationEnabled: flag});
    },

    // ...........................
    // getters & setters

    _setData: function(data = {})
    {
      this._data = data;
    },
    
    _getData: function()
    {
      return this._data;
    },
    
    // ...........................
    _setEvents: function()
    {
      this.own(
        
        on(this._permissionCheck, "change", lang.hitch(this, function(){
          this._updateData({shouldNotCheckPermission: this._permissionCheck.checked});
        })),
        
        on(this._timeVal, "change", lang.hitch(this, function(){
        	if(isNaN(this._timeVal.value) || this._timeVal.value=="" || this._timeVal.value < 1 || (this._timeVal.value - Math.floor(this._timeVal.value)) !== 0){
            	validator._alert({text: Res.vInvalidTimeInterval});
            	this._timeVal.value = 30;
            }
          })),

        on(this._gfRespVal, "change", lang.hitch(this, function () {
          if (isNaN(this._gfRespVal.value) || this._gfRespVal.value == "" || this._gfRespVal.value < 1 || (this._gfRespVal.value - Math.floor(this._gfRespVal.value)) !== 0) {
            validator._alert({ text: Res.invalidGfResponsiveness });
            this._gfRespVal.value = 5;
          }
        })),
        
        on(this._saveBtn, "click", lang.hitch(this, function(){
          this._saveSdkConfig();
        })),      
        );
    },

     /**
     * Save the ttl values for ios and android devices
     */
    _saveSdkConfig: function () 
    {
      if (this.os === this.OS.HTML5) 
      {
        // HTML5 does not have TTL value. 
        // So we save other config only.
        this._save();
      } 
      else 
      {
        if (parseInt(this.ttlValue.value) <= 0) 
        {
          udc.alert("Time to live has to be greater than 0 minutes.");
          return;
        }

        // Check if other config is also valid before calling save.
        this._collectDataToSave();
        if (this.MALFORMED_DATA) return;


        this.ttl = this.ttlValue.value ? this.ttlValue.value : 1440;
        // reset the value shown in the input 
        this.ttlValue.value = this.ttl;
        var output = {};

        // android.phone.ttl.value
        var ttlKey = this.os + "." + this.device + ".ttl.value";
        output[ttlKey] = "" + this.ttl;

        let _reqPayload = {
          payload: output,
        };

        // Save TTL
        SdkData.api.saveSystemConfig(_reqPayload).then(lang.hitch(this, function (data) 
        {
          stat = udc.getStatus(data);

          if (!stat.isSuccessful()) 
          { 
            udc.log('Failed to save TTL value. ', data); 
          }
          // Save other config.
          this._save();

        }), lang.hitch(this, function (error) {
          udc.log(error);
          udc.error(Res.failedMsg);
        }));
      }
    },
    
    // verification
    _validate: function(type, data)
    {
      switch(type)
      {
        case "pushURL":
        {
          // ensure that data has "://" and that it is not the first value or the last value of the string
          if(data.indexOf("://") > 0 && (data.indexOf("://") + 3 < data.length)) return true;
          return false;
        }	
      }
    },
    
    // cleanup
    
    _onClose: function(){
      z1.fireEvent(z1.Events.showDeviceChannel, {          
        itemData: {
          name: this._name, 
          description: this._name + " channel"          
          }
      });
    },
    
    destroy: function()
    {
      this.inherited(arguments);
    },

    _end: 0
  });

});
