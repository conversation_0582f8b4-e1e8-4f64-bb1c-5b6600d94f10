/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define(
  [
    "dojo/_base/declare",
    "z1/c3/desktop/start",
    "dojo/_base/lang",
    "dojo/on",
    "udc/udc",
    "udc/core/commons/loader",
    "udc/core/rt/widgets/_BaseWidget",
    "dojo/text!z1/c3/desktop/page/templates/developer/activityQueries/activityChart/QueryChartDesigner.html",
    "dojo/i18n!z1/c3/desktop/page/nls/QueryChartDesignerRes",
    "z1/c3/desktop/page/C3DeleteDialogBox", "z1/c3/desktop/data/C3Context",
    "z1/c3/desktop/page/developer/activityQueries/activityChart/AddAxis",
    "z1/c3/desktop/page/developer/activityQueries/activityChart/QueryChartRendererHelper",
    "z1/c3/desktop/page/developer/activityQueries/activityChart/QChartTypeSelector",
    "z1/c3/desktop/page/developer/activityQueries/activityChart/ChartTypeDecoratorFactory",
    "z1/c3/desktop/page/metrics/ChartRenderer",
    "z1/c3/desktop/page/developer/activityQueries/ActivityQueryDef", "dojo/dom-construct",
    "dojo/dom-class", "dijit/Dialog", "dojo/Deferred", "dojo/promise/all",
    "z1/c3/utils/Permissions",
    "dojo/text!z1/c3/desktop/data/query/chartProps.json",
    "dojo/text!z1/c3/desktop/data/query/continent.json",
    "dojo/text!z1/c3/desktop/data/query/country.json",
    "dojo/text!z1/c3/desktop/data/query/state.json"],
  function(declare, z1, lang, on, udc, loader, BaseWidget, templateStr, Res, DeleteDialogBox,
    C3Context, AddAxis, QueryChartRendererHelper, QChartTypeSelector, ChartTypeDecoratorFactory,
    ChartRenderer, ActivityQueryDef, domConstruct, domClass, Dialog, Deferred, all, UserACL, chartPropsJson,
    continentJson, countryJson, stateJson)
  {

    return declare(
      BaseWidget,
      {
        templateString: templateStr,

        Res: Res,

        _xAxis: null,

        _yAxis: null,

        _secondAxis: null,

        _qData: null,

        X_AXIS: "x",

        Y_AXIS: "y",

        _control: null,

        _allProps: null,

        _showGridFlag: "off",

        _gridProps: {
          "display": false,
          "hMajorLines": true,
          "includeZero": true,
          "majorHLine": {
            "color": "#ededed",
            "width": 2
          }
        },

        _xAxisProps: {
          "majorTickStep": 2,
          "includeZero": true,
          "stroke": "#cccccc",
          "font": "normal normal 8pt Arial",
          "fontColor": "#cccccc",
          "majorTick": {
            "color": "#cdcdcd",
            "length": 6
          }
        },

        _yAxisProps: {
          "includeZero": true,
          "stroke": "white",
          "font": "normal normal 10pt Tahoma",
          "fontColor": "#cccccc",
          "minorTicks": false,
          "majorTick": {
            "color": "gray",
            "length": 0
          },
          "minorTick": {
            "color": "gray",
            "length": 0
          },
          "natural": true,
          "fixed": true
        },

        _queryChart: null,

        _chartAxes: null,

        _dashboardName: "udc.system.core.CommonOOTB:SampleQueryChartDash",

        _datasheet: null,

        _queryId: "",

        NUMBER: "number",

        _chartName: "",

        _chartDesc: "",

        _chartTypeMap: {
          "Default": "Lines",
          "StackedLines": "Stacked Lines",
          "Areas": "Areas",
          "StackedAreas": "Stacked Areas",
          "Columns": "Columns",
          "StackedColumns": "Stacked Columns",
          "ClusteredColumns": "Clustered Columns",
          "Bars": "Bars",
          "ClusteredBars": "Clustered Bars",
          "StackedBars": "StackedBars",
          "Scatter": "Scatter Plot",
          "Pie": "Pie",
          "Table": "Table",
          "Geo": "Geo",
          "Bubble": "Bubble"
        },

        TITLE_SEP: " - ",

        /**
         * Creates an instance of this page. params holds the palette data which is automatically
         * placed in the DOM node.
         * 
         * @param {Object}
         * @param {Object}
         *          ctx - pass signalId or other extra data. ctx.itemCategory - segment, activity,
         *          paletteItem (signal sources)
         */
        constructor: function(params)
        {
          this.inherited(arguments);
          params._xAxis = [];
          params._yAxis = [];
          params._secondaryXAxis = [];
          params._secondaryYAxis = [];
          params._qData = (params.queryData) ? params.queryData : null;

          params._datasheet = (params.datasheet) ? params.datasheet : null;
          params._queryId = (params.queryId) ? params.queryId : "";
          params._chartName = (params.chartName) ? params.chartName : "";
          params._chartDesc = params?.chartDesc || params?.datasheet?.description || "";
          params._mode = (params.mode) ? params.mode : "new";
          params._control = {};
          params._allProps = [];
          params._chartAxes = [];
          
          params.FEvents = lang.mixin({
            "myWorkspace": {
              fireEvent: z1.Events.myWorkspace,
              params: {
                _tabName: "charts"
              }
            },
            "chartInstances": {
              fireEvent: z1.Events.showAllCharts,
              params: {}
            }
          }, params.FEvents);
        },

        postCreate: function()
        {
          this.inherited(arguments);
          
          UserACL.checkPermission(this, this.qcHead, 'mycharts', ['c']);
        },

        startup: function()
        {
          this.inherited(arguments);

          this.chartNameDiv.innerHTML = this.TITLE_SEP + this._chartName;

          this.queryName.innerHTML = this.TITLE_SEP + this.queryId;
          // showing the chart type widget inline and selecting the column chart by default
          this._selectNewChartType();

          this.setEvents();

          // fetch the JSON file containing all chart types and their respective
          // properties
          this._fetchChartProperties();

        },

        setEvents: function()
        {
          this.own(

          on(this.addXaxisBtn, "click", lang.hitch(this, function()
          {
            this._selectAxisToAdd("x");
          })), on(this.addYaxisBtn, "click", lang.hitch(this, function()
          {
            this._selectAxisToAdd("y");
          })), on(this.changeChartBtn, "click", lang.hitch(this, function()
          {
            this._selectNewChartType();
          })),

          /*
           * on(this.showGridDD, "change",lang.hitch(this, function(){ this._onShowGridChanged();
           * })),
           */
          on(this.gridColorPicker, "change", lang.hitch(this, function()
          {
            this._onGridColorChanged();
          })), on(this.xAxisStrokeColorPicker, "change", lang.hitch(this, function()
          {
            this._onXAxisStrokeColorChanged();
          })), on(this.xAxisFontColorPicker, "change", lang.hitch(this, function()
          {
            this._onXAxisFontColorChanged();
          })), on(this.renderBtn, "click", lang.hitch(this, function()
          {
            this._renderGraph();
          })), on(this.doneBtn, "click", lang.hitch(this, function()
          {
            this._onClose();
          })), on(this.closeLink, "click", lang.hitch(this, function()
          {
            if (this.viewSrc != "" && this.FEvents[this.viewSrc]) z1.fireEvent(this.FEvents[this.viewSrc].fireEvent, this.FEvents[this.viewSrc].params);
            else
            {
              // Used when charts and dashboards were listed under My Workspace
               z1.fireEvent(z1.Events.myWorkspace, {
                 _tabName: "charts"
               });
            }
          })), on(this.showGridDD, "click", lang.hitch(this, function()
          {
            var selVal = this.showGridDD.getAttribute("data-toggle");
            if (selVal.toLowerCase() === "off")
            {
              this.showGridDD.setAttribute("data-toggle", "on");
              this._toggleDisplayMode(this.showGridDD, true);
            }
            else
            {
              this.showGridDD.setAttribute("data-toggle", "off");
              this._toggleDisplayMode(this.showGridDD, false);
            }
            this._onShowGridChanged();
            this._renderGraph();
          })), 
          on(this.sortToggle, "click", lang.hitch(this, function()
	      {
	        var selVal = this.sortToggle.getAttribute("data-toggle");
	        if (selVal.toLowerCase() === "off")
	        {
	          this.sortToggle.setAttribute("data-toggle", "on");
	          this._toggleDisplayMode(this.sortToggle, true);
	        }
	        else
	        {
	          this.sortToggle.setAttribute("data-toggle", "off");
	          this._toggleDisplayMode(this.sortToggle, false);
	        }
	        this._onShowGridChanged();
	        this._renderGraph();
	      })),         
          on(this.chartTitleElem, "change", lang.hitch(this, function()
          {
            this._renderGraph();
          })), on(this.xLegendElem, "change", lang.hitch(this, function()
          {
            this._renderGraph();
          })), on(this.yLegendElem, "change", lang.hitch(this, function()
          {
            this._renderGraph();
          })), on(this.geoDisplayMode, "change", lang.hitch(this, function()
          {
            this._renderGraph();
          })), on(this.delBtn, "click", lang.hitch(this, function()
          {
            this._onDeleteAxis();
          })), on(this.regionBtn, "click", lang.hitch(this, function()
          {
            this._showRegionOptions();
          })), on(this.region_type, "change", lang.hitch(this, function()
          {
            this._onRegionTypeChange();
          })), on(this.regionSaveBtn, "click", lang.hitch(this, function()
          {
            this._onRegionDetailSave();
          })), on(this.regionBack, "click", lang.hitch(this, function()
          {
            this.goBack();
            this._hideAxisDetailsScreen();
            this._hideRegionDetailsScreen();

          })), on(this.saveBtn, "click", lang.hitch(this, function()
          {
            this._onAxisDetailSave();
          })), on(this.axis_type, "change", lang.hitch(this, function()
          {
            this.axis_format.value = "none";
            this._onFilterChartFormatter();
          }))

          );
        },

        _onFilterChartFormatter: function()
        {
          var type = this.axis_type.value;
          switch (type.toLowerCase())
          {
            case "number":
              domClass.add(this.dt_form, "c3_hide");
              domClass.add(this.hr_form, "c3_hide");
              domClass.add(this.min_form, "c3_hide");
              domClass.add(this.sec_form, "c3_hide");
              domClass.remove(this.decimal, "c3_hide");
              domClass.remove(this.scientific, "c3_hide");
              domClass.remove(this.currency, "c3_hide");
              domClass.remove(this.percent, "c3_hide");
              domClass.remove(this.short, "c3_hide");
              domClass.remove(this.long, "c3_hide");
              domClass.remove(this.none, "c3_hide");
            break;
            case "date":
              domClass.remove(this.dt_form, "c3_hide");
              domClass.remove(this.hr_form, "c3_hide");
              domClass.add(this.min_form, "c3_hide");
              domClass.add(this.sec_form, "c3_hide");
              domClass.remove(this.none, "c3_hide");
              domClass.add(this.decimal, "c3_hide");
              domClass.add(this.scientific, "c3_hide");
              domClass.add(this.currency, "c3_hide");
              domClass.add(this.percent, "c3_hide");
              domClass.add(this.short, "c3_hide");
              domClass.add(this.long, "c3_hide");
            break;
            case "timeofday":
              domClass.add(this.dt_form, "c3_hide");
              domClass.add(this.hr_form, "c3_hide");
              domClass.remove(this.min_form, "c3_hide");
              domClass.remove(this.sec_form, "c3_hide");
              domClass.remove(this.none, "c3_hide");
              domClass.add(this.decimal, "c3_hide");
              domClass.add(this.scientific, "c3_hide");
              domClass.add(this.currency, "c3_hide");
              domClass.add(this.percent, "c3_hide");
              domClass.add(this.short, "c3_hide");
              domClass.add(this.long, "c3_hide");
            break;
            case "string":
            case "day":
            case "month":
            case "address":
            case "city":
            case "state":
            case "country":
            case "latlong":
            default:
              domClass.add(this.dt_form, "c3_hide");
              domClass.add(this.hr_form, "c3_hide");
              domClass.add(this.min_form, "c3_hide");
              domClass.add(this.sec_form, "c3_hide");
              domClass.add(this.decimal, "c3_hide");
              domClass.add(this.scientific, "c3_hide");
              domClass.add(this.currency, "c3_hide");
              domClass.add(this.percent, "c3_hide");
              domClass.add(this.short, "c3_hide");
              domClass.add(this.long, "c3_hide");
              domClass.remove(this.none, "c3_hide");
            break;
          }
        },

        _toggleDisplayMode: function(elem, isAdvancedMode)
        {
          if (isAdvancedMode)
          {
            domClass.remove(elem, "fa-toggle-off");
            domClass.add(elem, "fa-toggle-on");
            domClass.remove(elem, "c3_dash_toggle_off");
            domClass.add(elem, "c3_dash_toggle_on");
          }
          else
          {
            domClass.add(elem, "fa-toggle-off");
            domClass.remove(elem, "fa-toggle-on");
            domClass.remove(elem, "c3_dash_toggle_on");
            domClass.add(elem, "c3_dash_toggle_off");
          }
        },

        /**
         * Fetch the JSON file with the chart properties based on chart types . Will act as a lookup
         * for the control object type and properties
         */
        _fetchChartProperties: function()
        {
          var data = [];
          if(chartPropsJson)
          {
            data = typeof chartPropsJson == 'string' ? JSON.parse(chartPropsJson) : chartPropsJson;
          }
          this._allProps = data;
          this._init();
        },

        _init: function()
        {
          // if chart is not saved then open the designer with default settings and values
          if (!this._datasheet)
          {
            // default chart type is columns unless specified otherwise
            this._onChartTypeChange(Res.columns);

            // this is the color axis that will be populated with the values of left
            // over X axis other than the ones
            // chosen under the primary x axis category
            this._populateSecondaryXAxis();

            // show the second y axis
            this._populateSecondaryYAxis();
          }
          // if chart is available then draw the chart as per the saved data
          else
          {
            // this._loadDatasheet();
            this._populateDesigner();
            this._renderGraph();
          }
        },

        _populateDesigner: function()
        {
          // control - chart type
          var type = (this._datasheet.control && this._datasheet.control.chartType) ? this._datasheet.control.chartType
              : Res.columns;
          if (this.qChartTypeSelector)
          {
            this.qChartTypeSelector.onChartTypeChange(type);
          }
          // this._onChartTypeChange(type);

          var chartProps = this._getChartProps();

          this._control.props = chartProps;

          // grid
          if (this._datasheet.control && this._datasheet.control.grid)
          {
            this._gridProps = this._datasheet.control.grid;

          }
          this.showGridDD.value = (this._gridProps.display) ? "on" : "off";
          this._onShowGridChanged();

          // grid color picker

          var gridCol = (this._gridProps.majorHLine && this._gridProps.majorHLine.color) ? this._gridProps.majorHLine.color
              : "#ededed";
          this.gridColorPicker.value = gridCol;

          // title, legends
          this.chartTitleElem.value = (this._datasheet.control && this._datasheet.control.props) ? this._datasheet.control.props.title
              : "";
          this.xLegendElem.value = (this._datasheet.control && this._datasheet.control.props && this._datasheet.control.props.hAxis) ? this._datasheet.control.props.hAxis.title
              : "";

          this.yLegendElem.value = (this._datasheet.control && this._datasheet.control.props && this._datasheet.control.props.vAxis) ? this._datasheet.control.props.vAxis.title
              : "";

          // color axis
          var _tempColorAxis = [];
          // we need to reset the color axis because "_onChartTypeChange" gets called first and it
          // populates the color axis with
          // initial "all" x axis values
          this._resetColorAxis();
          // the second x-axis is always the color axis. So store it right away if you have 2 x axes
          if (this._datasheet.axes && this._datasheet.axes.length > 1)
          {
            this._secondaryXAxis.push(this._datasheet.axes[1]);
            _tempColorAxis = JSON.parse(JSON.stringify(this._secondaryXAxis));

          }
          // x-axis

          // populate the x Axis. The chartDef.xAxis holds both x axis and color axis. We need
          // to subtract the color axis and get only the primary xAxis

          var xaxisInfo = {};
          xaxisInfo.type = "x";
          if (this._datasheet.axes && this._datasheet.axes.length > 0)
          {
            for (var k = 0; k < this._datasheet.axes.length; k++)
            {
              var firstAxis = this._datasheet.axes[k];

              if (_tempColorAxis.length == 0)
              {
                xaxisInfo.axis = firstAxis;
                this._onAddAxis(xaxisInfo);
                continue;
              }

              for (var x = 0; x < _tempColorAxis.length; x++)
              {
                var secondAxis = _tempColorAxis[x];
                if (firstAxis.id != secondAxis.id)
                {
                  xaxisInfo.axis = firstAxis;
                  this._onAddAxis(xaxisInfo);
                }
              }

            }
          }

          // color axis
          var _tempYAxis = [];
          // we need to reset the color axis because "_onChartTypeChange" gets called first and it
          // populates the color axis with
          // initial "all" x axis values
          this._resetSecondaryYAxis();

          if (this._datasheet.dataPoints && this._datasheet.dataPoints.length > 0)
          {
            for (var q = 0; q < this._datasheet.dataPoints.length; q++)
            {
              var dpItem = this._datasheet.dataPoints[q];
              if (dpItem.props)
              {
                var secProp = dpItem.props["renderAsSecondary"];
                if (secProp)
                {
                  this._secondaryYAxis.push(dpItem);
                }
              }
            }
            _tempYAxis = JSON.parse(JSON.stringify(this._secondaryYAxis));
          }

          // populate y-axis
          var yaxisInfo = {};
          yaxisInfo.type = "y";
          if (this._datasheet.dataPoints && this._datasheet.dataPoints.length > 0)
          {
            for (var j = 0; j < this._datasheet.dataPoints.length; j++)
            {
              var firstYAxis = this._datasheet.dataPoints[j];

              if (_tempYAxis.length == 0)
              {
                yaxisInfo.axis = firstYAxis;
                this._onAddAxis(yaxisInfo);
                continue;
              }

              for (var x = 0; x < _tempYAxis.length; x++)
              {
                var secondYAxis = _tempYAxis[x];
                if (firstYAxis.id != secondYAxis.id)
                {
                  yaxisInfo.axis = firstYAxis;
                  this._onAddAxis(yaxisInfo);
                }
              }

            }

          }

          // populate color axis after both primary axis are populated
          if (_tempColorAxis.length != 0)
          {
            this.secondXaxisDD.value = _tempColorAxis[0].id;
          }

          // populate secondary y axis after both primary axis are populated
          if (_tempYAxis.length != 0)
          {
            this.secondYaxisDD.value = _tempYAxis[0].id;
          }
          // set the x axis properties
          /*
           * if(this._xAxisProps) {
           * 
           * this.xAxisStrokeColorPicker.value = this._xAxisProps.stroke;
           * this.xAxisFontColorPicker.value = this._xAxisProps.fontColor; }
           */

          // set the specific properties of geoChart
          if (type.toLowerCase() === Res.geo.toLowerCase())
          {
            // set the display mode of the geo chart if user has selected any.
            this.geoDisplayMode.value = (this._datasheet.control && this._datasheet.control.props) ? this._datasheet.control.props.displayMode
                : "auto";
            
            //set the region type. First fetch the broader reqion, populate the drop downs for country/state/continent
            // and then set the value for user to see
            if(this._datasheet.control.props && this._datasheet.control.props.regionType)
            {
              this.region_type.value = this._datasheet.control.props.regionType || "world";
              this._onRegionTypeChange(this._setRegionOnLoadOfDatasheet);
            }
          }
        },

        _setRegionOnLoadOfDatasheet: function()
        {
          this.region_val_dd.value = this.datasheet.control.props.region || "none";
          this.regionVal.innerHTML = this.region_val_dd.options[this.region_val_dd.selectedIndex].innerText;
          this.regionVal.setAttribute("data-qcd-region-val", this.region_val_dd.value);
          //after setting the reqion render the graph since the regions is not set at  the load time earler
          this._renderGraph();
        },
        
        /**
         * Open a dialog box to help select a new type of chart to render
         */
        _selectNewChartType: function()
        {
          var pObj = {
            _parent: this,
            _showInline: true
          };
          if (this._control && this._control.chartType)
          {
            pObj.chartType = this._control.chartType;
          }
          this.qChartTypeSelector = new QChartTypeSelector(pObj);
          this.qChartTypeSelector.startup();
          dojo.place(this.qChartTypeSelector.domNode, this.chartSelector);
          this.own(this.qChartTypeSelector);
        },

        /**
         * Change the chart type selection once user has selected the new type, change the icon on
         * UI Called from chart type selector dialog.
         */
        _onChartTypeChange: function(chartType)
        {
          // before setting chart type check if the previous chart type was bubble.
          // in that case clear all axis selections and the canvas then proceed to set the new chart
          if (this._getChartType() && this._getChartType() != ""
            && this._getChartType().toLowerCase() === Res.bubble.toLowerCase())
          {
            this._clearAxesSelection();
            this._clearCanvas();
          }

          // proceed to set the chart type now
          this._setChartType(chartType);
          // call the decorator class to handle the show hide of chart properties
          // and to render the graph based on the axes
          this.cDecorator = ChartTypeDecoratorFactory.createInstance({
            "chartType": chartType,
            "designer": this
          });
          this.cDecorator.decorate();
          this.cDecorator.canRender();
          // this._disableChartSelections(cDecorator._disableMultiChartType(),
          // ChartTypeDecoratorFactory.getListOfMultiXChart());

        },

        /**
         * Sets the new chart type as selected by the user
         */
        _setChartType: function(chartType)
        {
          this._control.chartType = chartType;
        },

        _getChartType: function()
        {
          return this._control.chartType;
        },

        /**
         * Toggle hiding/viewing certain properties and controls on the Chart UI based on the chart
         * type selections (given chart is of single X or multi X renderer)
         */
        _hideChartProps: function(isSingleXChart)
        {
          domClass.remove(this.showGridDiv, "c3_hide");
          domClass.remove(this.chartTitleDiv, "c3_hide");
          // domClass.remove(this.xAxisStrokeColorPickerDiv, "c3_hide");
          // domClass.remove(this.xAxisFontColorPickerDiv, "c3_hide");
          domClass.remove(this.xAxisTitleDiv, "c3_hide");
          domClass.remove(this.yAxisTitleDiv, "c3_hide");
          domClass.remove(this.yLegendElem, "c3_hide");
          domClass.remove(this.xLegendElem, "c3_hide");
          domClass.add(this.geoDisplayModeDiv, "c3_hide");
          domClass.add(this.geoRegionDiv, "c3_hide");
          this.secondYAxisLabel.innerHTML = Res.secondYAxis;
          if (this._yAxis.length == 2)
          {
            domClass.add(this.addYaxisBtn, "c3_hide");
          }
          else
          {
            domClass.remove(this.addYaxisBtn, "c3_hide");
          }
          domClass.remove(this.secondYAxisDiv, "c3_hide");
          this._populateSecondaryYAxis();
          if (isSingleXChart)
          {
            domClass.add(this.colorAxisDiv, "c3_hide");
            // whenever the color axis is hidden, reset thecolor axis data too, so that it is not
            // preserved
            this._resetColorAxis();
          }
          else
          {
            domClass.remove(this.colorAxisDiv, "c3_hide");
            this._populateSecondaryXAxis();
          }

        },

        /**
         * Additional hiding/viewing certain properties and controls on the Chart UI based on the
         * chart type selections (given chart is bubble chart)
         */
        _hideBubbleChartProps: function()
        {
          domClass.add(this.showGridDiv, "c3_hide");
          domClass.remove(this.secondYAxisDiv, "c3_hide");
          domClass.remove(this.colorAxisDiv, "c3_hide");
          domClass.remove(this.secondYAxisDiv, "c3_hide");
          domClass.add(this.geoDisplayModeDiv, "c3_hide");
          domClass.add(this.geoRegionDiv, "c3_hide");

          this.secondYAxisLabel.innerHTML = Res.sizeAxis;
          // whenever the secondary y axis is hidden, reset the secondary y axis data too, so that
          // it is not preserved
          this._resetSecondaryYAxis();

        },

        /**
         * Additional hiding/viewing certain properties and controls on the Chart UI based on the
         * chart type selections (given chart is table chart)
         */
        _hideTableChartProps: function()
        {
          domClass.add(this.chartTitleDiv, "c3_hide");
          domClass.add(this.showGridDiv, "c3_hide");
          domClass.add(this.xAxisTitleDiv, "c3_hide");
          domClass.add(this.yAxisTitleDiv, "c3_hide");
          domClass.add(this.geoDisplayModeDiv, "c3_hide");
          domClass.add(this.geoRegionDiv, "c3_hide");
        },

        /**
         * Additional hiding/viewing certain properties and controls on the Chart UI based on the
         * chart type selections (given chart is pie chart / geo chart)
         */
        _hidePieChartProps: function(isGeoChart)
        {
          domClass.add(this.showGridDiv, "c3_hide");
          domClass.add(this.xAxisTitleDiv, "c3_hide");
          domClass.add(this.yAxisTitleDiv, "c3_hide");
          this.secondYAxisLabel.innerHTML = Res.secondYAxis;
          // domClass.add(this.xAxisFontColorPickerDiv, "c3_hide");
          if (this._yAxis.length >= 1)
          {
            domClass.add(this.addYaxisBtn, "c3_hide");
          }
          domClass.add(this.secondYAxisDiv, "c3_hide");
          // whenever the secondary y axis is hidden, reset the secondary y axis data too, so that
          // it is not preserved
          this._resetSecondaryYAxis();

        },

        _hideGeoChartProps: function()
        {
          domClass.add(this.showGridDiv, "c3_hide");
          domClass.add(this.xAxisTitleDiv, "c3_hide");
          domClass.add(this.yAxisTitleDiv, "c3_hide");
          domClass.add(this.chartTitleDiv, "c3_hide");
          this.secondYAxisLabel.innerHTML = Res.sizeAxis;
          domClass.remove(this.geoDisplayModeDiv, "c3_hide");

          domClass.remove(this.geoRegionDiv, "c3_hide");
          domClass.remove(this.secondYAxisDiv, "c3_hide");

          if (this._yAxis.length >= 1)
          {
            domClass.add(this.addYaxisBtn, "c3_hide");
          }
          // whenever the secondary y axis is hidden, reset the secondary y axis data too, so that
          // it is not preserved
          this._resetSecondaryYAxis();
        },

        _disableChartSelections: function(shouldDisableCharts, chartTypes)
        {
          // if the flag to draw single X chart is true
          if (isSingleXChartType)
          {
            // if the chart selector widget exists
            if (this.qChartTypeSelector)
            {
              // disable multiX charts
              this.qChartTypeSelector.disableMultiCharts(true, chartTypes);
            }
          }
        },

        _clearCanvas: function()
        {
          if (this._getChartType() && this._getChartType() != ""
            && this._getChartType().toLowerCase() === Res.bubble.toLowerCase())
          {
            this.chartHolderDiv.innerHTML = "";
          }
        },

        _clearAxesSelection: function()
        {
          if (this._getChartType() && this._getChartType() != ""
            && this._getChartType().toLowerCase() === Res.bubble.toLowerCase())
          {

            // clear the UI. clear the previous axes selections if any
            this.xAxisVals.innerHTML = "";
            this.yAxisVals.innerHTML = "";

            // show the Add Axis buttons to do the next round of selections for bubble chart
            domClass.remove(this.addXaxisBtn, "c3_hide");
            domClass.remove(this.addYaxisBtn, "c3_hide");

            // reset all the axes arrays and cleanup
            this._resetPrimaryAxes();
            this._resetColorAxis();
            this._resetSecondaryYAxis();
          }
        },

        _resetPrimaryAxes: function()
        {
          this._xAxis = [];
          this._yAxis = [];
        },

        _resetColorAxis: function()
        {
          this._secondaryXAxis = [];
        },

        _resetSecondaryYAxis: function()
        {
          this._secondaryYAxis = [];
        },

        /**
         * Opens a dialog to show the axis( X or Y) to be added based on user's selection
         */
        _selectAxisToAdd: function(axisType)
        {

          var addAxisWidget = new AddAxis({
            type: axisType,
            axis: JSON.parse(JSON.stringify(this._qData.axis)),
            chartType: this._getChartType(),
            parent: this
          });

          this._addAxisDialog = new Dialog({
            title: Res.dialog_title,
            style: "min-width: 500px;min-height:300px;",
          });
          dojo.place(addAxisWidget.domNode, this._addAxisDialog.containerNode);

          this._addAxisDialog.show();
        },

        /**
         * CB function when the user has chosen the axis to be added for rendering. Close the
         * dialog, and then based on the following rule decide to show the "+" button to add more
         * axis For X- axis - Allow only 1 X-axis to be added. For Y-Axis - Allow only 2 Y-axis to
         * be added.
         * 
         * @param axisInfo -
         *          the axis selected for adding
         */
        _onAddAxis: function(axisInfo)
        {
          if (this._addAxisDialog) this._addAxisDialog.hide();
          if (axisInfo)
          {
            if (axisInfo.type && axisInfo.type.toLowerCase() === this.X_AXIS)
            {
              this._xAxis.push(axisInfo.axis);
              this._showSelectedAxis(this.xAxisVals, axisInfo);
              if (this._xAxis.length > 0)
              {
                domClass.add(this.addXaxisBtn, "c3_hide");
              }
              // update the color values axis options
              this._populateSecondaryXAxis();
            }
            else
            {
              this._yAxis.push(axisInfo.axis);
              this._showSelectedAxis(this.yAxisVals, axisInfo);
              if (this._getChartType() && this._getChartType() != ""
                && this._getChartType().toLowerCase() === Res.geo.toLowerCase())
              {
                if (this._yAxis.length > 0)
                {
                  domClass.add(this.addYaxisBtn, "c3_hide");
                }
              }
              else
              {
                if (this._yAxis.length > 1)
                {
                  domClass.add(this.addYaxisBtn, "c3_hide");
                }
              }

              // update the secondary y axis options
              this._populateSecondaryYAxis();
            }

          }

        },

        /**
         * Show the selected axis on the Designer UI. Based on whether X or Y has been selected,
         * show the axis under the right node.
         * 
         * @param parentNode -
         * @param axisInfo
         */
        _showSelectedAxis: function(parentNode, axisInfo)
        {
          if (parentNode)
          {
            var aI = {};
            aI.axisInfo = JSON.parse(JSON.stringify(axisInfo));
            var axis = aI.axisInfo.axis;

            var id = "_" + axis.id + "_";
            var tagDiv = this._createElement("div", parentNode, {
              "id": id
            }, "c3_qcd_tag props_parent");
            var axisNameDiv = this._createElement("div", tagDiv, {
              "innerHTML": axis.label
            }, "c3_qcd_tagName col1 c3_axis_label axis_header_left");
            var props = axis.props || null;
            if (props)
            {
              var showAs = (props.showAs) ? props.showAs : "";
              var format = (props.format) ? props.format : "";
              var sort = (props.sort) ? props.sort : "";
              var showAsDiv = null;
              if (showAs != "")
              {
                var txt = "Show As " + showAs;
                if(sort)
                {
                	showAsDiv = this._createElement("div", tagDiv, {
                        "id": id + "showAs",
                        "data-axisType": showAs,
                        "data-axisFormat": format,
                        "data-axisSort": sort.toString(),
                        "innerHTML": txt
                      }, "c3_qcd_tagShowAs");
                }
                else
                {
                	showAsDiv = this._createElement("div", tagDiv, {
                        "id": id + "showAs",
                        "data-axisType": showAs,
                        "data-axisFormat": format,
                        "innerHTML": txt
                      }, "c3_qcd_tagShowAs");
                }
                
              }
            }
            else
            {
              showAsDiv = this._createElement("div", tagDiv, {
                "id": id + "showAs"
              }, "c3_qcd_tagShowAs c3_hide");
            }

            var dataTypeDiv = this._createElement("div", tagDiv, {},
              "c3_qcd_tagValue c3_btn btn-default c3_btn_fa_no_border");
            var typeIcon = this._createElement("i", dataTypeDiv, {}, "ti-angle-right");
            this.own(on(typeIcon, "click", lang.hitch(this, function()
            {
              this._onAddAxisDetails(aI);

            })));
          }
        },

        _hideChartScreen: function()
        {
          domClass.add(this.xAxisDiv, "c3_hide");
          domClass.add(this.yAxisDiv, "c3_hide");
          domClass.add(this.colorAxisDiv, "c3_hide");
          domClass.add(this.secondYAxisDiv, "c3_hide");
          domClass.add(this.showGridDiv, "c3_hide");
          domClass.add(this.chartTitleDiv, "c3_hide");
          domClass.add(this.showGridDiv, "c3_hide");
          domClass.add(this.xAxisTitleDiv, "c3_hide");
          domClass.add(this.yAxisTitleDiv, "c3_hide");
          domClass.add(this.geoDisplayModeDiv, "c3_hide");
          domClass.add(this.geoRegionDiv, "c3_hide");

        },

        /**
         * CB function to show the axis detail window and hide the properties window
         */
        _onAddAxisDetails: function(aI)
        {
          var axisInfo = null;
          this._hideChartScreen();
          domClass.remove(this.axisSelection, "c3_hide");

          if (aI)
          {
            axisInfo = (aI.axisInfo) ? aI.axisInfo.axis : {};
            // update the axis title of the axis being modified
            var lbl = axisInfo.label;
            this.axis_sel_title.innerHTML = lbl;
            var type = aI.axisInfo.type;
            if (type && type === this.X_AXIS)
            {
          	  domClass.remove(this.axis_sort_row, "c3_hide");
            }
            else 
            {
          	  domClass.add(this.axis_sort_row, "c3_hide");
            }
            // set custom params to help during update and editing
            this.axis_sel_title.setAttribute("data-axis-type", type);
            this.axis_sel_title.setAttribute("data-axis-id", axisInfo.id);

            // set default values for the dropdowns based on axis selection
            if (type && type === this.X_AXIS)
            {
              this.axis_type.value = "string";
            }
            else
            {
              this.axis_type.value = "number";
            }
            // set the default axis type selection and the formatter drop downs
            this._onFilterChartFormatter();

            // retrieve the axis type info from the dom node using the special attributes used to
            // save it
            // e.g. data-axisType and data-axisFormat
            var id = "_" + axisInfo.id + "_" + "showAs";
            var elem = document.getElementById(id);
            if (elem)
            {
              var type = elem.getAttribute("data-axisType");
              var format = elem.getAttribute("data-axisFormat");
              if (type && type != "")
              {
                this.axis_type.value = type;
              }
              if(elem.getAttribute("data-axisSort"))
        	  {
            	  var sortVal = elem.getAttribute("data-axisSort") || false;
            	  if (sortVal.toLowerCase() === "true")
	      	      {
	      	        this.sortToggle.setAttribute("data-toggle", "on");
	      	        this._toggleDisplayMode(this.sortToggle, true);
	      	      }
	      	      else
	      	      {
	      	        this.sortToggle.setAttribute("data-toggle", "off");
	      	        this._toggleDisplayMode(this.sortToggle, false);
	      	      }
        	  }
              // reset the axis type drop down and its corresponding formatter dropdown based on the
              // saved axis type value
              this._onFilterChartFormatter();
              this.axis_format.value = "none";
              // set the formatter drop down's saved value
              if (format && format != "")
              {
                this.axis_format.value = format;
              }
            }

          }

        },

        /**
         * CB function to go back to the properties window and hide the axis detail window
         */
        goBack: function()
        {
          domClass.remove(this.xAxisDiv, "c3_hide");
          domClass.remove(this.yAxisDiv, "c3_hide");
          domClass.remove(this.colorAxisDiv, "c3_hide");
          domClass.remove(this.secondYAxisDiv, "c3_hide");
          domClass.remove(this.showGridDiv, "c3_hide");
          domClass.remove(this.chartTitleDiv, "c3_hide");
          domClass.remove(this.showGridDiv, "c3_hide");
          domClass.remove(this.xAxisTitleDiv, "c3_hide");
          domClass.remove(this.yAxisTitleDiv, "c3_hide");
          domClass.add(this.axisSelection, "c3_hide");
          if (this.cDecorator)
          {
            this.cDecorator.decorate();
          }

        },

        /**
         * CB function to handle axis details update for datatype and formatter changes
         */
        _onAxisDetailSave: function()
        {
          var type = this.axis_sel_title.getAttribute("data-axis-type");
          var axisId = this.axis_sel_title.getAttribute("data-axis-id");
          //for now we can only sort X Axis values which for most graphs is restricted to 1 value
          var canSort = true;
          if (type && type === this.X_AXIS)
          {
            this._updateAxis(this._xAxis, axisId, canSort);
            this._updateSelectedAxisUI(axisId, canSort);
          }
          else
          {
            this._updateAxis(this._yAxis, axisId, !canSort);
            this._updateSelectedAxisUI(axisId, !canSort);
          }
          // this._updateControl();
          

          this.goBack();
          this._hideAxisDetailsScreen();
          this._hideRegionDetailsScreen();
        },

        _updateAxis: function(axis, id, canSort)
        {
          if (axis.length == 0) return;
          for (var i = 0; i < axis.length; i++)
          {
            if (axis[i].id.toLowerCase() === id.toLowerCase())
            {
              axis[i].props = axis[i].props || {};
              axis[i].props["showAs"] = this.axis_type.value.toLowerCase();
              axis[i].props["format"] = this.axis_format.value;
              if(canSort)
              {
            	var sortVal = this.sortToggle.getAttribute("data-toggle");
                axis[i].props["sort"] = (sortVal.toLowerCase() === "off") ? false: true ; 
              }
            }
          }

        },

        _updateSelectedAxisUI: function(axisId, canSort)
        {
          var id = "_" + axisId + "_" + "showAs";
          var elem = document.getElementById(id);
          elem.setAttribute("data-axisType", this.axis_type.value);
          elem.setAttribute("data-axisFormat", this.axis_format.value);
          if(canSort)
          {
        	  var sortVal = (this.sortToggle.getAttribute("data-toggle").toLowerCase() === "off") ? false: true ;
              elem.setAttribute("data-axisSort", sortVal);
          }
          
          elem.innerHTML = "Show As " + this.axis_type.value;
          domClass.remove(elem, "c3_hide");
        },

        /**
         * CB function to handle axis deletion
         */
        _onDeleteAxis: function()
        {
          var type = this.axis_sel_title.getAttribute("data-axis-type");
          var axisId = this.axis_sel_title.getAttribute("data-axis-id");
          if (axisId)
          {
            // remove the dom Node
            var id = "_" + axisId + "_";
            var elem = document.getElementById(id);
            elem.parentNode.removeChild(elem);

            if (type && type === this.X_AXIS)
            {
              this._removeFromAxis(this._xAxis, axisId);
              if (this._xAxis.length === 0)
              {
                domClass.remove(this.addXaxisBtn, "c3_hide");
              }
              // update the secondary axis options
              this._populateSecondaryXAxis();
            }
            else
            {
              this._removeFromAxis(this._yAxis, axisId);
              if (this._yAxis.length < 2)
              {
                domClass.remove(this.addYaxisBtn, "c3_hide");
              }
              // update the secondary axis options
              this._populateSecondaryYAxis();
            }
            this.goBack();
            this._hideAxisDetailsScreen();

          }
        },

        /**
         * Delete the axis from the stored array
         */
        _removeFromAxis: function(axis, id)
        {
          if (axis.length == 0) return;
          for (var i = 0; i < axis.length; i++)
          {
            if (axis[i].id.toLowerCase() === id.toLowerCase())
            {
              axis.splice(i, 1);
            }
          }

        },

        _showRegionOptions: function()
        {
          this._hideChartScreen();
          this._hideAxisDetailsScreen();

          domClass.remove(this.regionSelection, "c3_hide");
        },

        _hideAxisDetailsScreen: function()
        {
          domClass.add(this.axisSelection, "c3_hide");
        },

        _hideRegionDetailsScreen: function()
        {
          domClass.add(this.regionSelection, "c3_hide");
        },

        _onRegionTypeChange: function(cb)
        {
          switch (this.region_type.value.toLowerCase())
          {
            case Res.world.toLowerCase():
              domClass.add(this.region_dd_div, "c3_hide");
              this.region_val_dd.innerHTML = "";
            break;
            case Res.continentVal.toLowerCase():
            case Res.country.toLowerCase():
            case Res.state.toLowerCase():
              this._fetchRegionSelection(this.region_type.value, cb);
              // domClass.remove(this.region_country_div, "c3_hide");
            break;
          }
        },

        _fetchRegionSelection: function(regType, cb)
        {
          if (regType == "") return;
          
          var region = regType.toLowerCase();
          
          var data = [];
          switch(region)
          {
            case "continent":
            {
              if(continentJson)
              {
                data = typeof continentJson == 'string' ? JSON.parse(continentJson) : continentJson;
              }
              break;
            }
            case "country":
            {
              if(countryJson)
              {
                data = typeof countryJson == 'string' ? JSON.parse(countryJson) : countryJson;
              }
              break;
            }
            case "state":
            {
              if(stateJson)
              {
                data = typeof stateJson == 'string' ? JSON.parse(stateJson) : stateJson;
              }
              break;
            }
            default:
              break;
          }
          
          this._populateRegionCombo(data, regType, cb);
        },

        _populateRegionCombo: function(data, regType, cb)
        {
          if (!data || data.length === 0) return;
          domClass.remove(this.region_dd_div, "c3_hide");
          this.region_val_dd.innerHTML = "";
          var optionStr = "<option selected value='none'>Select a " + regType + "</option>";
          for (var i = 0; i < data.length; i++)
          {
            var item = data[i];
            optionStr += "<option value=" + item.code + ">" + item.name + "</option>";
          }
          this.region_val_dd.innerHTML = optionStr;
          if(cb) cb.call(this);
        },

        _onRegionDetailSave: function()
        {
          switch (this.region_type.value.toLowerCase())
          {
            case Res.world.toLowerCase():
              this.regionVal.innerHTML = Res.world;
              this.regionVal.setAttribute("data-qcd-region-val", Res.world.toLowerCase());
              this.goBack();
              this._hideAxisDetailsScreen();
              this._hideRegionDetailsScreen();
            break;
            case Res.continentVal.toLowerCase():
            case Res.country.toLowerCase():
            case Res.state.toLowerCase():
              if (this.region_val_dd.value == "" || this.region_val_dd.value == "none")
              {
                udc.error("Please select a region from the dropdown.");
                return;
              }
              this.regionVal.innerHTML = this.region_val_dd.options[this.region_val_dd.selectedIndex].innerText;
              this.regionVal.setAttribute("data-qcd-region-val", this.region_val_dd.value);
              this.goBack();
              this._hideAxisDetailsScreen();
              this._hideRegionDetailsScreen();
            break;
          }
        },

        /**
         * Once the chart type is selected, look it up in the properties and find the type's
         * specific props for rendering the control in the chart
         */
        _getChartProps: function()
        {
          var chartProp = {};
          for (var i = 0; i < this._allProps.length; i++)
          {
            var chart = this._allProps[i];
            if (chart.type && chart.type.toLowerCase() === this._getChartType().toLowerCase())
            {
              chartProp = chart.props;
              break;
            }
          }
          return chartProp;
        },

        /**
         * This is the color axis for want of a better word. It uses the left over axis values from
         * X Axis. Its used to determine the color shade or range based on the values variations
         * found in the xaxis
         */
        _populateSecondaryXAxis: function()
        {
          // the secondary axis should show only values which are not selected in
          // the primary x axis so filter the axis
          this._filterSecondaryAxisValues();
          var html = "<option value='' selected>" + Res.selectLbl + "</option>";
          // populate the secondAxis dropdown with the filtered array
          for (var i = 0; i < this._secondaryXAxis.length; i++)
          {
            var axis = this._secondaryXAxis[i];
            html += "<option value=" + axis.id + ">" + axis.label + "</option>";
          }
          this.secondXaxisDD.innerHTML = html;
        },

        /**
         * Difference function for axis. (allNumericValues - X Axis values)
         */
        _filterSecondaryAxisValues: function()
        {
          this._secondaryXAxis = [];
          for (var i = 0; i < this._qData.axis.length; i++)
          {
            var axisItem = JSON.parse(JSON.stringify(this._qData.axis[i]));
            if (axisItem.datatype && axisItem.datatype != ""
              && axisItem.datatype.toLowerCase() != this.NUMBER)
            {
              if (this._xAxis.indexOf(axisItem) === -1)
              {
                this._secondaryXAxis.push(axisItem);
              }
            }

          }
          return this._secondaryXAxis;
        },

        /**
         * This is the color axis for want of a better word. It uses the left over axis values from
         * X Axis. Its used to determine the color shade or range based on the values variations
         * found in the xaxis
         */
        _populateSecondaryYAxis: function()
        {
          // the secondary axis should show only values which are not selected in
          // the primary x axis so filter the axis
          this._filterSecondaryYAxisValues();
          var html = "<option value='' selected>" + Res.selectLbl + "</option>";
          // populate the secondAxis dropdown with the filtered array

          for (var i = 0; i < this._secondaryYAxis.length; i++)
          {
            var axis = this._secondaryYAxis[i];
            html += "<option value=" + axis.id + ">" + axis.label + "</option>";
          }
          this.secondYaxisDD.innerHTML = html;
        },

        /**
         * Difference function for axis. (allNumericValues - X Axis values)
         */
        _filterSecondaryYAxisValues: function()
        {
          this._secondaryYAxis = [];
          for (var i = 0; i < this._qData.axis.length; i++)
          {
            var axisItem = JSON.parse(JSON.stringify(this._qData.axis[i]));
            if (axisItem.datatype && axisItem.datatype != ""
              && axisItem.datatype.toLowerCase() == this.NUMBER)
            {
              if (this._yAxis.indexOf(axisItem) === -1)
              {
                this._secondaryYAxis.push(axisItem);
              }
            }

          }
          return this._secondaryYAxis;
        },

        /**
         * When the user switches on/ off the grid selection. Update the default grid props
         * accordingly
         */
        _onShowGridChanged: function()
        {
          // this._showGridFlag = this.showGridDD.value;
          this._showGridFlag = this.showGridDD.getAttribute("data-toggle");
          if (this._showGridFlag.toLowerCase() === "on")
          {
            this._toggleGridColorPicker(true);
            // this._gridProps.display = true;
          }
          else
          {
            this._toggleGridColorPicker(false);
            // this._gridProps.display = false;
          }
        },

        /**
         * Hide and show the grid color picker based on whether to hide or show grid
         */
        _toggleGridColorPicker: function(flag)
        {
          if (flag)
          {
            // domClass.remove(this.gridColorDiv, "c3_hide");
          }
          else
          {
            domClass.add(this.gridColorDiv, "c3_hide");
          }
        },

        /**
         * Update the props based on the color selected for grid
         */
        _onGridColorChanged: function()
        {
          var val = this.gridColorPicker.value;
          this._gridProps.majorHLine.color = val;
        },

        /**
         * Updtes xAxis props for axis stroke color change
         */
        _onXAxisStrokeColorChanged: function()
        {
          var val = this.xAxisStrokeColorPicker.value;
          this._xAxisProps.stroke = val;
        },

        /**
         * Updtes xAxis props for axis font color change
         */
        _onXAxisFontColorChanged: function()
        {
          var val = this.xAxisFontColorPicker.value;
          this._xAxisProps.fontColor = val;
        },

        _createDatasheet: function()
        {
          var ds = {};

          // set the chart title and axes legends
          var title = (this.chartTitleElem && this.chartTitleElem.value != "") ? this.chartTitleElem.value
              : "";

          this._updateChartControl();

          // control info
          ds.control = this._createControl(title);

          // x axis
          // get the color axis value from the selected dd and push it to the xAxis array
          var xA = this.cDecorator.getXAxisValues();
          ds.axes = this._constructAxisForDS(xA, "x", ds.control);

          // get the secondary y axis value from the selected dd and push it to the yAxis array
          var yA = this.cDecorator.getYAxisValues();
          // y axis
          ds.dataPoints = this._constructAxisForDS(yA, "y", ds.control);

          // query info
          ds.exec = {};
          ds.exec.uri = "c3/data/query/exec?qId=" + this.queryId;

          // misc fields
          ds.description = this._chartDesc;
          ds.filters = [];

          return ds;
        },

        _createControl: function(chartTitle)
        {
          var ctrl = {};
          ctrl.chartType = this._getChartType();
          ctrl.props = {};

          if (this._control.props && Object.keys(this._control.props).length > 0)
          {
            ctrl.props = this._control.props;

          }
          // add the chart title here as chartDef does not need that for rendering/saving but DS
          // needs
          // at
          // the time of save to render in the dashboard
          ctrl.props.title = chartTitle;

          ctrl.props.height = "500";// this.chartHolderDiv.

          if (this._showGridFlag.toLowerCase() != "on")
          {
            ctrl.props.vAxis = {
              "gridlines": {
                "color": "none"
              }
            };
          }
          else
          {
            ctrl.props.vAxis = {
              "gridlines": {
                "color": "#CCC"
              }
            };
          }
          // specific properties by chart types
          if (ctrl.chartType.toLowerCase() === Res.geo.toLowerCase())
          {
            ctrl.props["displayMode"] = this.geoDisplayMode.value || "auto";
            ctrl.props["region"] = this.region_val_dd.value || "world";
            ctrl.props["regionType"] = this.region_type.value || "world";
          }
          // ctrl.grid = this._control.grid;

          return ctrl;
        },

        _constructAxisForDS: function(axisInfo, axisType, control)
        {
          var axisArr = [];
          if (axisInfo && axisInfo.length > 0)
          {
            for (var i = 0; i < axisInfo.length; i++)
            {
              var axis = {};
              axis.id = axisInfo[i].id;
              axis.src = axisInfo[i].src;
              axis.label = axisInfo[i].label;
              // for the first element of the axis set certain properties and labels
              if (i == 0)
              {
                var prop = "";

                // first elem of x/y axis, store the axis properties and grab the label from the
                // legend if provided
                if (axisType.toLowerCase() === "y")
                {
                  if (control && control.props)
                  {
                    control.props.vAxis = control.props.vAxis || {};
                  }
                  control.props["vAxis"]["title"] = (this.yLegendElem && this.yLegendElem.value != "") ? this.yLegendElem.value
                      : axisInfo[i].label;

                  var yProp = axisInfo[i].props || null;
                  if (yProp)
                  {
                    if (yProp.format)
                    {
                      control.props["vAxis"]["format"] = control.props["vAxis"]["format"] || {};
                      control.props["vAxis"]["format"] = yProp.format;
                    }

                  }

                }

                else
                {
                  if (control && control.props)
                  {
                    control.props.hAxis = control.props.hAxis || {};
                  }
                  control.props["hAxis"]["title"] = (this.xLegendElem && this.xLegendElem.value != "") ? this.xLegendElem.value
                      : axisInfo[i].label;

                  var xProp = axisInfo[i].props || null;
                  if (xProp)
                  {
                    if (xProp.format)
                    {
                      control.props["hAxis"]["format"] = control.props["hAxis"]["format"] || {};
                      control.props["hAxis"]["format"] = xProp.format;
                    }

                  }
                }

                axis.props = (axisInfo[i].props) ? JSON.parse(JSON.stringify(axisInfo[i].props))
                    : prop;
              }
              // for the rest of the elements see if properties exist. The label would be the same
              // as id/ name of the axis
              else
              {
                axis.props = (axisInfo[i].props) ? JSON.parse(JSON.stringify(axisInfo[i].props))
                    : {};
              }

              // y axis has to have a special attribute called method for the ds to render, add that
              if (axisType.toLowerCase() === "y")
              {
                axis.method = "udc.system.aggregation:sum";
              }

              // formatting date fields on x axis. Based on their id, provide an appropriate
              // formatter
              if (axisType.toLowerCase() === "x")
              {
                if (control && control.props)
                {
                  control.props.hAxis = control.props.hAxis || {};
                }

              }
              axisArr.push(axis);
            }
          }

          return axisArr;
        },

        /**
         * Creates the graph to render based on the selections made by the user. Uses query chart
         * renderer helper to create the graph
         */
        _renderGraph: function()
        {
          this.chartHolderDiv.innerHTML = "";

          this._datasheet = this._createDatasheet();

          // validate the axes inputs and if they match with the chart type then render graph. else
          // throw an error and ask user to correct the inputs
          if (!this._isChartValid()) return;

          var ds = {
            "QueryChartSheet": this._datasheet
          };

          this._chart = new ChartRenderer({
            name: this._dashboardName,
            datasheet: ds,
            node: this.chartHolderDiv,
            resource: null,
            timeFilterFreq: null,
            timeFilterRange: null

          });
          this._chart.startup();

        },

        _isChartValid: function()
        {
          var retVal = true;
          switch (this._datasheet.control.chartType)
          {
	        case Res.stackedBars:
	        case Res.clusteredBars:
	        case Res.stackedColumns:
	        case Res.clusteredColumns:
	        case Res.stackedAreas:
	        case Res.stackedLines:
	        	if(this._datasheet.axes && this._datasheet.axes.length === 1)
	        	{
	        		udc.alert(Res.missingColorAxisForClusteredCharts);
	                retVal = false;
	        	}
            case Res.pie:
            case Res.scatter:
            case Res.stackedBars:
            case Res.clusteredBars:
            case Res.bars:
              if (this._datasheet.dataPoints && this._datasheet.dataPoints.length > 1)
              {
                udc.alert(Res.invalidSingleYAxisInput);
                retVal = false;
              }
            break;
            case Res.bubble:
              if (this._datasheet.dataPoints && this._datasheet.dataPoints.length < 2
                && this._datasheet.axes && this._datasheet.axes.length < 1)
              {
                udc.alert(Res.invalidBubbleChartInput);
                retVal = false;
              }
            break;
          }
          return retVal;
        },

        /**
         * Find the selected option from the secondary X Axis and return the axis object to render
         * the chart
         */
        _getSelectedColorAxis: function()
        {
          var selVal = this.secondXaxisDD.value;
          if (!selVal || selVal === "") return null;

          for (var i = 0; i < this._secondaryXAxis.length; i++)
          {
            var axis = this._secondaryXAxis[i];
            if (selVal.toLowerCase() === axis.id.toLowerCase())
            {
              return axis;
            }
          }
        },

        /**
         * Find the selected option from the secondary X Axis and return the axis object to render
         * the chart
         */
        _getSelectedSecondaryYAxis: function()
        {
          var selVal = this.secondYaxisDD.value;
          if (!selVal || selVal === "") return null;

          for (var i = 0; i < this._secondaryYAxis.length; i++)
          {
            var axis = this._secondaryYAxis[i];
            if (selVal.toLowerCase() === axis.id.toLowerCase())
            {
              axis.props = axis.props || {};
              axis.props["renderAsSecondary"] = true;
              return axis;

            }
          }
        },

        _updateChartControl: function()
        {
          var chartProps = this._getChartProps();

          // add the chart's title if saved
          // chartProps.title = this.chartTitleElem.innerText.trim();
          this._control.props = chartProps;
          this._control.grid = this._gridProps;
        },

        /**
         * When done designing the chart, close it and show it in the chart View.
         */
        _onClose: function()
        {
          this._queryChart = null;
          var chartData = null;
          if (this._datasheet)
          {
            // first update the datasheet
        	  this._datasheet = this._createDatasheet();

            // now save the datasheet and chart
            var deferr = new Deferred();

            all([ActivityQueryDef.saveDatasheet(this._chartName, this._datasheet, this._mode)]).then(
              lang.hitch(this, function(results)
              {
                if (this.viewSrc != "" && this.FEvents[this.viewSrc]) z1.fireEvent(this.FEvents[this.viewSrc].fireEvent, this.FEvents[this.viewSrc].params);
                else
                {
                  // Used when charts and dashboards were listed under My Workspace
                   z1.fireEvent(z1.Events.myWorkspace, {});
                }
                deferr.resolve(results.status);
              }), lang.hitch(this, function(error)
              {
                deferr.resolve(results.status);
              }));

            return deferr.promise;
          }
          else
          {
            udc.info(Res.saveFailed);
            //this.destroy();
          }
        },

        // .........................................................
        destroy: function()
        {
          this.inherited(arguments);

        },

        _end: 0
      });

  });
