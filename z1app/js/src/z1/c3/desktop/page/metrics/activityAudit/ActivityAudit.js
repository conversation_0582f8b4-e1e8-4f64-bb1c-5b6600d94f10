/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */

define(["dojo/_base/declare", "dojo/_base/lang", "dojo/on",
  "udc/udc", "z1/c3/desktop/start", "udc/core/rt/widgets/_ComplexBaseWidget",
  "udc/core/commons/loader",
  "dojo/text!z1/c3/desktop/page/templates/metrics/activityAudit/ActivityAudit.html",
  "dojo/i18n!z1/c3/desktop/page/nls/ActivityAuditRes",
  "z1/c3/desktop/page/metrics/activityAudit/ActivityAuditItem",
  "dijit/Dialog",
  "dojo/dom-class",
  "dojo/dom-construct",
  "dojo/topic",
  "z1/common/C3Util",
  "dojo/io-query",
  "dojo/date",
  "dojo/Deferred"
], 
function(declare, lang, on,
  udc, z1, BaseWidget, loader, templateStr, Res, ActivityAuditItem, Dialog,
  domClass, domConstruct, topic, C3Util, ioQuery, date, Deferred)
{

  /**
   * Creates a widget to show C3 landing page.
   */
  return declare(BaseWidget, {
    templateString: templateStr,
    Res: Res,

    /**
     * Creates an instance of this page.
     * 
     * @param {Object}
     * @param {Object}
     *          parentNode
     */
    constructor: function(params, parentNode)
    {
      this.inherited(arguments);
      if(parentNode)
      {
        this._parent = parentNode;
      }
      params._userCtx = (params.userCtx) ? params.userCtx : null;

      this.zDBG = 0;
      this.TIME_UNITS = "days";
      this.TIME_VALUE = 7;

      // if time difference between two activities is
      // sessionTimeGap+ minutes then
      // mark a session boundary
      // default 30 minutes
      params.sessionTimeGap = 30*60*1000;
      params.sessionTimeGapUnit = "millisecond";
    },

    // ////////////////////////////////////////////////////////////////////////////
    // Implementations

    postCreate: function()
    {
      this.inherited(arguments);
    },

    // .....................................................
    startup: function()
    {
      this.inherited(arguments);

      if(this._hideHeader)
      {
        // hide the head bar  [ "title"           "X" ]
        this._hCont.classList.add("c3_hide");
      }


      if(!this._showInline)
      {
        //...................
        // SHOW NORMAL POP UP DIALOG
        this.c3Dialog = new Dialog({
          title: this.dialog_title,
          //style: "min-width: 500px;min-height:300px;top:100px;",
          style: "min-width: 500px;min-height:300px;",
          hide: lang.hitch(this, function(){
            if(this.c3Dialog){
              this.c3Dialog.destroy();
            }
          }),
          class: "c3_auditActivity_dlg_box c3_dialog_box c3_dialog_box_x-large c3_dialog_box_noTitleBar c3_cols_nowrap"
        });
        dojo.place(this.dialogBody, this.c3Dialog.containerNode);
        this.own(this.c3Dialog);
        this.c3Dialog.show();
        //....................
      }
      if(this._showInline)
      {
        // add class to change UI to inline
        this.dialogBody.classList.add("c3_dlg_inline");
        this.dialogBody.classList.add("c3_flex_1");
      }


      this.fetchEventInfoAll().then(lang.hitch(this, function(){
        this._time.value = 7;
        this._showData({ showBusy: false });
      }));

      // hide download and filter options till data has loaded
      domClass.add(this.downloadBtn, "c3_hide");
      domClass.add(this.filterUnitControl, "c3_hide");
      domClass.add(this._filterBtn, "c3_hide");
      this._setEvents();
      this._subscribe();
    },


    // ...............................................
    // session boundary
    fetchConfig: function()
    {
      // here deferr.resolve always true
      var deferr = new Deferred();
      var targetUrl = "/c3/data/systemconfig/all";
      loader.GET(targetUrl, {}).then(lang.hitch(this, function(data)
      {
        if (data && Array.isArray(data) && data.length)
        {
          let tgCfg = data.find((cfg) => cfg.key === "z1.sessionTimeGap");
          if (tgCfg && !isNaN(tgCfg.value))
          {
            // config is in minutes, convert it to milliseconds
            this.sessionTimeGap = tgCfg.value*60*1000;
          }
        }
        deferr.resolve(data);
      }), lang.hitch(this, function(error)
      {
        udc.log(err);
        //udc.alert(err);
        deferr.resolve([]);
      }));

      return deferr.promise;
    },

    // ...............................................
    // events/signal list
    fetchEventInfoAll: function()
    {
      // here deferr.resolve always true
      var deferr = new Deferred();
      var targetUrl = "/c3/data/eventinfo/all";
      loader.GET(targetUrl, {}).then(lang.hitch(this, function(data)
      {
        //udc.log(targetUrl + " : ", JSON.stringify(data, null, "\t"));
        if (data && Array.isArray(data) === true && data.length)
        {
          //// sort by name
          //data.sort(function(a, b){
          //  return (a.name).localeCompare(b.name);
          //});
          
          this._eventInfoAll = data;
        }
        else
        {
          this._eventInfoAll = [];
        }
        deferr.resolve(data);
      }), lang.hitch(this, function(error)
      {
        udc.log(error);
        //udc.alert(error);

        // create blank
        this._eventInfoAll = [];
        deferr.resolve(this._customProfileAttributes);
      }));

      return deferr.promise;
    },

    //.............................................
    _showData: function(ctx={})
    {
      if(this.profileId)
      {
        ctx?.showBusy && udc.showBusy(this._bCont);

        this.fetchConfig({}).then(lang.hitch(this, function(dataCfg){
          this._fetchProfileActivitiesAudit({
            container: this._bCont,
            title: "This user",
            tunit: this.TIME_UNITS,
            tvalue: this.TIME_VALUE,
            profileId: this.profileId,
            user: "this"
          }).then(lang.hitch(this, function(){
            this._fetchProfileActionAudit({
              container: this._bCont,
              title: "This user",
              tunit: this.TIME_UNITS,
              tvalue: this.TIME_VALUE,
              profileId: this.profileId,
              user: "this"
            }).then(lang.hitch(this, function(){
              // show Audit
              udc.hideBusy();
              // combine the data and mark as action or activity
              this._combineData(this.getActivityData(), this.getActionData());
              // sort by date
              this._combinedData.sort(this._sortData);
              // reset page
              this.resetContextDisplay();
              this._showActivityAudit(this._combinedData);
              domClass.remove(this.downloadBtn, "c3_hide");
              domClass.remove(this.filterUnitControl, "c3_hide");
              domClass.remove(this._filterBtn, "c3_hide");
            }));
          }));
        }));
      }
    },

    //...............................................
    _fetchProfileActivitiesAudit: function(ctx)
    {
      var deferr = new Deferred();
      var ctx = ctx || {};
      if(this.zDBG !== 1 && (!ctx || !ctx.profileId))
      {
        return;
      }
      var targetUrl;
      var pObj = {};
      pObj.tunit = ctx.tunit;
      pObj.tvalue = ctx.tvalue;
      if(ctx.user === "this")
      {
        pObj.profileId = ctx.profileId;
        targetUrl = "/c3/data/activity/all?" + ioQuery.objectToQuery(pObj);
      }
      else if(ctx.user === "allusers")
      {
        targetUrl = "/c3/data/activity/all?" + ioQuery.objectToQuery(pObj);
      }
      // udc.showBusy(ctx.container);
      loader.GET(targetUrl, {
        ignoreAuth: true
      }).then(lang.hitch(this, function(data)
      {
        //udc.hideBusy();
        if(data && data.status !== "fail")
        {
          this.setActivityData(data);
//          this.resetContextDisplay();
//          this._showActivityAudit(data);
        }
        else
        {
          if(data && data.status === "fail" && data.reason)
          {
            udc.log(data.reason);
            udc.info(Res.vActivityDataError);
          }
          this.setActivityData([]);
        }
        deferr.resolve(data);
      }), lang.hitch(this, function(error)
      {
        //udc.hideBusy();
        C3Util.handleServerError(error);
        deferr.reject(error);
      }));
      
      return deferr.promise;
    },
    
  //...............................................
    _fetchProfileActionAudit: function(ctx)
    {
      var deferr = new Deferred();
      var ctx = ctx || {};
      if(this.zDBG !== 1 && (!ctx || !ctx.profileId))
      {
        return;
      }
      var targetUrl;
      var pObj = {};
      pObj.tunit = ctx.tunit;
      pObj.tvalue = ctx.tvalue;
      if(ctx.user === "this")
      {
        pObj.profileId = ctx.profileId;
        targetUrl = "/c3/data/actions/aggregate?" + ioQuery.objectToQuery(pObj);
      }
      else if(ctx.user === "allusers")
      {
        targetUrl = "/c3/data/actions/aggregate?" + ioQuery.objectToQuery(pObj);
      }
      // udc.showBusy(ctx.container);
      loader.GET(targetUrl, {
        ignoreAuth: true
      }).then(lang.hitch(this, function(data)
      {
        //udc.hideBusy();
        if(data && data.status !== "fail")
        {
          this.setActionData(data);
        }
        else
        {
          //ZMOB-6058 set the action array to [] incase the data returned is empty or corrupted.
          //to prevent the UI from failing to display the activities.
          udc.log(data.reason);
          this.setActionData([]);
        }
        deferr.resolve(data);
      }), lang.hitch(this, function(error)
      {
        //udc.hideBusy();
        C3Util.handleServerError(error);
        deferr.reject(error);
      }));
      
      return deferr.promise;
    },

 
    //........................
    setActivityData: function(data)
    {
      this._data = data;
    },
    
    setActionData: function(data)
    {
      this._actionData = data;
    },
    
    getActivityData: function()
    {
      return this._data;
    },
    
    getActionData: function()
    {
      return this._actionData;
    },
    
    _combineData: function(activity, action)
    {
      this._combinedData = [];
      var actLength = activity.length;
      for ( var i = 0; i < actLength; i++)
      {
        this._combinedData[i] = {};
        this._combinedData[i].time = activity[i].time ? activity[i].time : "";
        this._combinedData[i].activity = activity[i].activity ? activity[i].activity : "";
        this._combinedData[i].action = activity[i].type ? activity[i].type : "";
        this._combinedData[i].channels = activity[i].channel ? activity[i].channel : "";
        this._combinedData[i].device = activity[i].device ? activity[i].device : "";
      }
      for ( var j = 0; j < action.length; j++)
      {
        // convert time to milliseconds before writing
        var d = new Date(action[j].time.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})(\d{3})/, '$1-$2-$3T$4:$5:$6.$7Z'))
        action[j].time = d.getTime();
        
        //this._combinedData[actLength + j] = {};
        this._combinedData[actLength + j] = lang.mixin({}, action[j]);
        this._combinedData[actLength + j].time = action[j].time ? action[j].time : "";
        this._combinedData[actLength + j].activity = action[j].activity ? action[j].activity : "";
        this._combinedData[actLength + j].action = action[j].type ? action[j].type : "";
        this._combinedData[actLength + j].channels = action[j].channel ? action[j].channel : "";
        this._combinedData[actLength + j].device = action[j].device ? action[j].device : "";
        this._combinedData[actLength + j].name = action[j].name ? action[j].name : "";
      }
    },
    
    _sortData: function(objA, objB)
    {
      if (!objB?.time || isNaN(objB.time)) objB.time = 0;
      if (!objA?.time || isNaN(objA.time)) objA.time = 0;

      return parseInt(objB.time) - parseInt(objA.time);
    },

    //...................................
    // clear UI - no conversation selected.
    resetContextDisplay: function()
    {
      domConstruct.empty(this._bCont);
    },

    //...............................
    _onSearchCustomerId: function()
    {
      udc.log(this.customerIdFld.value);
      // clear context screen
      this.resetContextDisplay();
      this._fetchProfileId({ customerId: this.customerIdFld.value});
    },

    // ...........................................
    _getActionIconHtml: function(key)
    {
      var actionName = { 
        "ABCompositeAction": `<i class="ti-mobile"></i>`, 
        "AgentAlertAction": `<img src="res/alert.png" />`,
        "EmailAlertAction": `<img src="res/email.png" />`, 
        "CustomAction": `<i class="fa fa-code"></i>`,
        "MobileAppBannerAction": `<img src="res/banner.png" />`,
        "MobileAppFullscreenAction": `<img src="res/fullscreen.png" />`,
        "MobileAppAlertAction": `<img src="res/alert.png" />`,
        "MobileAppAlertKBAction": `<img src="res/article.png" />`,
        "MobileAppInbox": `<img src="res/appbox.png" />`,
        "MobileAppRaw": `<img src="res/app_msg_raw.png" />`,
        "MobileAppSMSMessage": `<img src="res/sms.png" />`,
        "PushNotification": `<img src="res/push.png" />`,
        "Code": `<i class="fa fa-code"></i>`
      }
      
      return (actionName[key] ? `<div class="c3_AAIAxnI c3_flex_center_1">${actionName[key]}</div> ` : "");
    },
    
    _getActionName: function(key)
    {
      var actionName = { 
        "ABCompositeAction": "AB Composite", 
        "AgentAlertAction": "Agent Alert", 
        "EmailAlertAction": "Agent Email", 
        "CustomAction": "Custom Action",
        "MobileAppBannerAction": "Banner",
        "MobileAppFullscreenAction": "Full Screen",
        "MobileAppAlertAction": "Alert",
        "MobileAppAlertKBAction": "Article",
        "MobileAppInbox": "Appbox",
        "MobileAppRaw": "Raw",
        "MobileAppSMSMessage": "SMS Message",
        "PushNotification": "Push Notification"
          }
      
      return (actionName[key] ? actionName[key] : "");
    },

    _getActionType: function(key)
    {
      var actionType = { 
        "ABCompositeAction": "Mobile", 
        "AgentAlertAction": "Alert", 
        "EmailAlertAction": "Email", 
        "CustomAction": "Custom",
        "MobileAppBannerAction": "Banner",
        "MobileAppFullscreenAction": "Full Screen",
        "MobileAppAlertAction": "Alert",
        "MobileAppAlertKBAction": "Article",
        "MobileAppInbox": "Appbox",
        "MobileAppRaw": "Raw Data",
        "MobileAppSMSMessage": "SMS Message",
        "PushNotification": "Push Notification"
      }
      
      return actionType?.[key] || "N/A";
    },
    
    _getFullActionName: function(data, showHtml)
    {
      if (this._getActionName(data.action) != "")
      {
        if(showHtml)
        {
          const expLink = data?.associatedExperienceUnknown
            ? "" : "c3_txt_blue2 c3_txt_lnk";

          // html format for c3 display
          //return "<span class='c3_auditActivityItem_actionType'>"+ this._getActionName(data.action) + "</span>:<span class='c3_auditActivityItem_actionName'>" + data.name + "</span>";
          return `<span class='c3_auditActivityItem_actionType c3_text_ellipsis ${expLink}'
                     title="${data.experienceName}" >
                    ${data.experienceName} </span>`;
        }

        //return this._getActionName(data.action) + ":" + data.name;
        return data.name;
      }

      return "";
    },

    _getFullActionName2: function(data, showHtml)
    {
      if (this._getActionName(data.action) != "")
      {
        if(showHtml)
        {
          const actLink = data?.action === "CustomAction"
            || !data.message
            ? "" : "c3_txt_blue2 c3_txt_lnk";

          // html format for c3 display
          return `<span class='c3_auditActivityItem_actionType c3_text_ellipsis ${actLink}'>  (${data.name} </span> 
                  <span> ${this._getActionType(data.type)})</span>`;
        }

        return data.name;
      }

      return "";
    },

    //..................................
    _showActivityAudit: function(data)
    {
      if(!data || !data.length)
      {
        this._bCont.textContent = Res.noDataFound;
        return;
      }

      // map channelType to icon class name
      var channelTypeClass = {
        "phone": "ti-mobile",
        "tablet": "ti-tablet",
        "desktop": "ti-desktop",
        "push": "fa-upload"
      };

      this._auditData = [];
      this.summaryByChannel = {
        "desktop": {
          "sessions": 0,
          "timeSpent": 0,
          "actionsReceived": 0
        },
        "phone": {
          "sessions": 0,
          "timeSpent": 0,
          "actionsReceived": 0
        },
        "tablet": {
          "sessions": 0,
          "timeSpent": 0,
          "actionsReceived": 0
        }
      }

      let actionCount = 0;
      let activityCount = 0;

      // Get channelType and deviceOS from eventInfo/all list
      // Assumption: channelType, deviceOS for Action will be
      // same as the ones associated with previous Activity/event,
      // the data is sorted by time.
      var tChannelType;
      var tDeviceOS;

      var dDv;
      // previous audit item date in millisecond
      var msDt0;
      var dt0;
      var dt1;
      var j = 0;
      for(var i = 0; i < data.length; i++)
      {
        var aData = {};
        if(data[i] && data[i].activity)
        {
          aData = {
            tIcon: "ti-signal",
            time: data[i].time,
            activity: data[i].activity ? data[i].activity : "",
            channelType: data[i].channelType ? data[i].channelType : "",
            deviceOS: data[i].deviceOS ? data[i].deviceOS : "",
          };
          aData.activityDisplayName = data[i].type ? data[i].type : data[i].activity;
          if(this._eventInfoAll && this._eventInfoAll.length)
          {
            j = 0;
            for(j = 0; j < this._eventInfoAll.length; j++)
            {
              if(this._eventInfoAll[j].name === aData.activity)
              {
                // display name
                if(this._eventInfoAll[j].displayName)
                {
                  aData.activityDisplayName = this._eventInfoAll[j].displayName;
                }
                // channelType - device type
                if(this._eventInfoAll[j].channelType)
                {
                  aData.channelType = this._eventInfoAll[j].channelType;
                  tChannelType = this._eventInfoAll[j].channelType;
                }
                // deviceOS - os
                if(this._eventInfoAll[j].deviceOS)
                {
                  aData.deviceOS = this._eventInfoAll[j].deviceOS;
                  tDeviceOS = this._eventInfoAll[j].deviceOS;
                }
                break;
              }
            }
          }
        }
        else if (data[i] && data[i].action)
        {
          //aData = {
          //  time: data[i].time,
          //  action: data[i].action ? data[i].action : "",
          //  message: data[i].message ? data[i].message : "",
          //  channels: data[i].channels ? data[i].channels : "",
          //  device: data[i].device ? data[i].device : "",
          //  actionDisplayName: data[i].action ? this._getFullActionName(data[i]) : ""
          //};
          aData = lang.mixin(aData, data[i]);
          aData.tIcon = (data[i].icon) ? data[i].icon : "ti-flag-alt";
          aData.actionDisplayName = data[i].action ? this._getFullActionName(data[i], true) : "";
          aData.actionDisplayName2 = data[i].action ? this._getFullActionName2(data[i], true) : "";
          aData.iconHtml = this._getActionIconHtml(aData.action);
          if(tChannelType)
          {
            aData.channelType = tChannelType;
            if (this.summaryByChannel[tChannelType])
            {
              // # of actions received by customer
              this.summaryByChannel[tChannelType]["actionsReceived"]++;
            }
          }
          if(tDeviceOS)
          {
            aData.deviceOS = tDeviceOS;
          }
        }

        // create group by day box
        //dt = new Date(aData.time).getTime();
        dt1 = C3Util.MS_to_yyyy_mm_dd(aData.time);
        if(dt0 !== dt1)
        {
          // new day
          dt0 = dt1;
          dDv = document.createElement("div");
          dDv.classList.add("c3_auditActivityBx");
          var dayDv = document.createElement("div");
          dayDv.classList.add("c3_auditActivityBxDay");
          // dayDv.textContent = dt1;
          let dayDt = C3Util.ms_to_dateStr(aData.time);
          if (dayDt)
          {
            dayDv.textContent = dayDt;
          }
          dDv.appendChild(dayDv);
          this._bCont.appendChild(dDv);
        }

        // Mark Session boundary if:
        // - first activity or
        // - time difference between two activities is greater than sessionTimeGap
        // - difference is calculated by subtracting previous data time from current data time
        //      because we are displaying the latest activity/action at the top of the page
        //      and going back in time as the user scrolls down

        // first record. Create a new widget for Session count info
        if(!msDt0)
        {
          var sDvCont = this._getSessionCountsDivObj(tChannelType, channelTypeClass);
          this._prevSessionDv = sDvCont;
          dDv.appendChild(sDvCont);
        } 
        else if(date.difference(new Date(aData.time), new Date(msDt0), this.sessionTimeGapUnit) > this.sessionTimeGap)
        {
          // Update counts for previous session count info object
          if (this._prevSessionDv && activityCount)
          {
            this._createCountEl("activity", activityCount, this._prevSessionDv);
          }
          if (this._prevSessionDv && actionCount)
          {
            this._createCountEl("action", actionCount, this._prevSessionDv);
          }
          // create new session boundary
          var sDvCont = this._getSessionCountsDivObj(tChannelType, channelTypeClass);
          this._prevSessionDv = sDvCont;
          dDv.appendChild(sDvCont);

           // reset previous session counts
           activityCount = 0;
           actionCount = 0;
        }
        
        // Since activity and action sesssion counts are updated at the end of session,
        // i.e. after new session boundary is created, their respective counts should
        // be updated later only.
        // Otherwise count of very first item in data[] will always get ingored
        if (data[i].activity)
        {
          activityCount++;
        } 
        else if (data[i].action) 
        {
          actionCount++;
        }

        msDt0 = aData.time;

        var activityAuditItem = new ActivityAuditItem({ 
          _parent: this,
          aData: aData
        });
        activityAuditItem.startup();

        domConstruct.place(activityAuditItem.domNode, dDv, "last");
        this.own(activityAuditItem);
      }
      // last sessions - previous session count
      if (this._prevSessionDv && activityCount)
      {
        this._createCountEl("activity", activityCount, this._prevSessionDv);
      }
      if (this._prevSessionDv && actionCount)
      {
        this._createCountEl("action", actionCount, this._prevSessionDv);
      }

      this._showSummary();
    },

    // ..............................................
    _createCountEl: function(act, count, cont)
    {
      let actCountDv = document.createElement("div");
      actCountDv.classList.add(...["c3_AAICnt", "c3_AAICnt" + act, "c3_rows_nowrap", "c3_rows_spacing", "c3_align_center"]);
      let v = document.createElement("div");
      v.classList.add(...["c3_AAICntV"]);
      v.textContent = count;
      actCountDv.appendChild(v);
      let lbl = document.createElement("div");
      lbl.classList.add(...["c3_AAICntLbl"]);
      lbl.textContent = Res[act + "CountLbl"];
      actCountDv.appendChild(lbl);
      cont.appendChild(actCountDv);
    },

    // ..............................................
    _getSessionCountsDivObj: function(tChannelType, channelTypeClass) {
      var sDvCont = document.createElement("div");
      sDvCont.classList.add("c3_auditActivitySCont");
      sDvCont.classList.add("c3_rows_nowrap_start");
      sDvCont.classList.add("c3_rows_spacing_x");
      sDvCont.classList.add("c3_align_center");
      var sDv = document.createElement("div");
      sDv.classList.add("c3_auditActivitySess");
      sDv.textContent = Res.sessionSummary;
      // device type
      var devDv = document.createElement("div");
      devDv.classList.add("c3_auditActivityDevice");
      devDv.classList.add("c3_flex_center_1");
      // add device type icon
      if (tChannelType && channelTypeClass[tChannelType]) {
        var devI = document.createElement("i");
        devI.classList.add(...["c3_auditActivityDeviceIcon", channelTypeClass[tChannelType]]);
        devI.title = tChannelType;
        devDv.appendChild(devI);
    
        if (this.summaryByChannel[tChannelType]) {
          // # of sessions for channel type
          this.summaryByChannel[tChannelType]["sessions"]++;
        }
      }
      sDvCont.appendChild(devDv);
      sDvCont.appendChild(sDv);
      return sDvCont;
    },

    // ..............................................
    _showSummaryItemRow: function(obj, k, dv2, ch)
    {
      // sessions
      let dv21 = document.createElement("div");
      dv21.classList.add(...["c3_cProfActSumBx2i", "c3_rows_nowrap"]);
      let dv211 = document.createElement("div");
      dv211.classList.add(...["c3_cProfActSumBx2i1", "c3_pd_4"]);
      dv211.textContent = obj[k];
      dv21.appendChild(dv211);
      let dv212 = document.createElement("div");
      dv212.classList.add(...["c3_cProfActSumBx2i2", "c3_flex_1", "c3_pd_4"]);
      if (k === "timeSpent")
      {
        dv212.textContent = Res[k + "Sum" + this.summaryByChannel[ch]["unit"]];
      }
      else
      {
        dv212.textContent = Res[k + "Sum"];
      }
      dv21.appendChild(dv212);
      dv2.appendChild(dv21);
    },

    // ..............................................
    _showSummaryItem: function(ch)
    {
      let dv = document.createElement("div");
      dv.classList.add(...["c3_cProfActSumBx", "c3_rows_nowrap", "c3_pd_6"]);
      let iconCls;
      if(ch === "desktop")
      {
        iconCls = "ti-desktop";
      }
      if(ch === "phone")
      {
        iconCls = "ti-mobile";
      }
      if(ch === "tablet")
      {
        iconCls = "ti-tablet";
      }
      let iconDv = document.createElement("div");
      iconDv.classList.add(...["c3_cProfActSumBx1", "c3_flex_center_1", "c3_pd_10"]);
      if (iconCls)
      {
        let icon = document.createElement("i");
        icon.title = ch;
        icon.classList.add(...["c3_cProfActSumBxI", iconCls]);
        iconDv.appendChild(icon);
      }
      dv.appendChild(iconDv);
      let dv2 = document.createElement("div");
      dv2.classList.add(...["c3_cProfActSumBx2", "c3_flex_1", "c3_cols_nowrap", "c3_cols_spacing_x"]);
      // time spent = total (sessions)
      this.summaryByChannel[ch]["unit"] = "hour";
      if ((this.summaryByChannel[ch]["sessions"]*10) < 1)
      {
        // seconds
        // less than 1 minute
        this.summaryByChannel[ch]["unit"] = "second";
        this.summaryByChannel[ch]["timeSpent"] = (this.summaryByChannel[ch]["sessions"]*10).toFixed(2).replace(".00", "");
      }
      else if ((this.summaryByChannel[ch]["sessions"]*10) < 60)
      {
        // minutes
        // less than 1 hr
        this.summaryByChannel[ch]["unit"] = "minute";
        this.summaryByChannel[ch]["timeSpent"] = (this.summaryByChannel[ch]["sessions"]*10).toFixed(2).replace(".00", "");
      }
      else
      {
        this.summaryByChannel[ch]["unit"] = "hour";
        this.summaryByChannel[ch]["timeSpent"] = ((this.summaryByChannel[ch]["sessions"]*10)/60).toFixed(2).replace(".00", "");
      }
      this._showSummaryItemRow(this.summaryByChannel[ch], "sessions", dv2, ch);
      this._showSummaryItemRow(this.summaryByChannel[ch], "timeSpent", dv2, ch);
      this._showSummaryItemRow(this.summaryByChannel[ch], "actionsReceived", dv2, ch);
      dv.appendChild(dv2);
      this._summaryCont.appendChild(dv);
    },

    // ..............................................
    _showSummary: function()
    {
      domConstruct.empty(this._summaryCont);

      Object.keys(this.summaryByChannel).forEach((ch) => {
        if (this.summaryByChannel[ch]["sessions"] ||
          this.summaryByChannel[ch]["timeSpent"] ||
          this.summaryByChannel[ch]["actionsReceived"]
        )
        {
          this._summaryCont.classList.remove("c3_hide");
          this._showSummaryItem(ch);
        }
      });
    },

    //...............................................
    // Filter data - show/hide rows
    // 1. [all, actions, activities]
    // 2. OS [android, iOS, html5]
    // 3. device [phone, tablet, desktop]
    _filterData: function()
    {
      // 0. Un-hide all
      
      var elmts = this._bCont.querySelectorAll(".c3_auditActivityItem.c3_hide");
      for(var k = 0; k < elmts.length; k++)
      {
        elmts[k].classList.remove("c3_hide");
      }

      // 1. Filter by [all, actions, activities]

      var choice = "all";
      if (this.filterUnit && this.filterUnit.value)
      {
        choice = this.filterUnit.value;
      }
      // get elements
      var actions = this._bCont.querySelectorAll(".actionAuditItemForFilter");
      var activities = this._bCont.querySelectorAll(".activityAuditItemForFilter");
      if (choice == "all")
      {
        for (var i = 0; i < actions.length; i++)
        {
          domClass.remove(actions[i], "c3_hide");
        }
        for (var j = 0; j < activities.length; j++)
        {
          domClass.remove(activities[j], "c3_hide");
        }
      }
      else if (choice == "actions")
      {
        // hide activities
        for (var i = 0; i < actions.length; i++)
        {
          domClass.remove(actions[i], "c3_hide");
        }
        for (var j = 0; j < activities.length; j++)
        {
          domClass.add(activities[j], "c3_hide");
        }

      }
      else if (choice == "activities")
      {
        // hide actions
        for (var i = 0; i < actions.length; i++)
        {
          domClass.add(actions[i], "c3_hide");
        }
        for (var j = 0; j < activities.length; j++)
        {
          domClass.remove(activities[j], "c3_hide");
        }
      }

      // 2. OS & channelType/device

      // Get each button's state (selected?)
      // Hide rows corresponding to un-selected

      var hideRowsWithClasses = [];

      // OS [android, ios, html5]

      if(!this.osAndroid.classList.contains("c3_msg_os_btn_sel"))
      {
        hideRowsWithClasses.push(".c3_auditActivityItem_osandroid");
      }
      if(!this.osIOS.classList.contains("c3_msg_os_btn_sel"))
      {
        hideRowsWithClasses.push(".c3_auditActivityItem_osios");
      }
      if(!this.osHTML5.classList.contains("c3_msg_os_btn_sel"))
      {
        hideRowsWithClasses.push(".c3_auditActivityItem_oshtml5");
      }

      // channelType - device [phone, tablet, desktop, push]

      if(!this._phoneDevice.classList.contains("c3_msg_os_btn_sel"))
      {
        hideRowsWithClasses.push(".c3_auditActivityItem_devicephone");
      }
      if(!this._tabletDevice.classList.contains("c3_msg_os_btn_sel"))
      {
        hideRowsWithClasses.push(".c3_auditActivityItem_devicetablet");
      }
      if(!this._desktopDevice.classList.contains("c3_msg_os_btn_sel"))
      {
        hideRowsWithClasses.push(".c3_auditActivityItem_devicedesktop");
      }
      if(!this._pushDevice.classList.contains("c3_msg_os_btn_sel"))
      {
        hideRowsWithClasses.push(".c3_auditActivityItem_devicepush");
      }

      if(hideRowsWithClasses && hideRowsWithClasses.length)
      {
        var rows = this._bCont.querySelectorAll(hideRowsWithClasses.join(", "));
        for(var r = 0; r < rows.length; r++)
        {
          if(rows[r]) rows[r].classList.add("c3_hide");
        }
      }
    },

    // .................................
    _setEvents: function()
    {
      this.own(
        // show Activity Audit list
        on(this._showBtn, "click", lang.hitch(this, function(){
          var t = 7;
          if(this._time && this._time.value && !isNaN(this._time.value))
          {
            this.TIME_VALUE = this._time.value;
          }
          var u = "days";
          if(this.tsUnit && this.tsUnit.value)
          {
            this.TIME_UNITS = this.tsUnit.value;
          }
          this._showData({ showBusy: true });
        })),

        // filter - change
        on(this.filterUnit, "change", lang.hitch(this, function(){
          this._filterData();
        })),

        // hidden 'filter' button
        on(this._filterBtn, "click", lang.hitch(this, function(){
          this._filterData();
        })),

        // filter by device type [phone, tablet, desktop]
        on(this._phoneDevice, "click", lang.hitch(this, function(){
          this._phoneDevice.classList.toggle("c3_msg_os_btn_sel");
          this._filterData();
        })),
        on(this._tabletDevice, "click", lang.hitch(this, function(){
          this._tabletDevice.classList.toggle("c3_msg_os_btn_sel");
          this._filterData();
        })),
        on(this._desktopDevice, "click", lang.hitch(this, function(){
          this._desktopDevice.classList.toggle("c3_msg_os_btn_sel");
          this._filterData();
        })),
        on(this._pushDevice, "click", lang.hitch(this, function(){
          this._pushDevice.classList.toggle("c3_msg_os_btn_sel");
          this._filterData();
        })),

        // filter by OS type [android, iOS, html5]
        on(this.osAndroid, "click", lang.hitch(this, function(){
          this.osAndroid.classList.toggle("c3_msg_os_btn_sel");
          this._filterData();
        })),
        on(this.osIOS, "click", lang.hitch(this, function(){
          this.osIOS.classList.toggle("c3_msg_os_btn_sel");
          this._filterData();
        })),
        on(this.osHTML5, "click", lang.hitch(this, function(){
          this.osHTML5.classList.toggle("c3_msg_os_btn_sel");
          this._filterData();
        })),

        // download
        on(this.downloadBtn, "click", lang.hitch(this, function(){
          if(!this._combinedData || !this._combinedData.length) return;
          var s = [];
          // csv-header : Date,Activity,Action,Data Channel
          // Device can be figured from Channel ex. mobileAndroid or desktopWeb
          s.push(Res.hDate + "," + Res.hActivity + "," + Res.hAction + "," + Res.hChannel)
          for(var i = 0; i < this._combinedData.length; i++)
          {
            s.push("\n");
            s.push(new Date( new Date(this._combinedData[i].time)).toLocaleString().replace(",", ""));
            s.push("," + this._combinedData[i].activity);
            s.push("," + this._getFullActionName(this._combinedData[i]));
            s.push("," + this._combinedData[i].channels);
          }
          s = s.join("");
          // once the data is in csv donwload the csv as a file.
          this.exportData.href = 'data:attachment/csv,' + encodeURI(s);
          var dt = (new Date()).getTime();
          var mDt = C3Util.MS_to_yyyy_mm_dd_hhmm(dt);
          if(mDt)
          {
            mDt = mDt.split(" ").join("").split(":").join("");
          }
          this.exportData.download = "activity_audit_" + mDt + "_" + this._time.value + "_" + this.tsUnit.value + ".csv";
          this.exportData.click();
        })),

        // close
        on(this.btnClose, "click", lang.hitch(this, function(){
          if(typeof this._onCloseCB === 'function')
          {
            this._onCloseCB();
          }
          this.destroy();
        }))
      );
    },

    //...............................
    _subscribe: function()
    {
      this.own(
        // refresh button clicked
        topic.subscribe("/interactions/refresh", lang.hitch(this, function(){
          // refresh - reload content for same customer id.
          //this._onSearchCustomerId();
         }))
      );
    },

    // .................................
    destroy: function()
    {
      // if there is a ref then destroy it.
      try{
        if(this._parent && this._parent.activityAudit)
        {
          this._parent.activityAudit = null;
        }
      }
      catch(err)
      {
      }

      this.inherited(arguments);
    },

    _end: 0
  });

});
