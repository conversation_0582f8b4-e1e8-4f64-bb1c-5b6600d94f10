/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 *
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define(
  ["dojo/_base/declare",
    "udc/core/rt/widgets/_BaseWidget",
    "dojo/text!z1/c3/desktop/page/templates/metrics/dashboard/insights/SessionScoreChart.html",
    "dojo/i18n!z1/c3/desktop/page/nls/PredictionModelResultsRes",
    "dojo/dom-construct",
    "z1/c3/desktop/page/metrics/charts/ChartTemplate",
    "z1/common/C3Util",
    "dojo/topic",
    "z1/c3/utils/QueryMapper", "z1/common/FFUtil",
    "udc/udc"
  ],
  function(declare, BaseWidget, templateStr, Res,
    domConstruct, ChartTemplate, C3Util,topic, QueryMapper, FFUtil, udc)
  {
    const featureFlag = FFUtil.getBooleanFeatureFlag(FFUtil.FLAG.NEW_SUBSYSTEM);

    /**
     * Creates a widget to show C3 landing page.
     */
    return declare(BaseWidget, {
      templateString: templateStr,
      Res: Res,

      /**
       * Creates an instance of this page.
       *
       * @param {Object}
       *          parentNode
       */
      constructor: function(params)
      {
        this.inherited(arguments);
      },

      // ////////////////////////////////////////////////////////////////////////////
      // Implementations

      postCreate: function()
      {
        this.inherited(arguments);
      },

      // .....................................................
      startup: function()
      {
        this.inherited(arguments);
        this.init();
      },

      init: function()
      {
        this._clearContent();
        this._showChart();
      },

      // ...................................
      _clearContent: function()
      {
        this._noDataCont.classList.add("c3_hide");
        if(this._chartCont)
        {
          domConstruct.empty(this._chartCont);
          this._chartCont.classList.remove("c3_hide");
        }
      },

      _convertToDateObj: function(dt)
      {
        let y = dt.substr(0, 4);
        let m = parseInt(dt.substr(4, 2)) -1;
        let d = dt.substr(6, 2);

        return new Date(y, ""+m, d);
      },

      _showWait: function(isShow)
      {
        C3Util.showWait(this._chartCont, isShow);
      },

      // ...................................
      _showChart: function()
      {
        this._showWait(true);

        // create the Chart Template which loads the time control and renders the chart
        var ct = new ChartTemplate({
          header: "",
          useTimeDim: false,
          showFrequency: false,
          showTimeWidget: false,
          showAPIWidget: false,
          resource: this.Res,
          fromDt: this._convertToDateObj(this._context.fromDt),
          toDt: this._convertToDateObj(this._context.toDt),
          period: this._context.period
        });
        ct.setDashboard(this.dashboardId);
        ct.startup();
        ct.placeAt(this._chartCont);
        this.own(ct);

        // now get the chart and draw it
        var chart = ct.getChart();
        var customParams = QueryMapper._setDataSheetCustomParams({
          startDate: this._convertToDateObj(this._context.fromDt),
          endDate: this._convertToDateObj(this._context.toDt),
          modelId: this._breakdown || "desktop_EPPV2",
          modelType: this._context.type || "EPPV2" ,
          channel: this._context.channel || "desktop",
          version: this._context.version || "*",
        }, {
          uriIds: [
            { key: "uriIdSessByModel", value: "sessionScoreBins" },
            { key: "uriIdConvByEpp", value: "sessionScoreBins" }
            ]
        })
        chart.setCustomParam(customParams);

        this._chartTemplate = ct;
        let sessionScoreChart = this;
        chart.beforePageStartup = (page) =>
        {
          page.handleHtmlTooltipFunc = (params) => { return this._handleHtmlTooltipFunc(params) };
          page.handleOnReadyFunc = (chart,page) => {return this._onReadyFunc(chart,page)};
          page.onBindingLoad = (id, response) =>
          {
            // if (this.isC2 && ct.resource.page.data["udc.system.core.CommonOOTB:conversionByEppC2"])
            // {
            //   ct.resource.page.data["udc.system.core.CommonOOTB:conversionByEppC2"].control.props.chartArea.left = "5%";
            // }
            // if (this.isC2 && ct.resource.page.data["udc.system.core.CommonOOTB:SessionsByModelChartC2Sheet"])
            // {
            //   ct.resource.page.data["udc.system.core.CommonOOTB:SessionsByModelChartC2Sheet"].control.props.chartArea.left = "5%";
            // }

            var items = sessionScoreChart._handleData(id, response, ct, this._breakdown);
            if(items)
            {
              this._exportSessionData(id, items);
              response.items.splice(0, response.items.length)
              for (var i = 0; i < items.length; i++)
              {
                response.items.push(items[i]);
              }
            }
          }
        };

        chart.onNoData = (id) =>
        {
          this._showWait(false);
          this._showNoData(true);
        }

        chart.showWait = (isShow) =>
        {
          this._showWait(isShow);
        }

        chart.startup();
        this._chartObj = chart;
      },

      _onReadyFunc: function(chart,page)
      {
        if(!this.charts)
          this.charts = [];
        this.charts.push(chart);

        if(this.charts.length == page.def.bindings.chart.length)
        {
          // dashboard is ready and all charts loaded
          topic.publish("/SessionScoreChart/dashboardReady",{
            status:"ready",
            chartWidth:this.getChartWidth()
          });
        }
      },

      getChartWidth: function()
      {
        let maxwidth = 0;
        for(let chart of this.charts)
        {
          let cont = chart.container;
          const rect = cont.querySelector("rect");
          // if there are more than 1 width for charts, return the largest
          if(rect && rect.width.baseVal.value && rect.width.baseVal.value > maxwidth)
          {
            maxwidth = rect.width.baseVal.value;
          }
        }
        return maxwidth;
      },
      //...............................................
      _exportSessionData: function(id, items)
      {
        if (this._parent._sessionData.length == 0)
        {
          this._parent._sessionData = JSON.parse(JSON.stringify(items));
          return;
        }
        for (var i = 0; i < items.length; i++)
        {
          let exists = this._parent._sessionData.find(b => items[i].scoreBin == b.scoreBin) && this._parent._sessionData.find(b => items[i].type == b.type);
          if (!exists)
          {
            this._parent._sessionData.push(items[i]);
          }
        }
      },

      //...............................................
      _showNoData: function (showNoData)
      {
        this?._parent?.showNoChartData && this._parent.showNoChartData(showNoData);

        if (showNoData)
        {
          if (this._chartCont)
          {
            this._chartCont.classList.add(...["c3_hide"]);
          }

          if (this._screen)
          {
            this._screen.classList.add(...["c3_bxWhite"]);
          }

          // Show no data div
          if (this._noDataCont)
          {
            domConstruct.empty(this._noDataChart);
            this._noDataChart.append(C3Util._createSvgIconDiv("no-chart-data", 280, 195));
            this._noDataCont.classList.remove("c3_hide");
          }

          // Hide the export button
          if (this._export)
          {
            this._export.classList.add(...["c3_hide"]);
          }
          // remove the waiting placeholder if no data is avaialble
          document.querySelector('[data-dojo-attach-point="_predTabCont2"]')
            ?.classList.remove("c3_waitBx");
          udc.hideBusy();
          return;
        }
        if (this._chartCont)
        {
          this._chartCont.classList.remove("c3_hide");
        }
        if (this._screen)
        {
          this._screen.classList.remove("c3_bxWhite");
        }

        // Hide no data div
        if (this._noDataCont)
        {
          this._noDataCont.classList.add(...["c3_hide"]);
        }
        // Show the export button
        if (this._export)
        {
          this._export.classList.remove("c3_hide");
        }

        this?.parent?.showNoData && history.parent.showNoData(showNoData);
      },

      // ......................
      destroy: function()
      {
        this.inherited(arguments);
      },

      /**
       * Generate html tooltip.
       *
       * If "genToolTipFunc" function name is speciefied in datasheet, it would use
       * that specific function to generate the tooltop. Other wise it would use
       * the Chart Template's 'default "_genDefaultHtmlTooltip" function to create the tooltip
       *
       * @param {object} params - datasheet chart properties
       *
       * @returns a string of html format tooltip
       */
      _handleHtmlTooltipFunc: function(params)
      {
        if (!params && !params.props && !params.props.tooltip &&!params.props.tooltip.isHtml) return params.row;

        let ttObj = params.props.tooltip;
        if (typeof this[ttObj.genToolTipFunc] === "function")
        {
          return this[ttObj.genToolTipFunc](params);
        }

        return this._chartTemplate._genDefaultHtmlTooltip(params);
      },

     /**
      * Generates Session Depth Chart HTML Tooltip that has a y-axis number value
      * that is abbreviated.
      *
      * Is parsing the tooltip html template that's replacing the html context with
      * param values.
      *
      * @param {Object} params - datasheet chart properties
      *
      * @returns google chart data row object.
      */
     _genSessionScoreToolTip: function(params)
     {
      let html = params.props.tooltip.htmlTemplate;

      let totalSessions = params.item['totalSessions'];
      let rate = params.yList[0].id == "percentOfSessions" ? params.item['percentOfSessions'] : params.item['successRate'];
      let range = params.item['scoreStart'] + "-" + params.item['scoreEnd'];
      if (totalSessions === undefined)
      {
        totalSessions = 0;
      }
      totalSessions = (totalSessions > 0 && totalSessions < 10000) ? C3Util._abbreviateNumber(totalSessions, 1) : C3Util._abbreviateNumber(totalSessions);

      return html.replace('${range}', range).replace('${rate}', rate).replace('${sessions}', totalSessions);
     },

    _handleData: function (id, response, ct, modelId)
    {
      var items = [];
      var data = {};
      this._showWait(false);
      if (response.items.length == 0)
      {
        this._showNoData(true);
        return;
      }

      for (var i = 0; i < response.items.length; i++)
      {
        if(response.items[i].totalSessions > 0 || id == "viewSessionsChart") {
          data = {
            scoreBin: response.items[i].scoreBin + "",
            successRate: Number(response.items[i].successRate.toFixed(2)),
            percentOfSessions: Number(response.items[i].percentOfSessions.toFixed(2)),
            scorePlot: ((response.items[i].scoreStart + response.items[i].scoreEnd) / 2) + "",
            scoreStart: response.items[i].scoreStart + "",
            scoreEnd: response.items[i].scoreEnd + "",
            type: modelId + "",
            totalSessions: response.items[i].totalSessions + "",
            totalSuccesses: response.items[i].totalSuccesses + ""
          }
          items.push(data);
        }
      }
      this._showNoData(false);
      ct.setExportData(response.items);
      return items;
    },

      _end: 0
    });

  });
