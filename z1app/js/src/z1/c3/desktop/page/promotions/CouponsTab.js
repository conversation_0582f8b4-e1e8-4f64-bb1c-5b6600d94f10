/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 *
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define(["dojo/_base/declare", "dojo/_base/lang", "dojo/on", "udc/udc", "udc/core/commons/loader",
  "z1/c3/desktop/start", "udc/core/rt/widgets/_BaseWidget", "dojo/dom-class",
  "dojo/text!z1/c3/desktop/page/templates/promotions/CouponsTab.html",
  "dojo/i18n!z1/c3/desktop/page/nls/CouponsTabRes",
  "z1/c3/desktop/page/util/C3Table2",
  "z1/common/C3Util",
  "dojo/topic",
  "dojo/dom-construct",
  "dojo/Deferred",
  "z1/c3/desktop/page/util/positionItMx",
  "z1/c3/utils/QueryMapper"
],
function(declare, lang, on, udc, loader,
  z1, BaseWidget, domClass, templateStr, Res, C3Table2, C3Util,
  topic, domConstruct, Deferred, positionItMx, QueryMapper
)
{
  /**
   * Creates a widget.
   */
  return declare([BaseWidget, positionItMx], {
    templateString: templateStr,
    Res: Res,
    isInView: true,
    secondaryTextList: {
        discount: /* "(% of Total)" */ "",
    },
    resObj: {
      hName: Res.hName,
      hDiscountedPurchases: Res.hDiscountedPurchases,
      hDiscount: Res.hDiscount,
      hDiscountShare: Res.hDiscountShare,
      hRevenue: Res.hRevenue,
      hRevenueShare: Res.hRevenueShare,
      hYield: Res.hYield,
      hDiscountRate: Res.hDiscountRate,
      hDiscountsHLB: Res.hDiscountsHLB,
      hDiscountsOTF: Res.hDiscountsOTF,
      hDiscountsULB: Res.hDiscountsULB
    },

    /**
     * Creates an instance of this page.
     *
     * @param {Object}
     * @param {Object}
     *          parentNode
     */
    constructor: function(params, parentNode)
    {
      this.inherited(arguments);
      params.fromDate = params.dtCtx.fromDt;
      params.toDate = params.dtCtx.toDt;
    },

    postCreate: function()
    {
      this.inherited(arguments);
    },

    /**
     * Fire a POST call to get all the user's selected channels from the backend.
     *
     * Initialize the data.
     */
    startup: function()
    {
      this.inherited(arguments);
      this._init();
      this._setEvents();
      this._subscribe();
    },

    //................................
    _init: function()
    {
      domConstruct.empty(this.content);
      this._fetchData();
    },

    _refresh: function (dObj)
    {
      this.fromDate = dObj.fromDt;
      this.toDate = dObj.toDt;
      this._init();
    },

    //.................................
    _fetchData: function()
    {
      var deferr = new Deferred();
      this._data = [];
      udc.showBusy(this._cont);

      let targetUrl = `c3/analytics/query?id=promotional_coupon&f=${this.fromDate}&t=${this.toDate}&qns=true`;
       loader.GET(targetUrl, {}).then((response) => {
        // handle empty response in caller
        this._data = response;
        udc.hideBusy();
        if(!response || !Array.isArray(response))
        {
          udc.log(response);
          udc.info(response);
          this._showNoDataDiv(true);
        }
        else if (response && response.length && response[0]?.values.length != 0)
        {
          if (
            response.length > 2 && 
            Array.isArray(response[0].values) &&
            Array.isArray(response[2].values) &&
            response[2].values.length > 0 &&
            typeof response[2].values[0] === 'object' &&
            typeof response[2].values[0].totalRevenue === 'number'
          )
          {
            const zero = response[2].values[0].totalRevenue === 0;
            response[0].values.forEach((value) =>
            {
              value.totalRevenueWithDiscountPercent = 
                (zero || typeof value.totalRevenueWithDiscount !== 'number') ?
                0 :
                (value.totalRevenueWithDiscount * 100 / response[2].values[0].totalRevenue)
            })
          }

          this._showNoDataDiv(false);
          this._showList();
        }
        else {
          this._showNoDataDiv(true);
        }
        deferr.resolve(true);
      })
      return deferr.promise;
    },

    _showNoDataDiv: function(flag)
    {
      domConstruct.empty(this._noDataDiv);
      if(flag)
      {
        const emptyStateCont = C3Util.createNoDataElmnt({ noDataLbl: Res.noDataFound });
        this._noDataDiv.appendChild(emptyStateCont);

        domClass.remove(this._noDataDiv, "c3_hide");
        domClass.add(this.content, "c3_hide");

        return;
      }
      domClass.add(this._noDataDiv, "c3_hide");
      domClass.remove(this.content, "c3_hide");
    },

    _setNoneAsFirst: function (d)
    {
      var r = [];

      d.forEach((e) => {
        if (e['couponBucket'] === 'none')
          r.unshift(e);
        else
          r.push(e);
      })
    
      return r;
    },

    // ..........................
    _showList: function ()
    {
      if(!this._data) this._data = [];
      
      this._data[1].values.forEach(el => {
        let findCp = this._data[0].values.find(x => x.couponBucket === el.couponBucket);
        findCp[el.eppLabel+"Discount"] = el.totalDiscount;
      });

      let mData = JSON.parse(JSON.stringify(this._data[0]['values']));
      this._mData = this._setNoneAsFirst(mData);
      let subTblMap = {
        align: "left",
        ignoreCase: true,
        isSortable: true,
        useDefaultSort: true,
      };

      var typeMap = {
        "name": {
          "type": "text_m2",
          "name_of_col": Res.hName,
          "clsD": "c3_lvTtl",
          ...subTblMap,
          "sortMethod": "textSort",
          "sortBy": "couponBucket"
        },
        "discountedPurchases": {
          "type": "text_m",
          "name_of_col": Res.hDiscountedPurchases,
          "clsD": "c3_lvTtl,c3_text_ellipsis",
          ...subTblMap,
          "sortMethod": "numberSort",
          "sortBy": "totalOrderCountWithDiscount"
        },
        "discount": {
          "type": "text_m",
          "name_of_col": Res.hDiscount,
          "clsD": "c3_lvTtl,c3_text_ellipsis",
          ...subTblMap,
          "sortMethod": "numberSort",
          "sortBy": "totalDiscount"
        },
        "revenue": {
          "type": "text_s4",
          "name_of_col": Res.hRevenue,
          "clsD": "c3_lvTtl,c3_text_ellipsis",
          ...subTblMap,
          "sortMethod": "numberSort",
          "sortBy": "totalRevenueWithDiscount"
        },
        "yield": {
          "type": "text_m",
          "name_of_col": Res.hYield,
          "clsD": "c3_lvTtl,c3_text_ellipsis",
          ...subTblMap,
          "sortMethod": "numberSort",
          "sortBy": "yield"
        },
        "discountRate": {
          "type": "text_m",
          "name_of_col": Res.hDiscountRate,
          "clsD": "c3_lvTtl,c3_text_ellipsis",
          ...subTblMap,
          "sortMethod": "numberSort",
          "sortBy": "discountRate"
        },
        "discountsHLB": {
          "type": "text_s4",
          "name_of_col": Res.hDiscountsHLB,
          "clsD": "c3_lvTtl,c3_text_ellipsis",
          ...subTblMap,
          "sortMethod": "numberSort",
          "sortBy": "HLBDiscount"
        },
        "discountsOTF": {
            "type": "text_s4",
            "name_of_col": Res.hDiscountsOTF,
            "clsD": "c3_lvTtl,c3_text_ellipsis",
            ...subTblMap,
            "sortMethod": "numberSort",
            "sortBy": "OTFDiscount"
        },
        "discountsULB": {
            "type": "text_s4",
            "name_of_col": Res.hDiscountsULB,
            "clsD": "c3_lvTtl,c3_text_ellipsis",
            ...subTblMap,
            "sortMethod": "numberSort",
            "sortBy": "ULBDiscount"
        },
      };
      // a generic "tool tip" for all the icons
      // singleton
      this.infoBx = this.infoBx || document.createElement("div");
      this.infoBx.classList.add(...["c3_hide", "c3_pd_6", "c3_br3", "c3_posA", "c3_minMax_w200", "c3-bg-c-gray100", "c3-c-gray0", "c3_min_wAuto"]);
      document.body.appendChild(this.infoBx);
      this.own(this.infoBx);
      // method that handles hiding the info box
      _hideInfoBx = () => {
        if (this.infoBx)
        {
          this.infoBx.textContent = "";
          this.infoBx.classList.add('c3_hide');
        }
      }
      this._handleScroll = (evt) => {
        _hideInfoBx();
        if (this._handleScroll && typeof this._handleScroll == 'function')
        {
          window.removeEventListener("scroll", this._handleScroll, true);
        }
      }
      _addSecondaryHeading = (ttlEl, subHeadingTxt = '') => {
        // Create heading text from ttlEl
        let heading = document.createElement('div');
        heading.textContent = ttlEl.textContent.replace(subHeadingTxt, '').trim();
        ttlEl.textContent = '';
        // Create a subheading element with given subHeadingTxt
        let subHeading = document.createElement('div');
        subHeading.textContent = subHeadingTxt;
        subHeading.classList.add(...["c3_fw400","c3-c-gray70"]);
        // Add a wrapper to make it appear one below other
        let newLineWrap = document.createElement('div');
        newLineWrap.classList.add('c3_cols_wrap');
        newLineWrap.append(heading, subHeading);
        ttlEl.prepend(newLineWrap)
        return ttlEl
      }

      _getTblHeadCtx = () => {
        let tblHCtx = {};
        if (typeMap)
        {
          Object.keys(typeMap).forEach((type) => {
            if (type != "name")
            {
              tblHCtx["_" + type + "Head"] = (key, el, ttlEl) => {
                ttlEl = (key in this.secondaryTextList) ? _addSecondaryHeading(ttlEl, this.secondaryTextList[key]) : ttlEl;
                let iconDiv = document.createElement('div');
                iconDiv.classList.add(...["c3_posR", "c3_pd_2v", "c3_pd-v_0b", "c3-c-gray80"]);
                let svgIcon = C3Util.useSvgDef("zi-info-outline");
                iconDiv.appendChild(svgIcon);
                ttlEl.appendChild(iconDiv);
                el.append(ttlEl);
                ttlEl.classList.remove("c3_rows_nowrap_start_center");
                ttlEl.classList.add("c3_rows_nowrap_start");
                // find first child (sort icon) and add 2v padding
                if(ttlEl.childNodes && ttlEl.childNodes[1] && ttlEl.childNodes[1].classList.contains('c3_lvCHSrtI')) ttlEl.childNodes[1].classList.add("c3_pd_2v");

                this.own(
                  on(iconDiv, "mouseover", (e)=> {
                    if (this.infoBx)
                    {
                      // change the "tool tip" content for each icon
                      this.infoBx.textContent = Res[type + "Info"];
                      this.infoBx.classList.remove('c3_hide');
                      this._positionIt3(this.infoBx, iconDiv);

                      // scroll event listener
                      window.addEventListener("scroll", this._handleScroll , true);
                    }
                  }),
                  on(iconDiv, "mouseout", (e)=> {
                    _hideInfoBx();
                  })
                );
              }
            }
          })
        }
        return tblHCtx;
      }
      let tblHeadCtx = _getTblHeadCtx();

      _showSubSlot = (d, el, k, fnl) => {
        let dDv = document.createElement("div");
        dDv.classList.add("c3_mrg_v-6b");
        dDv.textContent = fnl;
        el.appendChild(dDv);

        let val = `0.00%`;
        if (typeof d[k] === 'string')
        {
          val = d[k];
        }
        else if (typeof d[k] === 'number')
        {
          val = `${C3Util.getAbbrvDec_x2_xx1_xxx0(d[k])}%`;
        }

        let pDv = document.createElement("div");
        pDv.classList.add("c3-c-gray80");
        pDv.textContent = val;
        el.appendChild(pDv);
      }
      _isString = (val) => (typeof val === 'string') ? true : false

      var glryCtx = {
        _parent: this,
        cls: "c3_cpgnOvLv c3_lv2 c3_lvNW c3_lvNoSideBdr c3_lvNoBottomBdr c3_lvNoTopBdr",
        typeMap: typeMap,
        _mData: this._mData,
        generateId: true,
        _nameSlot: (d, el) => {
          let dvtl = document.createElement("div");
          dvtl.classList.add(...["c3_lvExpTtl", "c3_wordBreak", "c3_2l", "c3_clamp", "c3_t5"]);
          let ttlText = d.couponBucket === "none" ? Res.nameIfNone : d.couponBucket;
          dvtl.title = ttlText;
          dvtl.textContent = ttlText;
          el.append(dvtl);
        },
        _discountedPurchasesSlot: (d, el) => {
          el.classList.remove("c3_waitBx");
          d["totalOrderCountWithDiscount"] = d.totalOrderCountWithDiscount != void(0) ? d.totalOrderCountWithDiscount : 0;
          el.textContent = _isString(d.totalOrderCountWithDiscount) ? d.totalOrderCountWithDiscount : C3Util._numberWithCommas(d.totalOrderCountWithDiscount);
        },
        _discountSlot: (d, el) => {
          el.classList.remove("c3_waitBx");
          d["totalDiscount"] = d.totalDiscount != void(0) ? d.totalDiscount : 0;
          const finalVal = "$" + C3Util._numberWithCommas(C3Util.getAbbrvDec_x2_xx1_xxx0(d.totalDiscount));
          _showSubSlot(d, el, "totalDiscountPercent", finalVal);
        },
        _revenueSlot: (d, el) => {
          el.classList.remove("c3_waitBx");
          d["totalRevenueWithDiscount"] = d.totalRevenueWithDiscount != void(0) ? d.totalRevenueWithDiscount : 0;
          const finalVal = "$" + C3Util._numberWithCommas(C3Util.getAbbrvDec_x2_xx1_xxx0(d.totalRevenueWithDiscount));
          _showSubSlot(d, el, "totalRevenueWithDiscountPercent", finalVal);
        },
        _yieldSlot: (d, el) => {
          el.classList.remove("c3_waitBx");
          d["yield"] = d.yield != void(0) ? d.yield : 0;
          el.textContent = _isString(d.yield) ? d.yield : "$" + C3Util._numberWithCommas(d.yield, 2);
        },
        _discountRateSlot: (d, el) => {
          el.classList.remove("c3_waitBx");
          d["discountRate"] = d.discountRate != void (0) ? d.discountRate : 0;
          el.textContent = _isString(d.discountRate) ? d.discountRate : C3Util._numberWithCommas(d.discountRate, 2) + "%";
        },
        _discountsHLBSlot: (d, el) => {
          el.classList.remove("c3_waitBx");
          d["HLBDiscount"] = d.HLBDiscount != void (0) ? d.HLBDiscount : "N/A";
          let finalValue = d.HLBDiscount != "N/A" ? (d.HLBDiscount/d.totalDiscount*100) : "N/A"
          el.textContent = (finalValue != "N/A") && d.totalDiscount != 0 ? C3Util._abbreviatePercent(finalValue, 1) + "%" : "N/A";
        },
        _discountsOTFSlot: (d, el) => {
          el.classList.remove("c3_waitBx");
          d["OTFDiscount"] = d.OTFDiscount != void (0) ? d.OTFDiscount : "N/A";
          let finalValue = d.OTFDiscount != "N/A" ? (d.OTFDiscount/d.totalDiscount*100) : "N/A"
          el.textContent = (finalValue != "N/A") && d.totalDiscount != 0 ? C3Util._abbreviatePercent(finalValue, 1) + "%" : "N/A";
        },
        _discountsULBSlot: (d, el) => {
          el.classList.remove("c3_waitBx");
          d["ULBDiscount"] = d.ULBDiscount != void (0) ? d.ULBDiscount : "N/A";
          let finalValue = d.ULBDiscount != "N/A" ? (d.ULBDiscount/d.totalDiscount*100) : "N/A"
          el.textContent = (finalValue != "N/A") && d.totalDiscount != 0 ? C3Util._abbreviatePercent(finalValue, 1) + "%" : "N/A";
        },
         postCreateRowTbl: (rCtx) => {
          if (rCtx && rCtx.data && rCtx.data.couponBucket === 'none' && rCtx.node)
          {
            rCtx.node.classList.add(...["c3-none-coupon"]);
          }
        },
        _postSortCb: (name, isDesc) => {
          let noneCoupon = document.querySelector(".c3-none-coupon");
          let firstCh = noneCoupon.parentNode.firstChild;
          firstCh.after(noneCoupon);
        },
        _end: 0
      };

      if (tblHeadCtx) glryCtx = {...glryCtx, ...tblHeadCtx};
      this.listTable = new C3Table2(glryCtx);
      this.listTable.startup();
      domConstruct.place(this.listTable.domNode, this.content);
      this.own(this.listTable);
    },

    _prepareFilterDataToExport: function(data, str)
    {
      const getDecimal = (val) => {
        return Math.abs(val) < 10 ? 2 : (Math.abs(val) >= 100 ? 0 : 1);
      }
      let commas = "";
      const addCell = (val, comma = true) => {
        commas = comma ? "," : "";
        str += commas + val;
      }

      let discountedPurchases = data.totalOrderCountWithDiscount != void(0) ? data.totalOrderCountWithDiscount : 0;
      addCell(discountedPurchases, false);

      let discount = data.totalDiscount != void(0) ? data.totalDiscount : 0;
      addCell(discount);

      let discountShare = data.totalDiscountPercent != void(0) ? data.totalDiscountPercent : 0;
      addCell(discountShare);

      let revenue = data.totalRevenueWithDiscount != void(0) ? data.totalRevenueWithDiscount : 0;
      addCell(revenue);

      let revenueShare = data.totalRevenueWithDiscountPercent != void(0) ? data.totalRevenueWithDiscountPercent : 0;
      addCell(revenueShare);

      let yield = data.yield != void(0) ? data.yield : 0;
      addCell(yield);

      let discountRate = data.discountRate != void(0) ? data.discountRate : 0;
      addCell(discountRate);

      let discountHLB = data.HLBDiscount != "N/A" && data.totalDiscount != 0 ? (data.HLBDiscount/data.totalDiscount*100) : "N/A";
      addCell(discountHLB);

      let discountOTF = data.OTFDiscount != "N/A" && data.totalDiscount != 0 ? (data.OTFDiscount/data.totalDiscount*100) : "N/A";
      addCell(discountOTF);

      let discountULB = data.ULBDiscount != "N/A" && data.totalDiscount != 0 ? (data.ULBDiscount/data.totalDiscount*100) : "N/A";
      addCell(discountULB);

      return str;
    },

    _exportReports: function ()
    {
      // emptying the data variable to prevent stacked results
      let data = [];
      data =  this._data[0].values.length === 0 ? [] : this._mData;

      let headStr = Object.values(this.resObj).join(','), str = "", fData;

      if(data.length === 0) str = this.addBlankCSVFormat(Object.values(this.resObj), {});

      for (var i = 0; i < data.length; i++)
      {
        str = str + "\n" + data[i].couponBucket + ",";
        str = this._prepareFilterDataToExport(data[i], str);
      }

      // once the data is in csv donwload the csv as a file.
      this.downloadBtn.href = 'data:attachment/csv,' + encodeURI(headStr+str);
      var dt = (new Date()).getTime();
      var mDt = C3Util.MS_to_yyyymmdd_hhmm(dt);
      this.downloadBtn.download = 'Promotions_Coupons_' + this.fromDate + '-' + this.toDate + '_generatedon_' + mDt + '.csv';
      this.downloadBtn.click();
    },

    //.........................
    _subscribe: function()
    {
      if (this.tsSubscribeStr)
      {
        this.own(
          // Subscribe to changes in the time selector
          topic.subscribe(this.tsSubscribeStr, dObj => {
            // track whether this time change has been applied
            // when tab is not in view it will not apply time change
            // when user comes back to the tab it will check if the time
            // was previously applied, if not then it
            // will query with the new time range
            this.tsObj = {
              appliedTimeChange: false,
              dObj: dObj
            };
            // do not query on tab change unless previous time
            // change has not been applied
            if (this.isInView)
            {
              this._refresh(dObj);
              this.tsObj.appliedTimeChange = true;
            }
          })
        );
      }
      if (this.tabSubscribe && this.tabSubscribe.str)
      {
        this.own(
          // Subscribe to changes in the time selector
          topic.subscribe(this.tabSubscribe.str, tObj => {
            if (this.tabId != tObj.tabId) {
              this.isInView = false;
              return;
            }
            this.isInView = true;
            // on tab change query BE if last time change was not applied
            // to the data in this tab
            if (this.tsObj && this.tsObj.appliedTimeChange == false)
            {
              if (this.tsObj.dObj)
              {
                this._refresh(this.tsObj.dObj);
                this.tsObj.appliedTimeChange = true;
              }
            }
          })
        );
      }
      this.own(
        topic.subscribe("/interactions/refresh", lang.hitch(this, function()
        {
          this._init();
        }))
      );
    },

    //..........................................
    _setEvents: function()
    {
      // this.own();
    },

    // .................................................
    destroy: function()
    {
      this.inherited(arguments);
    },

    _end: 0
  });

});
