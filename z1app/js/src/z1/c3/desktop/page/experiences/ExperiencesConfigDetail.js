/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define([
  "dojo/_base/declare", 
  "dojo/_base/lang",
  "dojo/on",
  "udc/udc",
  "udc/core/commons/loader",
  "z1/c3/desktop/start",
  "udc/core/rt/widgets/_BaseWidget",
  "dojo/text!z1/c3/desktop/page/templates/experiences/ExperiencesConfigDetail.html",
  "dojo/i18n!z1/c3/desktop/page/nls/ExperiencesConfigDetailRes",
  "z1/c3/desktop/page/modules/ModuleDefineCtx",
  "z1/c3/desktop/page/modules/ModuleTemplate",
  "z1/c3/desktop/data/modules/ModulesData",
  "z1/c3/desktop/page/segments/C3NameDesc",
  "z1/c3/desktop/page/steps/C3Steps2",
  "z1/c3/desktop/data/eventSources/EventSources",
  "z1/c3/desktop/page/eventSources/EventSourcesSelect",
  "z1/c3/desktop/page/eventSources/EventSourcesRO",
  "z1/common/C3Util",
  "dojo/topic",
  "dojo/dom-class",
  "dojo/dom-construct",
  "dojo/Deferred",
  "dojo/promise/all",
  "z1/c3/utils/validator",
],
function(declare, lang, on, udc, loader, z1, BaseWidget, templateStr, Res,
  ModuleDefineCtx, ModuleTemplate, ModulesData, C3NameDesc, C3Steps2,
  EventSources, EventSourcesSelect, EventSourcesRO,
  C3Util, topic, domClass, domConstruct, Deferred, all, validator
)
{

  /**
   * Creates a widget to show details page.
   */
  return declare(BaseWidget,
  {
    templateString : templateStr,
    Res: Res,

    /**
     * Creates an instance of this page.
     * 
     * @param {Object}
     */
    constructor : function(params)
    {
      this.inherited(arguments);

      if( params && params.idSfx )
      {
        this._idSfx = params.idSfx;
      }
      else
      {
        this._idSfx = "_id_c3ExpCfgDet" + Math.ceil(Math.random()*100);
      }

      // custom class or class with no attributes.
      params.cls = params.cls || "";
      params.cls = params.cls + " c3_expCfgDet";

      if (!params.itemId && params._templateDetails && params._templateDetails.id)
      {
        params.itemId = params._templateDetails.id;
      }

      params.cmd = params.cmd || "new";

      params._noReset = false;
    },

    // ////////////////////////////////////////////////////////////////////////////
    // Implementations

    postCreate : function()
    {
      this.inherited(arguments);
    },

    // .....................................................
    startup : function()
    {
      this.inherited(arguments);

      this._initData();

      this._subscribe();
      this._setEvents();
    },

    // ..................................
    _reset: function()
    {
      this.populate();
    },

    //.........................................
    populate: function()
    {
      this._showTitle();
      this._showNameDesc();
      if(this.cmd !== "edit")
      {
        if (this._templateDetails &&
          ModulesData.api.isConfigurable(this._templateDetails))
        {
          this._showConfig();
        }
        else
        {
          // no config so remove related placeholders
          this._formCont.remove();
          this._sep1.remove();
        }
      }
      else
      {
        if (this.dataForSave && this.dataForSave.payload2 &&
          ModulesData.api.isConfigurable(this.dataForSave.payload2))
        {
          this._showConfig();
        }
        else
        {
          // no config so remove related placeholders
          this._formCont.remove();
          this._sep1.remove();
        }
      }
      this._showSteps();
      this._showEventSources();
    },
    
    //.........................................
    _initData: function()
    {
      var deferr = new Deferred();
      all([this._fetchDetail()]).then(lang.hitch(this, function (){
        this._usedIn = ModulesData.api.getExperienceType({
          _templateDetails: this._templateDetails
        }) || "campaign";

        this.populate();
        deferr.resolve(true);
      }));
      return deferr.promise;
    },

    //.........................................
    _fetchDetail: function()
    {
      var deferr = new Deferred();

      if (this._templateDetails)
      {
        // data passed from configure UI
        deferr.resolve(this._templateDetails);
        return deferr.promise;
      }

      ModulesData.api.fetchModuleArtifacts({id: this.itemId}).then(lang.hitch(this, function (data){
        this._templateDetails = data;
        deferr.resolve(data);
      }));
      return deferr.promise;
    },

    //.........................................
    _showTitle: function()
    {
      this._pgTitle.textContent = Res["pgTitle_" + this.cmd] + ": ";
      // set the name for this experience
      if (this.dataForSave && this.dataForSave.name)
      {
        this.expName = this.dataForSave.name;
      }
      else if (this._templateDetails.name)
      {
        this.expName = this._templateDetails.name.split(" ").join("_") + "" + C3Util.getRandomStr(2);
      }
      let pgSubTtl = document.createElement("span");
      pgSubTtl.classList.add("c3_pgCTtl")
      pgSubTtl.textContent = this.expName;
      this._pgTitle.appendChild(pgSubTtl);
      if (this._templateDetails.description)
      {
        let descrEl = document.createElement("span");
        descrEl.classList.add("c3_pgCDscr");
        descrEl.title = this._templateDetails.description;
        descrEl.textContent = this._templateDetails.description;
        this._pgTitle.appendChild(descrEl);
      }
    },

    _showNameDesc: function()
    {
      let nd;
      if(this.cmd !== "edit")
      {
        nd = {
          "nameHint": Res.unqSegNameHint,
          "descHint": Res.unqSegDescHint,
          "showCondensed": true,
          "_focused": true
        };
      }
      else
      {
        nd = {
          "nameHint": Res.unqSegNameHint,
          "descHint": Res.unqSegDescHint,
          "showCondensed": true,
          "_readOnlyName": true,
          "_focused": true
        };
      }
      nd.name = this.expName;
      if (this.dataForSave && this.dataForSave.description)
        nd.description = this.dataForSave.description;
      this._c3NameDesc = new C3NameDesc(nd);
      this._c3NameDesc.startup();
      domConstruct.place(this._c3NameDesc.domNode, this._nmdscBox);
      this.own(this._c3NameDesc);
    },

    //....................................
    _showSteps: function ()
    {
      domConstruct.empty(this._stepsCont);

      let sCtx = {
        _parent: this,
        stepsCtx: [
          {
            title: "Configure Template",
            cls: "c3_steps2Sel"
          },
          {
            title: "Customize Messages",
          },
          {
            title: "Review Experience",
          }
        ],
        //onStepClickCb: lang.hitch(this, "_showSection")
        isNotClickable: true,
        hideStepNumber: true
      };

      this.steps2 = new C3Steps2(sCtx);
      this.steps2.startup();
      domConstruct.place(this.steps2.domNode, this._stepsCont);
      this.own(this.steps2);
    },

    //.........................................
    _showSection: function (num)
    {
      var sxnCount = (this.steps && !isNaN(this.steps.stepsCount)) ?
          this.steps.stepsCount : 4;
      this._sxnNum = (isNaN(num) || num < 1 || num > sxnCount) ? 1 : num;

      //var sxns = this.dialogBody.querySelectorAll('.c3_cpgn_bsxn');
      //sxns.forEach(function(sx){
      //  sx.classList.add('c3_hide');
      //});
      //if(this['_sxn' + this._sxnNum + 'Cont'])
      //{
      //  this['_sxn' + this._sxnNum + 'Cont'].classList.remove('c3_hide');
      //}
      // steps
      if(this.steps2){
        this.steps2.selectedStep({
          _selectedStepNum: this._sxnNum
        });
      }
    },

    //...........................................
    _showEventSources: function ()
    {
      if (!EventSources.api.isEventMappingAllowed({_templateDetails: this._templateDetails}))
      {
        if (this._eventSourcesCont) this._eventSourcesCont.remove();
        return;
      }

      // find out if eventSource is read only
      let esProp = ModulesData.api.getMainArtifactEventSourceProp({
        _templateDetails: this._templateDetails
      });
      // "any" - default event source
      // If no event source is specified then use "any"
      //let defaultValue = EventSources.api.ANY_SOURCES;
      let defaultValue = [];
      let ro = false;
      if (esProp)
      {
        if (Array.isArray(esProp.value) && esProp.value.length)
        {
          defaultValue = esProp.value;
        }
        if (esProp.readonly === "true")
        {
          ro = true;
        }
      }
      this._showEventSourcesSelect({readOnly: ro, defaultValue: defaultValue});
    },

    //...........................................
    _showEventSourcesSelect: function (ctx = {readOnly: false})
    {
      if (this._eventSourcesSelect)
      {
        this._eventSourcesSelect.destroy();
      }
      var eCtx = {
        _parent: this,
        _titleCls: "c3_t4",
        _descrCls: "c3_fldDescr",
        _hideDescription: true,
        required: true,
        // guided event sources
        // true for modules
        _isGuided: true,
        // allow only one selection
        _singleSelect: true,
        moduleId: this.itemId,
        // only for TI in experience
        type: "campaign"
      };
      if (ctx.readOnly)
      {
        eCtx = {...eCtx, ...{
          cls: "c3_eSrcsModPrv"
        }};
      }
      else
      {
        eCtx = {...eCtx, ...{
          cls: "c3_eSrcsMod",
          onChange: (v) => {
          }
        }};
      }
      // DEBUG - defaultValue
      //ctx.defaultValue = ["mobileWeb"];
      // default event sources specified in module
      eCtx.defaultValue = (Array.isArray(ctx.defaultValue) && ctx.defaultValue.length) ?
          ctx.defaultValue : [];
      if (this.dataForSave && this.dataForSave.payload2 &&
        Array.isArray(this.dataForSave.payload2.eventSource))
      {
        eCtx.value = this.dataForSave.payload2.eventSource;
      }
      // default value
      // default values are setup and assigned
      // in eventSources.js
      this._eventSourcesSelect = void(0);
      if (ctx.readOnly)
        this._eventSourcesSelect = new EventSourcesRO(eCtx);
      else
        this._eventSourcesSelect = new EventSourcesSelect(eCtx);
      this._eventSourcesSelect.startup();
      domConstruct.place(this._eventSourcesSelect.domNode, this._eventSourcesCont);
      this.own(this._eventSourcesSelect);
    },

    /*
     * Pass the artifacts data and the massaged rule/behavior payload to ModuleDefineCtx
     */
    _showConfig: function()
    {
      if (!this._templateDetails) return;
      
      // reset form - ModuleTemplate
      this._form = void(0);
  
      // dynamically load form based on trigger type
      this.initTemplate();
    },
    
    /*
     * Initialize ModuleTemplate 
     * 
     * This class will get the data from ModuleDefineCtx and act on it to paint the page
     * It will work with ModuleDefineCtx to collect valid data from the user input
     */
    initTemplate: function()
    {
      var pObj = {
        _parent: this,
        cmd: this.cmd,
        _usedIn: this._usedIn
      };
      this._form = new ModuleTemplate(pObj);
      this._form.startup();
      domConstruct.place(this._form.domNode, this._formCont);
      this.own(this._form);
    },

    _isValidNameDesc: function()
    {
      var nData = this._c3NameDesc.getData();
      
      // use default tag name validation
      if (!validator.isValid({
        vMethodName: "isValidExperienceName",
        vParam: nData.name
      }))
      {
        udc.log("Invalid name: ", nData.name);
        return false;
      }

      // description
      if (!validator.isValid({
        vMethodName: "isValidDescription",
        vParam: nData.description,
        vErrorMessage: Res.invalidDescription
      }))
      {
        udc.log("Invalid description: ", nData.description);
        return false;
      }
      
      return true;
    },

    _validation: function ()
    {
      if(!this._isValidNameDesc()) return false;

      if (this._eventSourcesSelect && this._usedIn !== "campaignonce" &&
        typeof this._eventSourcesSelect.isValid === "function" &&
        !this._eventSourcesSelect.isValid())
      {
        return false;
      }

      return true;
    },

    /* 
     * Get the data from ModuleDefineCtx
     */
    _prepareForSave: function()
    {
      if(!this._validation()) return;
      
      var nData = this._c3NameDesc.getData();
      var eventSourceObj = {};
      if (this._eventSourcesSelect && this._usedIn !== "campaignonce")
      {
        eventSourceObj.eventSource = this._eventSourcesSelect.getData();
      }
      ModuleDefineCtx.mData._preparePayloadForSave({
        nData: nData,
        eventSourceObj: eventSourceObj
      }).then(lang.hitch(this, function(data){

        var payload = data;
        payload.payload2 = data.payload2 || {};
        payload.payload2 = {...payload.payload2, ...eventSourceObj};
        payload = {...payload, ...nData};

        this._noReset = true;
        if (!this._inlineXpC)
        {
          z1.fireEvent(z1.Events.showExperiencesModTest, {
            itemId: this.itemId || this._templateDetails.id,
            _templateDetails: this._templateDetails,
            dataForSave: payload,
            cmd: this.cmd,
            _isJDataSet: this._isJDataSet,
            _rCtx: this._rCtx
          });
        }
        else
        {
          if (!this._expModTest)
          {
            udc.showBusy(this._xpContNode);

            require(["z1/c3/desktop/page/experiences/ExperiencesModTesting"],
              (ExperiencesModTesting) => {
              this._expModTest = new ExperiencesModTesting({
                itemId: this.itemId || this._templateDetails.id,
                _templateDetails: this._templateDetails,
                dataForSave: payload,
                cmd: this.cmd,
                _isJDataSet: this._isJDataSet,
                _rCtx: this._rCtx,
                _inlineXpC: this._inlineXpC,
                _xpContNode: this._xpContNode,
                _exitPublishEvt: this._exitPublishEvt
              });
              this._expModTest.startup();
              domConstruct.place(this._expModTest.domNode, this._xpContNode, 'only');
              this.own(this._expModTest);

              udc.hideBusy();
            });
          }
        }
      }), lang.hitch(this, function(err){
        // NOOP
      }));
    },
    
    //.........................................
    _onCancel: function ()
    {
      if (this.cmd != "edit")
      {
        //z1.fireEvent(z1.Events.showExperiencesGallery, {});
        z1.fireEvent(z1.Events[ModulesData.api.detailEventMap[this._templateDetails.type].fireEvent], {
          itemId: this._templateDetails.id
        });
      }
      else
      {
        if (!this._inlineXpC)
        {
          var params = {
            "itemId": this._templateDetails.id,
            "_templateDetails": this._templateDetails,
            dataForSave: this.dataForSave,
            cmd: this.cmd,
            _isJDataSet: true
          };
          Object.assign(params, this._rCtx);
          z1.fireEvent(z1.Events[ModulesData.api.editDetailEventMap[this._templateDetails.type].fireEvent], params);
        }
        else
        {
          if (!this._expInstDet)
          {
            udc.showBusy(this._xpContNode);

            require(["z1/c3/desktop/page/experiences/ExpInstanceDetail"],
              (ExpInstanceDetail) => {
              this._expInstDet = new ExpInstanceDetail({
                "itemId": this._templateDetails.id,
                "_templateDetails": this._templateDetails,
                dataForSave: this.dataForSave,
                _isJDataSet: true,
                _rCtx: this._rCtx,
                _inlineXpC: this._inlineXpC,
                _xpContNode: this._xpContNode,
                _exitPublishEvt: this._exitPublishEvt,
                hideMenuBar: true,
                cmd: "edit",
                ...this._rCtx
              });
              this._expInstDet.startup();
              domConstruct.place(this._expInstDet.domNode, this._xpContNode, 'only');
              this.own(this._expInstDet);

              udc.hideBusy();
            });
          }
        }
      }
    },

    //.........................................
    _setEvents: function()
    {
      this.own(
        // cancel
        on(this._cancelBtn, "click", lang.hitch(this, function(){
          this._onCancel();
        })),
        // next
        on(this._nextBtn, "click", lang.hitch(this, function(){
           this._prepareForSave();
        }))
      );
    },
    //.........................................
    _clearData: function()
    {
      domConstruct.empty(this._nmdscBox);
      domConstruct.empty(this._formCont);
    },
    
    //.........................................
    _subscribe: function()
    {
      this.own(
        // refresh
        topic.subscribe("/interactions/refresh", lang.hitch(this, function()
        {
          this._reloaded = true;
          this._clearData();
          this._reset();
        }))
      );
    },

    //.....................................................
    destroy: function()
    {
      // clear data
      if (!this._noReset)
      {
        ModuleDefineCtx.mData.reset();
      }

      this.inherited(arguments);
    },

    _end : 0
  });
  

});
