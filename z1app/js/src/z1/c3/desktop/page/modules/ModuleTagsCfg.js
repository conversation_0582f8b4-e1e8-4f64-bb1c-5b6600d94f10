/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define([
  "dojo/_base/declare", 
  "dojo/_base/lang",
  "dojo/on",
  "udc/udc",
  "udc/core/commons/loader",
  "z1/c3/desktop/start",
  "dijit/registry",
  "udc/core/rt/widgets/_BaseWidget",  
  "dojo/text!z1/c3/desktop/page/templates/modules/ModuleTagsCfg.html",
  "dojo/i18n!z1/c3/desktop/page/nls/ModuleTagsCfgRes",
  "z1/c3/desktop/page/taglistbox/C3TagListBox",
  "z1/c3/desktop/page/fields/C3MultiSelect",
  "dojo/dom-style",
  "dojo/dom-construct",
  "dijit/Dialog",
  "dojo/dom-class",
  "dojo/Deferred",
  "z1/c3/utils/validator",
  "z1/common/C3Util",
],
function(declare,
  lang, on, udc, loader, z1, registry, BaseWidget, templateStr, Res,
  TagListBox, C3MultiSelect, style, domConstruct, Dialog,
  domClass, Deferred, validator, C3Util
)
{

  /**
   * Creates a widget to show C3 landing page.
   */
  return declare(BaseWidget,
  {
    templateString : templateStr,
    
    Res: Res,
    
    Events: {},
    
    EXPERIENCE_TYPE: {
      "campaign": "Triggered",
      "c1": "Scheduled"
    },
    
    TAG_CATEGORIES:{
      "devicesdk":"Device SDK",
      "devicetype":"Device Type"
    },
    
    /**
     * Creates an instance of this page.
     * 
     * @param {Object}
     * @param {Object}
     *          parentNode
     */
    constructor : function(params, parentNode)
    {
      this.inherited(arguments);

      params._customTags = [];
      this._tagData = {};
    },

    // ////////////////////////////////////////////////////////////////////////////
    // Implementations

    postCreate : function()
    {
      this.inherited(arguments);

    },

    // .....................................................
    startup : function()
    {
      this.inherited(arguments);
      this.populate();
      this._setEvents();
    },

    // ....................................
    populate: function ()
    {
      this._showOfferTemplate();
      this._showTagListBox();
      this._showCustomTags();
    },

    _showOfferTemplate: function()
    {
      let toggle = (this.offerTemplate && this.offerTemplate.toLowerCase() === "offer")
      ? "fa-toggle-off" : "fa-toggle-on";

      this._dpTypeOfferBtn.classList.add(toggle);
      this._toggleButton(this._dpTypeOfferBtn);
    },

    // ....................................
    _showTagListBox : function()
    {
      let targetUrl = "c3/data/modules/filter?type=" + this.type;
      this._tagbox = new TagListBox({
        "tagsIconClass": "fa-tag",
        "targetUrl": targetUrl,
        "placeholderTags": Res.searchText,
        "_validation": (vTag) => {
          return validator.isValidOrShowAlert({
            vParam: vTag,
            vMethodName: "isValidModuleCustomTag",
            vSilent: true
          });
        },
        "_fetchTags" : function(tCtx = {})
        {
          var deferr = new Deferred();
          loader.GET(targetUrl, {}).then(lang.hitch(this, function(data)
          {
            if (data && data.custom && data.custom.values
              && Array.isArray(data.custom.values)
              && data.custom.values.length)
            {
              // 'custom' properties values are
              // used for populating tag widget's auto-complete
              data = data.custom.values;
            }
            deferr.resolve(data);
          }), lang.hitch(this, function(err)
          {
            C3Util.handleServerError(err);
            deferr.reject(err);
          }));
          return deferr.promise;
        }
      });
      this._tagbox.placeAt(this.tagHolder);
      this._tagbox.setParent(this);
      this.own(this._tagbox)
    },

    // ....................................
    _showCustomTags : function()
    {
      this.pObjList = [];
      var deferr = new Deferred();
      this._fetchCustomTags().then(lang.hitch(this, function(data){
        // we don't want the group options to show in the popup for action templates. 
        // The non in-app message widgets shouldn't show the options deleted.
        // Do not show Experience Type tag - this is selected based on subtype provided by ModuleSaveAs
        if (this.type == "actiontemplate")
        {
          delete data.group;
          if (!["push", "fullscreen", "banner", "alert", "appbox"].includes(this.group.toLowerCase().replace(/\s/g, '')))
          {
            delete data[this.TAG_CATEGORIES.devicesdk];
            delete data[this.TAG_CATEGORIES.devicetype];
          }
          // if the group is "push" then do not allow users to select HTML5
          if (this.group.toLowerCase().replace(/\s/g, '') == "push")
          {
            // look for "HTML5" which is the raw text sent by BE
            let idx = data[this.TAG_CATEGORIES.devicesdk].indexOf("HTML5");
            if (idx > -1) data[this.TAG_CATEGORIES.devicesdk].splice(idx, 1);
          }
        }
        for (let row in data)
        {
          if (row !== "Add custom tag" && row !== "Experience Type" && row !== "Experience Status")
          {
            var newObj = {
              _parent: this,
              placeholder: Res.multiSelectPlaceholder,
              dispName: row,
              multiSelectField: row,
              optionsList: this._transformUtil(data[row])
            }
            this.pObjList.push(newObj);
          }
        }
        this._populateTags();
        deferr.resolve(data);
      }));
      return deferr.promise;
    },

    // ....................................
    _fetchCustomTags : function()
    {
      var deferr = new Deferred();
      var targetUrl = "c3/data/modules/filter?type=" + this.type;
      loader.GET(targetUrl, {}).then(lang.hitch(this, function(data)
      {
        // Note, do not add anything here
        deferr.resolve(data);
      }), lang.hitch(this, function(err)
      {
        C3Util.handleServerError(err);
        deferr.reject(err);
      }));
      return deferr.promise;
    },

    // ....................................
    _populateTags: function()
    {
      if (!Array.isArray(this.pObjList)) return;

      this.pObjList.forEach((pObj) => {
        this._createMultiSelectTags(pObj);
      })
    },

    // ....................................
    _createMultiSelectTags: function(ctx)
    {
      let dv = document.createElement("div");
      dv.classList.add(...["c3_cols_nowrap", "c3_cols_spacing"]);
      let lbl = document.createElement("div");
      lbl.classList.add(...["c3_lbl"]);
      lbl.textContent = ctx.dispName;
      dv.appendChild(lbl);
      let desc = document.createElement("div");
      desc.classList.add(...["c3_fldDescr"]);
      desc.textContent = Res[ctx.multiSelectField + "Desc"];
      dv.appendChild(desc);
      let dvFld = document.createElement("div");
      dvFld.classList.add(...["c3_cols_nowrap"]);

      ctx.noIndentation = true;
      this["fldSelect_" + ctx.multiSelectField] = new C3MultiSelect(ctx);
      this["fldSelect_" + ctx.multiSelectField].startup();
      if (this.type == "actiontemplate")
      {
        if (ctx.dispName == this.TAG_CATEGORIES.devicesdk)
        {
          if (this.group.toLowerCase().replace(/\s/g, '') != "push") this["fldSelect_" + ctx.multiSelectField].setValue("Android|iOS|HTML5");
          else this["fldSelect_" + ctx.multiSelectField].setValue("Android|iOS");
        }
        else if (ctx.dispName == this.TAG_CATEGORIES.devicetype)
        {
          lbl.textContent += " (Optional)";
        }
      }
      dvFld.appendChild(this["fldSelect_" + ctx.multiSelectField].domNode);
      this.own(this["fldSelect_" + ctx.multiSelectField]);

      dv.appendChild(dvFld);
      this.multiCustomCont.appendChild(dv);
    },

    // ....................................
    _getData: function()
    {
      this.pObjList.forEach((pObj) => {
        if (this.type != "actiontemplate" && !this["fldSelect_" + pObj.multiSelectField].getData())
        {
          this._tagData[pObj.multiSelectField] = [];
        }
        else
        {
          this._tagData[pObj.dispName] = this["fldSelect_" + pObj.multiSelectField].getData().split("|");
        }
      })
      this._tagData["custom"] = this._tagbox.getTagValues() || "";
      for (var tag in this._tagData)
      {
        this._tagData[tag].forEach(function(part, index, theArray) {
          theArray[index] = {"value": part};
        });
      }
      if (this.subtype != void(0))
      {
        let part = this.EXPERIENCE_TYPE[this.subtype];
        if (part != void(0)) this._tagData["Experience Type"] = [{"value": part}];
      }

      return this._tagData;
    },

    _getTemplateType: function()
    {
      return this._dpTypeOfferBtn.classList.contains("fa-toggle-on")
      ? "offer" : "";
    },

    // ....................................
    _transformUtil: function(values)
    {
      var transformed = [];
      values.forEach((val) => transformed.push({"value": val, "name": val}));
      return transformed;
    },
  
    // ....................................
    _validate: function(values)
    {
      var _isValid = true;
      var _invalidFields = [];
      if (this.type == "actiontemplate")
      {
        if (this._tagData[this.TAG_CATEGORIES.devicesdk] && this._tagData[this.TAG_CATEGORIES.devicesdk].length > 0 && this._tagData[this.TAG_CATEGORIES.devicesdk][0] && this._tagData[this.TAG_CATEGORIES.devicesdk][0].value == "")
        {
          _isValid = false;
          _invalidFields.push("devicesdk");
        }
      }
      else
      {
        Object.keys(this._tagData).forEach((tag) => {
          if (tag!== "custom" && (!this._tagData[tag] || this._tagData[tag].length === 0))
          {
            _isValid = false;
            _invalidFields.push(tag);
          }
        });
      }
      return {"isValid": _isValid, "invalidFields": _invalidFields};
    },
  
    // ....................................
    setParent: function(parent)
    {
      this._parentDijit = parent;
    },

    // ....................................
    getParent: function()
    {
      return this._parentDijit;
    },

    // ....................................
    setCallback: function(cb)
    {
      this._cb = cb;
    },

    // ....................................
    getCallback: function()
    {
      return this._cb;
    },

    // ....................................
    setCancelCallback: function(cb)
    {
      this._cbCancel = cb;
    },

    // ....................................
    getCancelCallback: function()
    {
      return this._cbCancel;
    },

    _toggleButton: function(btn)
    {
      if (btn?.classList.contains("fa-toggle-off"))
      {
        btn.classList.add("fa-toggle-on");
        btn.classList.remove("fa-toggle-off");
        btn.dataset.toggle = "on";
        return;
      }

      btn.classList.add("fa-toggle-off");
      btn.classList.remove("fa-toggle-on");
      btn.dataset.toggle = "off";
    },

    // ....................................
    _setEvents: function()
    {
      this.own(
        on(this._dpTypeOfferBtn, "click", () => {
          this._toggleButton(this._dpTypeOfferBtn);
        })
      )
    },

    // ....................................
    destroy: function()
    {
      this.inherited(arguments);
    },

    _end : 0
  });
  

});