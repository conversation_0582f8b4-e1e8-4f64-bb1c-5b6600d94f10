define(
[
  "udc/udc", "udc/core/commons/loader", "dojo/_base/lang",
  "dojo/topic",
  "dojo/Deferred",
  "dojo/promise/all",
  "z1/c3/desktop/data/C3Context",
  "dojo/i18n!z1/c3/desktop/page/nls/ActionDefineCtxRes",
  "z1/c3/desktop/data/ContextAttributes",
  "z1/c3/desktop/data/modules/ModulesData",
  "z1/c3/desktop/data/content/ContentData",
  "z1/common/C3Util",
  "z1/c3/utils/validator"
],
function(udc, loader, lang, topic, Deferred, all, C3Context,
  Res, ContextAttributes, ModulesData, ContentData, C3Util, validator
)
{

  /**
   * SAMPLE PAYLOAD FOR AN ACTION
   * {  
   *  "ref":"udc.system.core.ZineOne Processing:MobileAppBannerAction",
   *  "name":"Banner1",
   *  "dimension":"mobile/message",
   *  "params":[  
   *     {  
   *        "name":"z1_template_ref",
   *        "value":"udc.system.core.ActionTemplateOOTB:banner:b_blank:1.0"
   *     },
   *     {  
   *       // HERE "tickerText" is the content name returned by _getTemplateParamContentName
   *       "name":"z1_template_param",
   *        "value":"tickerText=?{z1_content}|backgroundcolor=#ffffff|fontcolor=#000000"
   *     },
   *     // OR 
   *     //{
   *     //  "name": "z1_content_text",
   *     //  "value": "Suit l'amour, l'amour fuit.  Fuit l'amour, l'amour suit!!!"
   *     //},
   *     {  
   *        "name":"z1_content_ref",
   *        "value":"ea3585bf-b00b-4956-af67-5ef42dbc1531"
   *     },{
   *        "name": "z1_template_custom",
   *        "value": "custom_action_type=deeplink|custom_action_value=12l23los-soejfo-skjolfjd-djfojl"
   *    },{
   *        "name": "z1_template_properties",
   *        "value": "_z1_os=android,ios"
   *    }
   *  ],
   *  "selector":[],
   *  "nextSession":false
   * }
   */
  
  // Constants
  var consts = {

  };

  var CONTROLS = {
    "CONTENT": "z1_contenttext",
    "SELECT": "z1_select",
    "TEXT": "z1_text",
    "TAGS": "z1_tags",
    "COLOR": "z1_color",
    "IMAGE": "z1_image"
  };
  
  var PARAMS = {
    "TEMPLATE_REF": "z1_template_ref",
    "TEMPLATE_PARAM": "z1_template_param",
    "CONTENT_REF": "z1_content_ref",
    "CONTENT_TEXT": "z1_content_text",
    "CUSTOM_DATA": "z1_template_custom",
    "PROPERTIES": "z1_template_properties",
    "RAW_DATA": "rawData",
    "SMS_DATA": "_z1_message",
    "ARTICLE_TITLE": "listTitle",
    "ARTICLE_TAGLIST": "tags"
  };
  
  var ActionCtx = {

    // Constants
    consts: consts,
    
    // Controls
    CONTROLS: CONTROLS,

    // Params
    PARAMS: PARAMS,
    
    //Actions Data
    aData: {

      _originalCtx: {},
      // if editing an existing action
      // then keep the original action state
      // It is null for new action
      _prevAction: null,
      // Actions List Data 
      // actions, _actionIndex
      _dItem: {},
      _existingActions: null,
      _actionInEdit: null,
      
      // Action item data
      // the action that is being defined by this instance of the ActionTemplatePopup 
      _aItem: {},

      // Selected Template and its Style and Content Structures
      _aTemplate: null,
      _aStyle: null,
      _aContent: null,
      _actionType: null,

      // reset selected items
      _selectedName: null,
      _selectedType: null,
      _selectedTemplate: null,
      _selectedConfig: null,
      _selectedContent: null,
      _selectedCustomData: null,
      _definedRawData: null,
      _definedSMSData: null,
      _definedArticleData: null,

      // reset selected item index
      _actionIndex: 0,
      
      //............................
      // Reset all data.
      // - call when the parent is opened.
      // - call when the caller is closed
      // @params {object} [optional]
      //  _typeChanged: true - selected action
      //   type changed. Clear previously
      //   selected action type related data
      resetCtx: function (ctx = {})
      {
        if (!ctx._typeChanged)
        {
          this._originalCtx = {};
          this._prevAction = null;
          this._dItem = {};
          this._existingActions = null;

          // reset selected item index
          this._actionIndex = 0;
        }

        this._actionInEdit = null;

        this._aItem = {};
        
        this._aTemplate = null;
        this._aStyle = null;
        this._aContent = null;
        this._actionType = null;

        this._z1TemplateRef = void(0);
        this._z1TemplateParam = void(0);
        this._z1TemplateCustom = void(0);
        this._z1Content = void(0);
        this._z1Ref = void(0);
        
        // reset selected items
        this._selectedName = null;
        this._selectedType = null;
        this._selectedTemplate = null;
        this._selectedConfig = null;
        this._selectedContent = null;
        this._selectedCustomData = null;
        this._definedRawData = null;
        this._definedSMSData = null;
        this._definedArticleData = null;
      },

      // ................................................
      // Track items that are needed for functionality
      // ................................................
      setActionType: function(type)
      {
        if (!type) return;
        this._actionType = type;
      },
      
      getActionType: function()
      {
        return this._actionType; 
      },
      
      setActionTemplate: function(template)
      {
        if (!template)
        {
          this._aTemplate = void(0);
          return;
        }
        if (this._aTemplate && JSON.stringify(this._aTemplate) == JSON.stringify(template))
        {
          // not changed
          return;
        }
        this._aTemplate = template;
      },
      
      getActionTemplate: function()
      {
        return this._aTemplate;
      },
      
      // ................................................
      // Set items to current action for saving
      // ................................................
      // Writes to current action
      // actions[n].name
      // Input name [string]
      setActionName: function(name)
      {
        if (!name) return;
        this._aItem.name = name.trim();
      },

      // Writes to current action 
      // actions[n].selector
      // Input sel [string]
      setActionSelector: function(sel)
      {
        if (!sel) return;
        this._aItem.selector = sel;
      },
      
      // Writes to current action
      // actions[n].nextSession
      // Input flag [Boolean]
      setActionSession: function(flag)
      {
        flag = flag || false;
        this._aItem.nextSession = flag;
      },
      
      // Writes to current action
      // actions[n].dimension
      // Input dim [string]
      setActionDim: function(dim)
      {
        if (!dim) return;
        this._aItem.dimension = dim;
      },
      
      // Writes to current action
      // actions[n].ref
      // Input ref [string]
      setActionRef: function(ref)
      {
        if (!ref) return;
        this._aItem.ref = ref;
      },
      
      // Writes to current action
      // params [{...},{...}...] > z1_template_ref: [string] "actiontemplate+banner:b_ticker:1.0"
      // Input templateId [string]
      setActionTemplateRef: function(templateId)
      {
        if (!templateId || templateId == "") return;
        
        if (!this._aItem.params) this._aItem.params = [];
        
        var index = this._getIndexOfItemInParams(PARAMS.TEMPLATE_REF, this._aItem);
        
        this._aItem.params[index] = {
          "name": PARAMS.TEMPLATE_REF,
          "value": templateId
        };
      },
      
      // Writes to current action
      // params [{...},{...}...] > z1_template_param: [string] "backgroundcolor=#ffffff|fontcolor=#000000"
      // Input style [string] - a key value pair string "backgroundcolor=#ffffff"
      // Input is a pipe separated string that cannot start with a pipe
      // Takes a string and replaces the existing string with the new value
      setActionStyle: function(style)
      {
        if (style == void(0)) return;
        
        if (!this._aItem.params) this._aItem.params = [];
        
        var index = this._getIndexOfItemInParams(PARAMS.TEMPLATE_PARAM, this._aItem);
        
        this._aItem.params[index] = {
          "name": PARAMS.TEMPLATE_PARAM,
          "value": style
        };
      },
      
      // Writes to current action
      // params [{...},{...}...] > z1_content_ref OR z1_content_text
      // Input contentName [string] "z1_content_ref" || "z1_content_text"
      // Input contentValue [string] "ea3585bf-b00b-4956-af67-5ef42dbc1531" || "Sample Text"
      setActionContent: function(contentName, contentValue)
      {
        if (!contentName || contentName == "" 
          || !contentValue || contentValue == "") return;
        
        if (!this._aItem.params) this._aItem.params = [];
        
        // check if action has a content ref or text
        // use the provided contentName to find the other content name
        // if contentName is "z1_content_ref" then otherCN is "z1_content_text"
        var otherCN = PARAMS.CONTENT_REF == contentName ? PARAMS.CONTENT_TEXT : PARAMS.CONTENT_REF;
        
        var iCN = this._getIndexOfItemInParams(contentName, this._aItem);
        var iOCN = this._getIndexOfItemInParams(otherCN, this._aItem);
        
        var index = iCN; 
        // Only 2 options at this point
        // 1. neither exists in params
        //if (iCN == iOCN) index = iCN;
        if (iOCN >= 0 && iOCN < this._aItem.params.length)
        {
          // 2. the other CN exists in params
          // if iOCN is == this._aItem.params.length then it doesn't exist - no need to check this
          var iOCN_item = this._aItem.params[iOCN];
          // if the other content name was found in params then use that index 
          // and override both name and value
          if (iOCN_item != void(0) && iOCN_item.name == otherCN) index = iOCN;
        }
        
        this._aItem.params[index] = {
          "name": contentName,
          "value": contentValue
        };
        
        // get the name of the structure object whose id = "z1_contenttext" 
        var templateParamContentName = this._getTemplateParamContentName();
        if (templateParamContentName)
        {
          var c_param = templateParamContentName + "=?{z1_content}";
          // find the style param
          var index_style = this._getIndexOfItemInParams(PARAMS.TEMPLATE_PARAM, this._aItem);
          
          // if not found
          // create style param with templateParamContentName=?{z1_content}
          if (index_style == this._aItem.params.length)
          {
            // did not find style param
            this._aItem.params[index_style] = {
              "name": PARAMS.TEMPLATE_PARAM,
              "value": c_param
            };
          }
          else
          {
            // if available then check if this string already includes templateParamContentName=?{z1_content}
            // if it doesn't include that then append templateParamContentName=?{z1_content}
            // this protects against indecisive content creation when a user toggles back and forth multiple times
            // before deciding on the content
            var t_paramObj = this._aItem.params[index_style];
            if (t_paramObj)
            {
              // TODO: 
              // check if value includes ?{z1_content} - split the string by | and remove the item that has it if the item is not equal to c_param
              if (!t_paramObj.value.includes(c_param))
              {
                t_paramObj.value += "|" + c_param;
              }
            }
          }
        }
      },
      
      // Writes to current action
      // params [{...},{...}...] > z1_template_custom: [string] "custom_action_type=deeplink|custom_action_value=12l23los-soejfo-skjolfjd-djfojl"
      // Input style [string]
      // Input can also be a pipe separated string but cannot start with a pipe
      // Takes the full string and replaces the existing string
      setActionCustomData: function(customData)
      {
        if (customData == void(0)) return;
        
        if (!this._aItem.params) this._aItem.params = [];
        
        var index = this._getIndexOfItemInParams(PARAMS.CUSTOM_DATA, this._aItem);
        
        this._aItem.params[index] = {
          "name": PARAMS.CUSTOM_DATA,
          "value": customData
        };
      },
      
      addActionCustomAxn: function(type, value)
      {
        if (type == void(0) || type == "" || value == void(0)) return;
        
        if (!this._aItem.params) this._aItem.params = [];
        
        var index = this._getIndexOfItemInParams(PARAMS.CUSTOM_DATA, this._aItem);
        
        var currentCustomItem = this._aItem.params[index] || void(0);
        
        let customAxnStr = "custom_action_type=" + type + "|custom_action_value=" + value;
        if (currentCustomItem != void(0))
        {
          let customValList = [];
          if (this._aItem.params[index].value != void(0) && this._aItem.params[index].value != "")
          {
            customValList = this._aItem.params[index].value.split("|");
          }
          
          if (customValList.length)
          {
            let arIndex = customValList.length;
            while (arIndex--)
            {
              if (customValList[arIndex] === "")
              {
                // remove blank entries
                customValList.splice(arIndex, 1);
              }
            }

            // check whether the custom action was added previously 
            // if found then replace the type and value
            customValList.forEach((item, i) => {
              // split item on =
              var keyValPair = item.split("=");
              if (keyValPair[0] == "custom_action_type") keyValPair[1] = type;
              else if (keyValPair[0] == "custom_action_value") keyValPair[1] = value;
              
              customValList[i] = keyValPair.join("=");
            });

            // if customValList does not contain
            // custom_action_type/value then add it
            if (!customValList.find(cv => cv.startsWith("custom_action_type")))
            {
              customValList.push("custom_action_type=" + type);
            }
            if (!customValList.find(cv => cv.startsWith("custom_action_value")))
            {
              customValList.push("custom_action_value=" + value);
            }

            customAxnStr = customValList.join("|");
          }
          
          this._aItem.params[index] = {
            "name": PARAMS.CUSTOM_DATA,
            "value": customAxnStr
          };
        }
        else
        {
          // there is no other custom data so just set the custom action selection
          this._aItem.params[index] = {
            "name": PARAMS.CUSTOM_DATA,
            "value": customAxnStr
          };
        }
      },
      
      // Writes to current action
      // params [{...},{...}...] > rawData: [string] "<raw data value>"...
      // Input style [string] - the raw data value
      setRawData: function(rawDataStr)
      {
        if (!rawDataStr || rawDataStr == "") return;
        
        if (!this._aItem.params) this._aItem.params = [];
        
        var index = this._getIndexOfItemInParams(PARAMS.RAW_DATA, this._aItem);
        
        this._aItem.params[index] = {
          "name": PARAMS.RAW_DATA,
          "value": rawDataStr
        };
      },

      setSMSData: function(smsDataStr)
      {
        if (!smsDataStr || smsDataStr == "") return;
        
        if (!this._aItem.params) this._aItem.params = [];
        
        var index = this._getIndexOfItemInParams(PARAMS.SMS_DATA, this._aItem);
        
        this._aItem.params[index] = {
          "name": PARAMS.SMS_DATA,
          "value": smsDataStr
        };
      },

      setArticleData: function (articleDataObj) 
      {
        if (!articleDataObj) return;

        if (!this._aItem.params) this._aItem.params = [];

        Object.keys(articleDataObj).forEach(k => {
          var idx = this._getIndexOfItemInParams(k, this._aItem);
          this._aItem.params[idx] = {
            "name": k,
            "value": articleDataObj[k]
          };
        });
      },
      
      // Writes to current action
      // params [{...},{...}...] > z1_template_properties: [string] "_z1_os=android,ios,html5" || "_z1_os=android,ios" ...
      // Input style [string] - comma separated os values with key = _z1_os
      setActionProperties: function(props)
      {
        if (!props || props == "") return;
        
        if (!this._aItem.params) this._aItem.params = [];
        
        var index = this._getIndexOfItemInParams(PARAMS.PROPERTIES, this._aItem);
        
        this._aItem.params[index] = {
          "name": PARAMS.PROPERTIES,
          "value": props
        };
      },
      
      //.....................................
      // BEGIN - DATA MANIP
      //.....................................      
      
      /**
       * The popup widget has the selected template from this.getActionTemplate
       * which contains the default value 
       * for all the 'structure' and 'custom' components  
       * 
       * It will call the getSelected* method to get any pre-existing values 
       * If there are values then it will show that, otherwise it will show the default value 
       */
      
      /**
       * Set selected action name
       * 
       * Sets the name 
       * ex. "Banner"
       */
      _setSelectedName: function(selectedAction)
      {
        // first check _aItem (this is the active action payload that the Template widget tracks)
        // checking _aItem handles the case where the user is toggling between widget pages
        // if !_aItem then check the selectedAction
        // if there is a name in the selectedAction then use that name
        if (this._aItem && this._aItem.name && this._aItem.name != "")
        {
          if (this._selectedName !== this._aItem.name)
            this._selectedName = this._aItem.name; 
        }
        else if (selectedAction && selectedAction.name && selectedAction.name != "")
        {
          if (this._selectedName !== selectedAction.name)
            this._selectedName = selectedAction.name;
        }
        else this._selectedName = null;
      },
      
      /**
       *  Set selected action type
       *  
       *  Sets the ref value 
       *  ex. "udc.system.core.ZineOne Processing:MobileAppBannerAction"
       */
      _setSelectedType: function(selectedAction)
      {
        // first check _aItem (this is the active action payload that the Template widget tracks)
        // checking _aItem handles the case where the user is toggling between widget pages
        // if !_aItem then check the selectedAction
        // if there is a ref in the selectedAction then use that ref
        if (this._aItem && this._aItem.ref && this._aItem.ref != ""){
          if (this._selectedType !== this._aItem.ref)
            this._selectedType = this._aItem.ref;
        }
        else if (selectedAction && selectedAction.ref && selectedAction.ref != "")
        {
          if (this._selectedType !== selectedAction.ref)
            this._selectedType = selectedAction.ref;
        }
        else
        {
          // null is the default value
          this._selectedType = null;
        }
      },
      
      // supporting method
      _hasActionTypeChanged: function()
      {
        var selectedType = this.getSelectedType();
        var selectedAction = this.getSelectedAction();
        
        if (!selectedAction) return true;
        
        if (selectedAction.ref && selectedType && selectedAction.ref == selectedType)
        {
          return false;
        }
        return true;
      },

      /**
       * Type has changed. Reset action data.
       */
      actionTypeChanged: function ()
      {
        this.resetCtx({_typeChanged: true});
      },

      /**
       *  Set selected action template
       *  
       *  Sets the z1_template_ref value 
       *  ex. "udc.system.core.ActionTemplateOOTB:banner:b_blank:1.0"
       */
      _setSelectedTemplate: function(selectedAction)
      {
        // null is the default value
        this._selectedTemplate = null;
        
        // if action type has changed don't check the selectedAction
        // if action type has not changed then check _aItem first 
        // note: by design - _aItem will only have a template if the user
        // has gone on to the next page to design the configuration for that template.
        
        var aTypeChanged = this._hasActionTypeChanged(); 
        
        // first check _aItem (this is the active action payload that the Template widget tracks)
        // checking _aItem handles the case where the user is toggling between widget pages
        // if there is no z1_template_ref in _aItem then check the selectedAction
        // if there is a z1_template_ref in the selectedAction then use that value
        let index = -1;
        if (this._aItem && this._aItem.params && this._aItem.params.length > 0)
        {
          // find z1_template_ref in _aItem
          index = this._getIndexOfItemInParams(PARAMS.TEMPLATE_REF, this._aItem);
          if (index >= 0 && index < this._aItem.params.length)
          {
            // above check is because _getIndexOfItemInParams will 
            // return params.length if it doesn't find anything 
            this._selectedTemplate = this._aItem.params[index].value;
          }
        }
        // if the selectedTemplate is still null
        // check the selectedAction to see if it has a preselected template
        // the above logic path implies that the widget is in 'edit' mode
        // and no one has selected a new template or moved to the next page to design the config
        if (!aTypeChanged && !this._selectedTemplate &&
          selectedAction != void(0) &&
          selectedAction.params && selectedAction.params.length > 0)
        {
          index = this._getIndexOfItemInParams(PARAMS.TEMPLATE_REF, selectedAction);
          if (index >= 0 && index < selectedAction.params.length)
          {
            // above check is because _getIndexOfItemInParams will 
            // return params.length if it doesn't find anything 
            this._selectedTemplate = selectedAction.params[index].value;
          }
        }
      },
      
      // supporting method
      _hasActionTemplateChanged: function()
      {
        var selectedTemplate = this.getSelectedTemplate();
        var selectedAction = this.getSelectedAction();
        
        if (!selectedAction) return true;
        
        // find previously selected template ref
        var templateRef = null;
        if(selectedAction.params && selectedAction.params.length > 0)
        {
          var index = this._getIndexOfItemInParams(PARAMS.TEMPLATE_REF, selectedAction);
          if (index >= 0 && index < selectedAction.params.length)
          {
            // above check is because _getIndexOfItemInParams will 
            // return params.length if it doesn't find anything 
            templateRef = selectedAction.params[index].value;
          }
        }
        if (templateRef && selectedTemplate && templateRef == selectedTemplate)
        {
          return false;
        }
        return true;
      },
      
      /**
       *  Set selected action config - style & properties
       *  
       *  Sets the z1_template_param value 
       *  ex. "tickerText=?{z1_content}|backgroundcolor=#ffffff|fontcolor=#000000"
       */
      _setSelectedConfig: function(selectedAction)
      {
        // null is the default value
        this._selectedConfig = null;
        
        // if action type or action template have changed then only check _aItem
        // otherwise check _aItem first then check selectedAction - handles user toggling across pages
        var aTypeChanged = this._hasActionTypeChanged(); 
        var aTemplateChanged = this._hasActionTemplateChanged();
        
        var actionHasChanged = aTypeChanged || aTemplateChanged;
        
        // first check _aItem (this is the active action payload that the Template widget tracks)
        // checking _aItem handles the case where the user is toggling between widget pages
        // if there is no z1_template_param in _aItem then check the selectedAction
        // if there is a z1_template_param in the selectedAction then use that value
        let index = -1;
        if (this._aItem && this._aItem.params && this._aItem.params.length > 0)
        {
          // find z1_template_ref in _aItem
          index = this._getIndexOfItemInParams(PARAMS.TEMPLATE_PARAM, this._aItem);
          if (index >= 0 && index < this._aItem.params.length)
          {
            // above check is because _getIndexOfItemInParams will 
            // return params.length if it doesn't find anything 
            this._selectedConfig = this._aItem.params[index].value;
          }
        }
        // if the _selectedConfig is still void(0) and action has not changed
        if (!actionHasChanged && this._selectedConfig == void(0) && selectedAction != void(0) 
            && selectedAction.params && selectedAction.params.length > 0)
        {
          index = this._getIndexOfItemInParams(PARAMS.TEMPLATE_PARAM, selectedAction);
          if (index >= 0 && index < selectedAction.params.length)
          {
            // above check is because _getIndexOfItemInParams will 
            // return params.length if it doesn't find anything 
            this._selectedConfig = selectedAction.params[index].value;
          }
        }
      },
      
      /**
       *  Set selected action content - content_text or content_ref
       *  
       *  Sets the entire object
       *  ex. 
       *  {  
       *    "name":"z1_content_ref",
       *    "value":"ea3585bf-b00b-4956-af67-5ef42dbc1531"
       *  }
       *  OR
       *  {
       *    "name": "z1_content_text",
       *    "value": "Suit l'amour, l'amour fuit.  Fuit l'amour, l'amour suit!!!"
       *  }
       */
      _setSelectedContent: function(selectedAction)
      {
        // null is the default value
        this._selectedContent = null;
        
        // if action type or action template have changed then only check _aItem
        // otherwise check _aItem first then check selectedAction - handles user toggling across pages
        var aTypeChanged = this._hasActionTypeChanged(); 
        var aTemplateChanged = this._hasActionTemplateChanged();
        
        var actionHasChanged = aTypeChanged || aTemplateChanged;
        
        // first check _aItem (this is the active action payload that the Template widget tracks)
        // checking _aItem handles the case where the user is toggling between widget pages
        // find either z1_content_ref OR z1_content_text in _aItem 
        // if neither is found then check the selectedAction for either of those
        // if there is either z1_content_ref OR z1_content_text in the selectedAction then use that object
        let index = -1;
        if (this._aItem && this._aItem.params && this._aItem.params.length > 0)
        {
          let key = null;
          
          let foundCT = this._aItem.params.filter(param => {
            return (param.name == PARAMS.CONTENT_TEXT);
          });
          
          let foundCR = this._aItem.params.filter(param => {
            return (param.name == PARAMS.CONTENT_REF);
          });
          
          // if content_text was found in _aItem.params
          if (foundCT && foundCT.length > 0) key = PARAMS.CONTENT_TEXT;
          else if (foundCR && foundCR.length > 0) key = PARAMS.CONTENT_REF;
          
          if (key)
          {
            // find z1_content_ref OR z1_content_text in _aItem
            index = this._getIndexOfItemInParams(key, this._aItem);
            if (index >= 0 && index < this._aItem.params.length)
            {
              // above check is because _getIndexOfItemInParams will 
              // return params.length if it doesn't find anything 
              this._selectedContent = this._aItem.params[index];
            }
          }
        }
        // if selectedContent is still void(0) and action has not changed
        if (!actionHasChanged && this._selectedContent == void(0) && selectedAction != void(0) 
            && selectedAction.params && selectedAction.params.length > 0)
        {
          let key = null;
          
          let foundCT = selectedAction.params.filter(param => {
            return (param.name == PARAMS.CONTENT_TEXT);
          });
          
          let foundCR = selectedAction.params.filter(param => {
            return (param.name == PARAMS.CONTENT_REF);
          });
          
          // if content_text was found in _aItem.params
          if (foundCT && foundCT.length > 0) key = PARAMS.CONTENT_TEXT;
          else if (foundCR && foundCR.length > 0) key = PARAMS.CONTENT_REF;
          
          if (key)
          {
            index = this._getIndexOfItemInParams(key, selectedAction);
            if (index >= 0 && index < selectedAction.params.length)
            {
              // above check is because _getIndexOfItemInParams will 
              // return params.length if it doesn't find anything 
              this._selectedContent = selectedAction.params[index];
            }
          }
        }
      },
      
      /**
       *  Set selected action custom data
       *  
       *  Sets the z1_template_custom value 
       *  ex. "custom_action_type=deeplink|custom_action_value=12l23los-soejfo-skjolfjd-djfojl|..."
       */
      _setSelectedCustomData: function(selectedAction)
      {
        // null is the default value
        this._selectedCustomData = null;
        
        // if action type or action template have changed then only check _aItem
        // otherwise check _aItem first then check selectedAction - handles user toggling across pages
        var aTypeChanged = this._hasActionTypeChanged(); 
        var aTemplateChanged = this._hasActionTemplateChanged();
        
        var actionHasChanged = aTypeChanged || aTemplateChanged;
        
        // first check _aItem (this is the active action payload that the Template widget tracks)
        // checking _aItem handles the case where the user is toggling between widget pages
        // if there is no z1_template_custom in _aItem then check the selectedAction
        // if there is a z1_template_custom in the selectedAction then use that value
        let index = -1;
        if (this._aItem && this._aItem.params && this._aItem.params.length > 0)
        {
          // find z1_template_ref in _aItem
          index = this._getIndexOfItemInParams(PARAMS.CUSTOM_DATA, this._aItem);
          if (index >= 0 && index < this._aItem.params.length)
          {
            // above check is because _getIndexOfItemInParams will 
            // return params.length if it doesn't find anything 
            this._selectedCustomData = this._aItem.params[index].value;
          }
        }
        // if the selected custom data is still void(0) and the action has not changed
        // if user does not define any other customData then it's possible for it to be an empty string
        // in this case look into the selectedAction as well
        if (!actionHasChanged && (this._selectedCustomData == void(0) || this._selectedCustomData == "") 
            && selectedAction != void(0) 
            && selectedAction.params && selectedAction.params.length > 0)
        {
          index = this._getIndexOfItemInParams(PARAMS.CUSTOM_DATA, selectedAction);
          if (index >= 0 && index < selectedAction.params.length)
          {
            // above check is because _getIndexOfItemInParams will 
            // return params.length if it doesn't find anything 
            this._selectedCustomData = selectedAction.params[index].value;
          }
        }
      },
      
      _setSelectedRawData: function(selectedAction)
      {
        // null is the default value
        this._definedRawData = null;
        
        var aTypeChanged = this._hasActionTypeChanged();
        
        let index = -1;
        
        if (this._aItem && this._aItem.params && this._aItem.params.length > 0)
        {
          index = this._getIndexOfItemInParams(PARAMS.RAW_DATA, this._aItem);
          if (index >= 0 && index < this._aItem.params.length)
          {
            this._definedRawData = this._aItem.params[index].value;
          }
        }
        
        // if the defined raw data is still void(0) and the action has not changed
        // if user does not define any other rawData then it's possible for it to be an empty string
        // in this case look into the selectedAction as well
        if (!aTypeChanged && (this._definedRawData == void(0) || this._definedRawData == "")
            && selectedAction != void(0)
            && selectedAction.params && selectedAction.params.length > 0)
        {
          index = this._getIndexOfItemInParams(PARAMS.RAW_DATA, selectedAction);
          if (index >= 0 && index < selectedAction.params.length)
          {
            // above check is because _getIndexOfItemInParams will 
            // return params.length if it doesn't find anything 
            this._definedRawData = selectedAction.params[index].value;
          }
        }
      },

      _setSelectedArticleData: function(selectedAction)
      {
        // null is the default value
        this._definedArticleData = null;
        
        var aTypeChanged = this._hasActionTypeChanged();
        
        let index = -1;
        
        if (this._aItem && this._aItem.params && this._aItem.params.length > 0)
        {
          let articleData = {};
          
          index = this._getIndexOfItemInParams(PARAMS.ARTICLE_TITLE, this._aItem);
          if (index >= 0 && index < this._aItem.params.length)
          {
            articleData[PARAMS.ARTICLE_TITLE] = this._aItem.params[index].value;
          }

          index = -1;
          index = this._getIndexOfItemInParams(PARAMS.ARTICLE_TAGLIST, this._aItem);
          if (index >= 0 && index < this._aItem.params.length)
          {
            articleData[PARAMS.ARTICLE_TAGLIST] = this._aItem.params[index].value;
          }

          this._definedArticleData = articleData;
        }
        
        // if the defined article data is still void(0) and the action has not changed
        // if user does not define any other article data then it's possible for it to be an empty string
        // in this case look into the selectedAction as well
        if (!aTypeChanged && (this._definedArticleData == void(0) || this._definedArticleData == "")
            && selectedAction != void(0)
            && selectedAction.params && selectedAction.params.length > 0)
        {
          let articleData = {};

          index = this._getIndexOfItemInParams(PARAMS.ARTICLE_TITLE, selectedAction);
          if (index >= 0 && index < selectedAction.params.length)
          {
            // above check is because _getIndexOfItemInParams will 
            // return params.length if it doesn't find anything 
            articleData[PARAMS.ARTICLE_TITLE] = selectedAction.params[index].value;
          }

          index = -1;
          index = this._getIndexOfItemInParams(PARAMS.ARTICLE_TAGLIST, selectedAction);
          if (index >= 0 && index < selectedAction.params.length)
          {
            // above check is because _getIndexOfItemInParams will 
            // return params.length if it doesn't find anything 
            articleData[PARAMS.ARTICLE_TAGLIST] = selectedAction.params[index].value;
          }

          this._definedArticleData = articleData;
        }
      },

      _setSelectedSMSData: function(selectedAction)
      {
        // null is the default value
        this._definedSMSData = null;
        
        var aTypeChanged = this._hasActionTypeChanged();
        
        let index = -1;
        
        if (this._aItem && this._aItem.params && this._aItem.params.length > 0)
        {
          index = this._getIndexOfItemInParams(PARAMS.SMS_DATA, this._aItem);
          if (index >= 0 && index < this._aItem.params.length)
          {
            this._definedSMSData = this._aItem.params[index].value;
          }
        }
        
        // if the defined sms data is still void(0) and the action has not changed
        // if user does not define any other sms data then it's possible for it to be an empty string
        // in this case look into the selectedAction as well
        if (!aTypeChanged && (this._definedSMSData == void(0) || this._definedSMSData == "")
            && selectedAction != void(0)
            && selectedAction.params && selectedAction.params.length > 0)
        {
          index = this._getIndexOfItemInParams(PARAMS.SMS_DATA, selectedAction);
          if (index >= 0 && index < selectedAction.params.length)
          {
            // above check is because _getIndexOfItemInParams will 
            // return params.length if it doesn't find anything 
            this._definedSMSData = selectedAction.params[index].value;
          }
        }
      },
      
      _setSelectedGroupDisplayName: function(grpDispName)
      {
        // null is the default value
        this._selectedGrpDispName = grpDispName || null;
      },
      
      // get selected action name
      getSelectedName: function()
      {
        return this._selectedName;
      },
      
      // get selected action type
      getSelectedType: function()
      {
        return this._selectedType;
      },
      
      // get selected action template
      getSelectedTemplate: function()
      {
        return this._selectedTemplate;
      },
      
      // get selected action config - style & properties
      getSelectedConfig: function()
      {
        return this._selectedConfig;
      },
      
      // get selected content - content_text or content_ref
      getSelectedContent: function()
      {
        return this._selectedContent;
      },
      
      // get selected custom data 
      getSelectedCustomData: function()
      {
        return this._selectedCustomData;
      },
      
      getSelectedGrpDispName: function()
      {
        return this._selectedGrpDispName;
      },
      
      getDefinedRawData: function()
      {
        return this._definedRawData;
      },

      getDefinedSMSData: function()
      {
        return this._definedSMSData;
      },

      getDefinedArticleData: function()
      {
        return this._definedArticleData;
      },
      
      //.....................................
      // END - DATA MANIP
      //.....................................
      
      //...............................
      // if there is an action index
      // and it is greater than or equal to the number of current actions
      // then this is a new action
      isNew: function()
      {
        if(this._dItem._actionIndex >= 0 && 
            (this._dItem && this._dItem.actions && this._dItem._actionIndex >= this._dItem.actions.length)) return true;
        return false;
      },

      //.............................
      // copy without references
      copyObj: function (obj)
      {
        return JSON.parse(JSON.stringify(obj));
      },
      
      //.............................................................
      
      /**
       * Set the existing list of actions in this experience
       * 
       * This is the original list and it is not edited until
       * the user clicks "Done" on the ActionTemplateWidget
       * 
       * if editing an existing action then actionIndex will be 
       * within range of [0, length of _dItem.actions)
       * starting from and including 0 up to _dItem.actions.length-1
       * else actionIndex = 0
       */
      setActionCtx: function(ctx = {})
      {
        this._originalCtx = ctx;
        
        this._dItem = ctx._dItem || {};
        this._actionIndex = (!isNaN(ctx._actionIndex)) ? ctx._actionIndex : this._actionIndex;
        
        if (this._dItem && this._dItem.actions)
        {
          this._setExistingActions(this._dItem.actions);
          if (this._actionIndex >= 0 && this._actionIndex < this._dItem.actions.length)
          {
            // Keep original action.
            this._prevAction = this.copyObj(this._dItem.actions[this._actionIndex]);
            // set current action
            this._setSelectedAction(this._dItem.actions[this._actionIndex]);
          }
        }
      },
      
      getActionCtx: function ()
      {
        return this._originalCtx;
      },
      
      getUsedIn: function()
      {
        if (this._originalCtx && this._originalCtx._usedIn) return this._originalCtx._usedIn;
        else return "";
      },
      
      _setSelectedAction: function (action)
      {
        if (!action) return;
        this._actionInEdit = action;
      },
      
      getSelectedAction: function ()
      {
        return this._actionInEdit;
      },
      
      _setExistingActions: function (actions)
      {
        if (!actions) return;
        this._existingActions = actions;
      },
      
      getExistingActions: function ()
      {
        return this._existingActions;
      },
      
      prepareSelectedItems: function()
      {
        var selectedAction = this.getSelectedAction();
        
        // set selected action name
        this._setSelectedName(selectedAction);
        // set selected action type
        this._setSelectedType(selectedAction);
        // set selected action template
        this._setSelectedTemplate(selectedAction);
        // set selected action config - style & properties
        this._setSelectedConfig(selectedAction);
        // set selected content - content_text or content_ref
        this._setSelectedContent(selectedAction);
        // set selected custom data 
        this._setSelectedCustomData(selectedAction);
        // set selected raw data
        this._setSelectedRawData(selectedAction);
        // set selected sms data
        this._setSelectedSMSData(selectedAction);
        // set selected article data
        this._setSelectedArticleData(selectedAction);
        // update steps/progress bar
        topic.publish("/action/config/steps");
      },

      /**
       * The action editor does not manage existing
       * page/position labels.
       * If editing an existing action and that action
       * has labels then:
       * Case 1: If the action type/template has changed then
       * remove the labels. As it's not known if the
       * action's display behavior is identical to
       * previous action, that is will it show on
       * page or not.
       * Case 2: Action type/template is not changed so
       * copy the labels data from _prevAction to
       * edited action.
       */
      maintainActionLabels: function ()
      {
        if (!this._prevAction) return;
        if (Array.isArray(this._prevAction.params))
        {
          let pa = this._prevAction;
          let prevLabels = [];
          let iPg = this._aItem.params.findIndex((p) => p.name === "z1_action_page");
          let iPos = this._aItem.params.findIndex((p) => p.name === "z1_action_position");

          // indicator that the edit change is big enough
          // that existingd
          let changed = false;

          // is type changed
          let typeChanged = pa.ref !== this._aItem.ref;

          // is template changed
          let pPrevTRef = pa.params.find((p) => p.name === "z1_template_ref");
          let pTRef = this._aItem.params.find((p) => p.name === "z1_template_ref");
          let templateChanged = true;
          if (!pPrevTRef && !pTRef)
            // no templates used 
            templateChanged = false;
          if (pPrevTRef && pTRef && pPrevTRef.value === pTRef.value)
            templateChanged = false;

          if (typeChanged)
          {
            // if type changed then mark templateChanged as true
            templateChanged = true;
            changed = true;
          }
          if (templateChanged) changed = true;

          // rawData does not have template
          let isRawData = false;
          if (pa.dimension === "mobile/raw" && this._aItem.dimension === pa.dimension)
          {
            // check if content changed.
            let rd1 = pa.params.find((p) => p.name === "rawData");
            let rd2 = this._aItem.params.find((p) => p.name === "rawData");
            if (rd1 && rd2 && rd1.value !== rd2.value)
            {
              // rawData content changed
              changed = true;
            }
          }

          // page
          let page = pa.params.find((p) => p.name === "z1_action_page");
          if (page && page.value && page.value.length)
            prevLabels = prevLabels.concat(page.value.split(","));
          if (!page || !page.value)
          {
            // no labels, do nothing
          }
          else if (iPg > -1 && changed)
          {
            // Changed so remove the
            // labels if they were present
            this._aItem.params.splice(iPg, 1);
          }
          else if (changed)
          {
            // Changed and no
            // labels in edited action so
            // do nothing
          }
          else if (iPg == -1)
          {
            this._aItem.params.push({
              name: "z1_action_page",
              value: page.value
            });
          }
          else
          {
            this._aItem.params[iPg].value = page.value;
          }

          // position
          let position = pa.params.find((p) => p.name === "z1_action_position");
          if (position && position.value && position.value.length)
            prevLabels = prevLabels.concat(position.value.split(","));
          if (!position || !position.value)
          {
            // no labels, do nothing
          }
          else if (iPos > -1 && changed)
          {
            // Changed so remove the
            // labels if they were present
            this._aItem.params.splice(iPos, 1);
          }
          else if (changed)
          {
            // Changed and no
            // labels in edited action so
            // do nothing
          }
          else if (iPos == -1)
          {
            this._aItem.params.push({
              name: "z1_action_position",
              value: position.value
            });
          }
          else
          {
            this._aItem.params[iPos].value = position.value;
          }

          if (prevLabels.length && changed)
          {
            //udc.info(`${Res.labelsRemoved1}${prevLabels.join(", ")}${Res.labelsRemoved2}`);
          }
        }
      },

      /**
       * Take the current action 
       * Take a deep copy of the originalCtx
       * add the current action to the newCtx and return it
       */
      getDataToSave: function()
      {
        this.maintainActionLabels();
        // if there are changes to _aItem
        // then update the originalctx
        var items = Object.keys(this._aItem);
        if (items != void(0) && items.length);
        {
          var actionLength = 0;
          if (this._originalCtx._dItem && this._originalCtx._dItem.actions)
          {
            actionLength = this._originalCtx._dItem.actions.length;
          }
          if (this._actionIndex >= actionLength)
          {
            // in this case the item needs to be added to the actions list
            // ensure that the actions list exists before adding the new action
            if (this._originalCtx._dItem.actions == void(0)) this._originalCtx._dItem.actions = [];
            this._originalCtx._dItem.actions.push(this._aItem);
          }
          else
          {
            this._originalCtx._dItem.actions[this._actionIndex] = this._aItem;
          }
        }
        
        return this._originalCtx;
      },
      
      //.............................................................
      
      /**
       * Given a selected template 
       * Return true if this template has z1_contenttext in structure
       * else false
       */
      templateNeedsContent: function()
      {
        if (this._aTemplate && Array.isArray(this._aTemplate.structure)
          && this._aTemplate.structure.find(control => control.id == CONTROLS.CONTENT))
        {
          return true;
        }
        return false;
      },
      
      /**
       * Given a selected template 
       * Return true if this template has anything other than 
       * z1_contenttext in its structure
       * else false
       */
      templateNeedsStyle: function()
      {
        if (this._aTemplate && Array.isArray(this._aTemplate.structure)
          && this._aTemplate.structure.find(control => control.id != CONTROLS.CONTENT))
        {
          return true;
        }
        return false;
      },
      
      /**
       * Given a selected template
       * Return true if this template has a non-empty custom object
       * else false
       */
      templateNeedsCustomData: function()
      {
        if (this._aTemplate && this._aTemplate.custom)
        {
          var ck = Object.keys(this._aTemplate.custom);
          if (ck != void(0))
          {
            // if it includes action_type or action_value the length has to be greater than two
            // else length has to be greater than 0
            if (ck.includes("custom_action_type") && ck.includes("custom_action_value") && ck.length > 2) return true;
            else if (ck.length > 0) return true;
          }
        }
        return false;
      },
      
      /**
       * Given a selected template
       * Return true if this template needs a custom action 
       * else false
       */
      templateNeedsCustomAction: function()
      {
        if (this._aTemplate && this._aTemplate.custom)
        {
          // actionType is not set, so check on dimension
          if(this._aTemplate.dimension === "mobile/appbox")
          {
            // 202008 disable no deeplink selection for appbox
            // (step 5 'Chain an Action')
            return;
          }

          var ck = Object.keys(this._aTemplate.custom);
          if (ck != void(0) && ck.length && ck.includes("custom_action_type") && ck.includes("custom_action_value")) return true;
        }
        return false;
      },
      
      /**
       * Given a selected template
       * Return true if this template has a non-empty tags object
       * else false
       * 
       * Turned Off - no support currently
       */
//      templateNeedsProps: function()
//      {
//        if (this._aTemplate && this._aTemplate.tags && this._aTemplate.tags.length)
//        {
//          return true;
//        }
//        return false;
//      },
      
      //...................................
      /**
       * Checks whether the current action needs a blank content
       */
      actionNeedsBlankContent: function()
      {
        if (this._aItem && Array.isArray(this._aItem.params)
          && this._aItem.params.find((p) => p.name == PARAMS.CONTENT_TEXT))
        {
          return true;
        }
        return false;
      },

      // ............................................................
      initHelperData: function ()
      {
        var deferr = new Deferred();

        // fetch / set only if data is changed

        // var a = this.getSelectedAction();
        // a - active action data
        var a;
        if(this._dItem && this._dItem.actions 
            && this._dItem.actions.length
            && this._dItem.actions[this._actionIndex])
        {
          // existing action for edit
          a = this._dItem.actions[this._actionIndex];
        }
        // new or modified action is in _aItem
        var aItem = this._aItem;
        if (aItem && Array.isArray(aItem.params))
        {
          a = aItem;
        }
        // from _dItem or _aItem
        if (!a && aItem) a = aItem;

        if (!a || !a.params)
        {
          deferr.resolve(true);
          return deferr.promise;
        }

        var z1TemplateRef;
        var z1TemplateParam;
        var z1TemplateCustom;
        var z1Content;
        var z1Ref;

        var allArr = [];

        if (a && a.params)
        {
          a.params.forEach((p) => {
            if (p.name === "z1_template_ref")
            {
              z1TemplateRef = p.value;
            }
            if (p.name === "z1_template_param")
            {
              // {
              //   "name": "z1_template_param",
              //   "value": "promolabel=Discount coupon|promolabelfontsize=30%
              //    |promocodefontsize=140%|promocodefontweight=600|promodescription=hmm
              //    |promodescriptionfontsize=100%|promodatelabel=Expires
              //    |promodate=11/11/2020|z1_position=top|z1_contenttext=?{z1_content}"
              // }
              if (p.value !== void(0) && p.value.length)
              {
                z1TemplateParam = {};
                let ar = p.value.split("|").forEach(str => {
                  let pAr = str.split("=");
                  if (pAr[1])
                  {
                    z1TemplateParam[pAr[0]] = pAr[1];
                  }
                });
              }
            }
            if (p.name === "z1_template_custom")
            {
              // name: "z1_template_custom"
              // value: "custom_data_product=Shoes|||custom_data_z1image=https://../enwiki-1.5x.png|"
              if (p.value !== void(0) && p.value.length)
              {
                z1TemplateCustom = {};
                let ar = p.value.split("|||").forEach(str => {
                  let pAr = str.split("=");
                  if (pAr[1] && pAr[1].length)
                  {
                    z1TemplateCustom[pAr[0]] = pAr[1];
                  }
                });
              }
            }
            if (p.name === "z1_ref" || p.name === "z1_content_ref")
            {
              if (p.value)
              {
                z1Ref = p.value;
              }
            }
            if (p.name === "z1_content_text")
            {
              if (p.value !== void (0))
              {
                z1Content = p.value;
              }
            }
          });
        }

        // template
        if (z1TemplateRef !== void(0))
        {
          if (z1TemplateRef !== this._z1TemplateRef)
          {
            this._z1TemplateRef = z1TemplateRef;
            allArr.push(this.fetchTemplate({id: this._z1TemplateRef}));
          }
        }
        else
        {
          this._z1TemplateRef = void(0);
        }

        if (z1TemplateParam !== void(0))
        {
          if (this._z1TemplateParam &&
            JSON.stringify(this._z1TemplateParam) === JSON.stringify(z1TemplateParam))
          {
            // no change
          }
          else
          {
            this._z1TemplateParam = z1TemplateParam;
          }
        }
        else
        {
          this._z1TemplateParam = void(0);
        }

        if (z1TemplateCustom !== void(0))
        {
          if (this._z1TemplateCustom &&
            JSON.stringify(this._z1TemplateCustom) === JSON.stringify(z1TemplateCustom))
          {
            // no change
          }
          else
          {
            this._z1TemplateCustom = z1TemplateCustom;
          }
        }
        else
        {
          this._z1TemplateCustom = void(0);
        }

        // content
        if (z1Ref !== void(0))
        {
          if (z1Ref !== this._z1Ref)
          {
            this._z1Ref = z1Ref;
            allArr.push(this.fetchContentDetailById({contentId: this._z1Ref}));
          }
        }
        else
        {
          this._z1Ref = void(0);
        }

        if (z1Content !== void(0))
        {
          if (z1Content !== this._z1Content)
          {
            this._z1Content = z1Content;
          }
        }
        else
        {
          this._z1Content = void(0);
        }

        // CSS
        // allArr.push(this.fetchContentcss());

        if (allArr.length)
        {
          all(allArr).then(lang.hitch(this, function (){
            deferr.resolve(true);
          }), lang.hitch(this, function (err){
            deferr.resolve(true);
          }));
        }
        else
        {
          deferr.resolve(true);
        }

        return deferr.promise;
      },

      // ............................................................
      fetchTemplate: function (ctx = {})
      {
        var deferr = new Deferred();

        ModulesData.api.fetchModuleConfig({id: ctx.id, cacheOk: true}).then(lang.hitch(this, function(data){
          if (data == void(0) || data.config == void(0) || data.config[0].subtype != 'actiontemplate')
          {
            deferr.resolve(true);
            return deferr.promise;
          }
          
          if (data.config[0].payload == void(0) || data.config[0].payload.trim() == "")
          {
            deferr.resolve(true);
            return deferr.promise;
          }

          var aTemp = JSON.parse(data.config[0].payload);
          this.setActionTemplate(aTemp);

          deferr.resolve(true);
        }), lang.hitch(this, function(err){
          udc.log(err);
          deferr.resolve({ pass: true });
          return deferr.promise;
        }));

        return deferr.promise;
      },

      //.................................................
      // @params {
      //   contentId
      // }
      fetchContentDetailById: function (ctx)
      {
        var deferr = new Deferred();

        let cCtx = {
          contentId: ctx.contentId
        };
        // /c3/data/kb/content?contentId=<>
        ContentData.api.fetchContent(cCtx).then(lang.hitch(this, function(data)
        {
          if (data && data.status && data.status === "fail")
          {
            if (data.message) udc.info(data.message);
            udc.log("failed: ", data);
            deferr.reject(data);
          }
          else if (Array.isArray(data) && data.length)
          {
            // [
            //   {
            //       "contentId": "38f34230-2c60-402b-8153-c4cc668a3b8e",
            //       "title": "Tagged html content",
            //       "subTitle": "",
            //       "tags": [
            //           "_z1_os:android",
            //           "_z1_os:html5",
            //           "_z1_os:ios",
            //           "test"
            //       ],
            //       "contentType": "text",
            //       "contents": {
            //           "en": {
            //               "status": "published",
            //               "title": "Tagged html content",
            //               "content": "This is<span style=\"background-color: rgb(240, 230, 140);\"> html</span> text",
            //               "draft": null
            //           }
            //       },
            //       "creationDate": "06/14/2019",
            //       "updateDate": "",
            //       "language": "en",
            //       "timestamp": 1560530350395,
            //       "url": "",
            //       "subTitleFieldName": "subTitle",
            //       "defaultLanguage": true,
            //       "titleFieldName": "title"
            //   }
            // ]
            let c = data[0];
            if (c.contentType = "text" && c.contents && c.contents[c.language] &&
              c.contents[c.language].content)
            {
              this._z1Content = c.contents[c.language].content;
            }
            deferr.resolve(this._z1Content);
          }
          else
          {
            this._z1Content = data;
            deferr.resolve(data);
          }
        }), lang.hitch(this, function(error)
        {
          deferr.reject({
            pass: false
          });
        }));

        return deferr.promise;
      },

      /**
       * Get ordered list of steps/progress bar
       * for configuring Action.
       */
      getSteps: function ()
      {
        // these are Screen ID's
        let Screen = {
          SELECT_ACTION_TYPE: "s0",
          SELECT_ACTION_TEMPLATE: "s1",
          SELECT_ACTION_CONFIG: "s2",
          SELECT_ACTION_CONTENT: "s3",
          SELECT_CUSTOM_ACTION: "s4",
          DEFINE_RAW_ACTION: "s5",
          DEFINE_SMS_ACTION: "s6",
          DEFINE_ARTICLE_ACTION: "s7"
          // PREVIEW: "s5"
        };
        var sList = [
          {
            "name": "type",
            "title": "Select Type",
            "sId": Screen.SELECT_ACTION_TYPE
          },
          {
            "name": "template",
            "title": "Select Template",
            "sId": Screen.SELECT_ACTION_TEMPLATE
          },
          {
            "name": "configure",
            "title": "Configure Template",
            "sId": Screen.SELECT_ACTION_CONFIG
          },
          {
            "name": "content",
            "title": "Select Content",
            "sId": Screen.SELECT_ACTION_CONTENT
          },
          {
            "name": "chainAction",
            "title": "Chain an Action",
            "sId": Screen.SELECT_CUSTOM_ACTION
          }
        ];
        return sList;
      },

      /**
       * Get ordered list of sections shown for
       * creating Action.
       */
      getSections: function ()
      {
        var sList = [];

        // a - active action data
        var a;
        if(this._dItem && this._dItem.actions 
            && this._dItem.actions.length
            && this._dItem.actions[this._actionIndex])
        {
          // existing action for edit
          a = this._dItem.actions[this._actionIndex];
        }
        // new or modified action is in _aItem
        var aItem = this._aItem;
        // if (aItem && Array.isArray(aItem.params))
        // {
        //   a = aItem;
        // }
        // from _dItem or _aItem
        if (!a && aItem) a = aItem;

        if (!a) return sList;

        if (a.ref && a.ref.length)
        {
          // type
          sList.push({
            name: "type",
            value: a.ref
          });
        }

        if (a.ref && a.ref.length && a.ref.endsWith(":MobileAppRaw"))
        {
          // rawData
          if (a && a.params &&
            a.params.find(p => p.name === "rawData" && typeof p.value === "string" && p.value.length))
          {
            // template value is not set yet
            sList.push({
              name: "rawData",
              hasRawDataValue: true
            });
          }
          else
          {
            sList.push({
              name: "rawData",
              hasRawDataValue: false
            });
          }
        }
        else if (a.ref && a.ref.length && a.ref.endsWith(":MobileAppSMSMessage"))
        {
          // smsData
          if (a && a.params &&
            a.params.find(p => p.name === "_z1_message" && typeof p.value === "string" && p.value.length))
          {
            // template value is not set yet
            sList.push({
              name: "_z1_message",
              hasSMSDataValue: true
            });
          }
          else
          {
            sList.push({
              name: "_z1_message",
              hasSMSDataValue: false
            });
          }
        }
        else if (a.ref && a.ref.length && a.ref.endsWith(":MobileAppAlertKBAction"))
        {
          // articleData
          if (a && a.params &&
            a.params.find(p => p.name === "listTitle" && typeof p.value === "string" && p.value.length))
          {
            // template value is not set yet
            sList.push({
              name: "listTitle",
              hasArticleDataValue: true
            });
          }
          else
          {
            sList.push({
              name: "listTitle",
              hasArticleDataValue: false
            });
          }

          if (a && a.params &&
            a.params.find(p => p.name === "tags" && typeof p.value === "string" && p.value.length))
          {
            // template value is not set yet
            sList.push({
              name: "tags",
              hasArticleDataValue: true
            });
          }
          else
          {
            sList.push({
              name: "tags",
              hasArticleDataValue: false
            });
          }
        }
        else
        {
          // template
          if (a && a.params &&
            a.params.find(p => p.name === "z1_template_ref" && typeof p.value === "string" && p.value.length))
          {
            // template value is not set yet
            sList.push({
              name: "template",
              hasTemplateRef: true
            });
          }
          else
          {
            sList.push({
              name: "template",
              hasTemplateRef: false
            });
          }
        }

        if (a && a.params)
        {
          // style
          if (this.templateNeedsStyle())
          {
            sList.push({
              name: "style"
            });
          }
          // content
          if (this.templateNeedsContent())
          {
            sList.push({
              name: "content"
            });
          }
          // custom action
          if (this.templateNeedsCustomAction())
          {
            sList.push({
              name: "customAction"
            });
          }
        }

        return sList;
      },

      /**
       * Get ordered list of content sections shown for
       * creating Action.
       */
      getContentSections: function ()
      {
        var sList = this.getSections().filter((s) => {
          switch (s.name){
            case "style":
            case "content":
            case "customAction":
              return true;
            default:
              return false;
          }
        });
        return sList;
      },

      isStep1Complete: function ()
      {
        let s = this.getSelectedType();
        if (s && s !== "") return true;
        return false;
      },
  
      isStep2Complete: function ()
      {
        if (this._selectedTemplate) return true;
        return false;
      },
  
      isStep3Complete: function()
      {
        if (this._selectedConfig) return true;
        return false;
      },
  
      isStep4Complete: function ()
      {

        // a - active action data
        var a;
        if(this._dItem && this._dItem.actions 
            && this._dItem.actions.length
            && this._dItem.actions[this._actionIndex])
        {
          // existing action for edit
          a = this._dItem.actions[this._actionIndex];
        }
        // new or modified action is in _aItem
        var aItem = this._aItem;
        // if (aItem && Array.isArray(aItem.params))
        // {
        //   a = aItem;
        // }
        // from _dItem or _aItem
        if (!a && aItem) a = aItem;

        if (!a) return sList;

        if (a.ref && a.ref.length && a.ref.endsWith(":MobileAppRaw"))
        {
          // rawData
          if (a && a.params &&
            a.params.find(p => p.name === "rawData" && typeof p.value === "string" && p.value.length))
          {
            return true;
          }
          else
          {
            // template value is not set yet
            return false;
          }
        }
        else if (a.ref && a.ref.length && a.ref.endsWith(":MobileAppSMSMessage"))
        {
          // smsData
          if (a && a.params &&
            a.params.find(p => p.name === "_z1_message" && typeof p.value === "string" && p.value.length))
          {
            return true;
          }
          else
          {
            // template value is not set yet
            return false;
          }
        }
        else if (a.ref && a.ref.length && a.ref.endsWith(":MobileAppAlertKBAction"))
        {
          // articleData
          if (a && a.params &&
            a.params.find(p => p.name === "listTitle" && typeof p.value === "string" && p.value.length))
          {
            return true;
          }
          else
          {
            // template value is not set yet
            return false;
          }
        }
        else if (this._selectedContent)
        {
          // 'content'
          return true;
        }
        return false;
      },
  
      isStep5Complete: function ()
      {
        if (this._selectedCustomData && this._selectedCustomData.length)
        {
          // custom_action_type=deeplink|custom_action_value=https://example
          let cav = this._selectedCustomData.split("|").find(s => s.startsWith("custom_action_value"));
          if (cav)
          {
            let cavAr = cav.split("=");
            if(cavAr[1] && cavAr[1].length) return true;
          }
        }
        return false;
      },
  
      //.............................................................
      // INTERNAL METHODS
      //.............................................................
      // get the index of a given item in action.params
      // action can be this._aItem or this._actionInEdit
      _getIndexOfItemInParams: function(itemName, action)
      {
        // if there are params 
        // find the z1_template_ref item in the existing params
        var index = (action && action.params) ? action.params.length : 0;
        if (action && action.params && action.params.length)
        {
          for (var i = 0; i < action.params.length; i++)
          {
            if (action.params[i].name == itemName)
            {
              index = i;
              break;
            }
          }
        }
        return index;
      },

      getParamItemByName: function (action, pName)
      {
        if (!action || !Array.isArray(action.params) || !action.params.length)
          return;
        return action.params.find((p) => p.name === pName);
      },

      /**
       * Finds the 'name' of the z1_contenttext item in structure
       * 
       * This is appended to the z1_template_param value in params 
       * when saving the payload - see payload at top
       */
      _getTemplateParamContentName: function()
      {
        var tpContentName = null;
        if (!this._aTemplate || !this._aTemplate.structure) return tpContentName;
        
        var found = this._aTemplate.structure.filter(control => {
          return (control.id == CONTROLS.CONTENT)
        });
        
        if (found && found.length)
        {
          // only one z1_contenttext
          tpContentName = found[0].name;
        }
        
        return tpContentName;
      },
      
      //.............................................................
    },

    _end: 0
  };

  return ActionCtx;
});
