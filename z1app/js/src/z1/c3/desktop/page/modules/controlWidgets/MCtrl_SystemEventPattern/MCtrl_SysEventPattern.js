/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define(["dojo/_base/declare", "dojo/_base/lang", "dojo/on",
  "udc/udc", "z1/c3/desktop/start",
  "udc/core/rt/widgets/_ComplexBaseWidget",
  "dojo/text!z1/c3/desktop/page/templates/modules/controlWidgets/MCtrl_SystemEventPattern/MCtrl_SysEventPattern.html",
  "dojo/i18n!z1/c3/desktop/page/nls/MCtrl_SysEventPatternRes",
  "z1/c3/desktop/page/modules/controlWidgets/MCtrl_SystemEventPattern/MCtrl_PatternPartDefiner",
  "z1/c3/desktop/page/modules/controlWidgets/MCtrl_SystemEventPattern/MCtrl_PatternPartDisplay",
  "z1/c3/desktop/page/modules/ModuleDefineCtx",
  "dojo/dom-class",
  "dojo/topic",
  "dojo/dom-construct",
  "dojo/Deferred",
  "dojo/promise/all"
],
function(declare, lang, on, udc, z1, BaseWidget, templateStr, Res,
  PatternPartDefiner, PatternPartDisplay, ModuleDefineCtx,
  domClass, topic, domConstruct,
  Deferred, all
)
{

  /**
   * Creates a widget to edit Define Trigger.
   */
  return declare(
  BaseWidget,
  {
    templateString: templateStr,
    Res: Res,
    
    ppDisplayList: [],

    /**
     * Creates an instance of this page.
     * 
     * @param {Object}
     *  {
     *    ctx
     *    templateData
     *  }
     */
    constructor: function(params)
    {
      this.inherited(arguments);
      this.ctx = params.ctx || {};
      this._templateData = params.templateData || {};
      
      this.ppDisplayList = [];
      this.ppdispCount = 0;
    },

    // ////////////////////////////////////////////////////////////////////////////
    // Implementations

    postCreate: function()
    {
      this.inherited(arguments);
    },

    // .....................................................
    startup: function()
    {
      this.inherited(arguments);

      this.setDisplay();

      this._setEvents();
    },
    
    //...............................
    setDisplay: function()
    {
      if (this.ctx.display)
      {
        this.sysPattern_widgetTitleDisplay.textContent = this.ctx.display;
      }
      if (this.ctx && this.ctx.value !== void(0))
      {
        // Assign default value
        // assign default value to rule.
        this.defaultRuleValue = this.ctx.value;
        if (this.ctx.value == "")
        {
          if (!this._templateData)
          {
            udc.alert("system_eventPattern: Missing data");
            return;
          }
          // saving default value
          ModuleDefineCtx.mData.setParamsItem(this.ctx, this.ctx.value);
        }
        else
        {
          this.displayPattern(this.ctx.value);
        }
      }
    },

    //..........................................
    saveRule: function()
    {
      // called by Pattern Part Definer's callback - onDoneCallback
      // or when clear is called 
      // in either case go through the patternDisplayList
      // get the rules and create JSON string
      // if there are no items in patternDisplayList then 
      // pattern = this.defaultRuleValue;
      // ModuleDefineCtx.mData.setTemplateBasedRule(this.ctx.rule, pattern);
      if (this.ppDisplayListIsValid())
      {
        var pattern = this.defaultRuleValue;
        if (this.ppDisplayList && this.ppDisplayList.length)
        {
          var p = [];
          for (var i = 0; i < this.ppDisplayList.length; i++ )
          {
            p.push(this.ppDisplayList[i].getRule());
          }
          pattern = JSON.stringify(p);
        }      
        
        ModuleDefineCtx.mData.setParamsItem(this.ctx, pattern);
      }
    },
    
    // 
    onDoneCallback: function(payload)
    {
      if(!payload || JSON.stringify(payload) === "{}")
      {
        return;
      }
      if (payload.cmd === "edit")
      {
        var displayType = (payload.ctx.name === "*" || payload.ctx.name === "+" || payload.ctx.name === "group") ? payload.ctx.name : "specificActivity"; 
        for (var i = 0; i < this.ppDisplayList.length; i++)
        {
          if (this.ppDisplayList[i].getId() === payload.ppDisplayId)
          {
            // order matters
            this.ppDisplayList[i].setDisplayType(displayType);
            this.ppDisplayList[i].setRule(payload.ctx);
          }
        }
      }
      else if (payload.cmd === "new")
      {
        this.displayPattern(JSON.stringify([payload.ctx]));
      }
      
      if (this.ppDisplayList.length && domClass.contains(this.sysPattern_widgetClearBtn, "c3_hide"))
      {
        domClass.remove(this.sysPattern_widgetClearBtn, "c3_hide");
      }
      
      this.saveRule();
    },
    
    // @param: Pattern string
    displayPattern: function(patternListStr)
    {
      // Convert to JSON 
      let pattern = JSON.parse(patternListStr);
      if (pattern && pattern.length) 
      {
        domClass.remove(this.sysPattern_widgetClearBtn, "c3_hide");
      }
      // Parse pattern and create display widget for each pattern
      // Pattern structure: [{},{},{}]
      // display type is dependent on name of each {}
      // name: group / uniqueName / * / + 
      for (var i = 0; i < pattern.length; i++)
      {
        this.createPatternPartDisplay(pattern[i]);
      }
    },
    
    createPatternPartDisplay: function (ctx)
    {
      // pattern part = 
      // event --> {name: ..., freq: ..., vars: ...}
      // {name: "*"} OR {name: "+"}
      // {name: group, ordinal: all, order: true/false, events: [event1, event2, ....]}
      var displayType = (ctx.name === "*" || ctx.name === "+" || ctx.name === "group") ? ctx.name : "specificActivity"; 
      var ppDisplay = new PatternPartDisplay({ctx: ctx, displayType: displayType});
      ppDisplay.setParent(this);
      ppDisplay.setId(this._getUqSfx());
      ppDisplay.setCallback(this.editPatternPart);
      ppDisplay.startup();
      
      domConstruct.place(ppDisplay.domNode, this._sysEvtPatternParts, "last");
      
      this.ppDisplayList.push(ppDisplay);
    },
    
    // called when a pattern part display is clicked
    editPatternPart: function(ctx)
    {
      // called when the pattern part display is clicked
      // opens up a Pattern Part Definer with the saved part of the pattern
      // in a dialog box
      
      var patternPart = ctx.patternPart;
      var id = ctx.id;
      var displaySettings = this.getDisplaySettings(id);
      
      var patternPartDef = new PatternPartDefiner({
        ctx: patternPart,
        cmd: "edit",
        ppDisplayId: id,
        displaySettings: displaySettings
      });
      patternPartDef.setParent(this);
      patternPartDef.setCallback(this.onDoneCallback);
      patternPartDef.startup();
      this.own(patternPartDef);
    },
    
    // when a plus is clicked open the popup where you define the pattern part
    addPatternPart: function()
    {
      // Open a Pattern part definer (cmd: new)
      // with a callback to create a pattern part display widget - partCallback
      var displaySettings = this.getDisplaySettings();
      
      var patternPartDef = new PatternPartDefiner({
        ctx: {},
        cmd: "new",
        displaySettings: displaySettings
      });
      patternPartDef.setParent(this);
      patternPartDef.setCallback(this.onDoneCallback);
      patternPartDef.startup();
      this.own(patternPartDef);
    },
    
    /**
     * The first item of a pattern cannot be +
     * A pattern cannot have * and + options next to each other 
     */
    getDisplaySettings: function(id)
    {
      // if id - this means its in edit mode
      // this id is assigned to a specific pattern part in the pattern part display list
      // you can retrieve each pattern parts id with its getId() method 
      // if no id - then it is in new mode
      var displaySettings = {};
      var index = (this.ppDisplayList.length);
      
      // check if there is a item in ppDisplay with that id
      if (id)
      {
        if (this.ppDisplayList && this.ppDisplayList.length)
        {
          for (var i = 0; i < this.ppDisplayList.length; i++)
          {
            if (this.ppDisplayList[i].getId() === id)
            {
              index = i;
              break;
            }
          }
        }
      }
      
      // use the index to decide whether the display settings should be restrictive or not
      if (this.ppDisplayList && this.ppDisplayList.length)
      {
        if (index === 0)
        {
          // check pattern on right
          displaySettings = this.checkPattern(index + 1, index);
        }
        else if (index === this.ppDisplayList.length - 1)
        {
          // check pattern on left
          displaySettings = this.checkPattern(index - 1, index);
        }
        else if (index > 0 )
        {
          // check pattern on left
          displaySettings = this.checkPattern(index - 1, index);
          
          // combine the restrictions from the left with the restrictions from the right 
          displaySettings = lang.mixin(displaySettings, this.checkPattern(index + 1, index));
        }
      }
      
      // Last check
      // if there are no restrictions and there are no pattern parts 
      // (i.e. we are attempting to create the first pattern part)
      // then restrict option1 (cannot create a pattern starting with +)
      if (JSON.stringify(displaySettings) === "{}" && this.ppDisplayList.length === 0)
      {
         displaySettings.option1 = false;
      }
      
      return displaySettings;
    },
    
    checkPattern: function(index, caller)
    {
      // check the pattern at this index of ppDisplayList
      // return the displaySettings based on the name of this pattern part
      var ds = {};
      // Mandatory
      // the first item (caller = 0) cannot show option 1 - i.e. set it to false
      if (index === 1 && caller === 0)
      {
        ds.option1 = false;
      }
      // continue to check the rule for the item at index
      if (this.ppDisplayList[index])
      {
        var r = this.ppDisplayList[index].getRule();
        if (r && r.name && (r.name === "*" || r.name === "+"))
        {
          ds.option1 = false;
          ds.option2 = false;
        }
      }
      return ds;
    },
    
    ppDisplayListIsValid: function()
    {
      // currently only has one check
      // if there is only one part to the pattern
      // it cannot be *
      if (this.ppDisplayList.length === 1)
      {
        var r = this.ppDisplayList[0].getRule();
        if (r.name === "*")
        {
          udc.alert(Res.ppDisplayInvalid);
          return false;
        }
      }
      return true;
    },
    
    //...............................
    clear: function()
    {
      this.destroyWidgets();
      domConstruct.empty(this._sysEvtPatternParts);
      
      domClass.add(this.sysPattern_widgetClearBtn, "c3_hide");
      
      this.saveRule();
    },
    
    // ....................................
    // Generate unique suffix string. Used in click
    // events to access matching element in list.
    _getUqSfx: function()
    {
      this.ppdispCount = this.ppdispCount + 1;
      return "_xy_" + (new Date()).getTime() + "_" + this.ppdispCount + "_x";
    },

    //...............................
    _setEvents: function()
    {
      this.own(
        // _time - change
        on(this._addPatternPart, "click", lang.hitch(this, this.addPatternPart)),
        
        // _tsUnit - change
        on(this.sysPattern_widgetClearBtn, "click", lang.hitch(this, this.clear))
      );
    },

    // .....................................................
    destroy: function()
    {
      this.destroyWidgets();
      this.inherited(arguments);
    },
    
    destroyWidgets: function()
    {
      // recursively destroy the list of widgets
      if (this.ppDisplayList && this.ppDisplayList.length)
      {
        for (var i = 0; i < this.ppDisplayList.length; i++)
        {
          if (this.ppDisplayList[i] && typeof this.ppDisplayList[i].destroyRecursive === "function")
          {
            this.ppDisplayList[i].destroyRecursive();
            this.ppDisplayList[i] = null;
          }
        }
      }
      this.ppDisplayList = [];
    },

    _end: 0
  });

});
