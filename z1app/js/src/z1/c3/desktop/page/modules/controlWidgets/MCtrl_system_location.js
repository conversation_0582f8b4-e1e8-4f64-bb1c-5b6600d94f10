/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define(["dojo/_base/declare", "dojo/_base/lang", "dojo/on",
  "udc/udc", "z1/c3/desktop/start",
  "udc/core/commons/loader",
  "udc/core/rt/widgets/_ComplexBaseWidget",
  "dojo/text!z1/c3/desktop/page/templates/modules/controlWidgets/MCtrl_system_location.html",
  "dojo/i18n!z1/c3/desktop/page/nls/MCtrl_system_locationRes",
  "z1/c3/desktop/page/modules/ModuleDefineCtx",
  "z1/c3/desktop/page/modules/controlWidgets/MCtrl_TriggerLocations",
  "dojo/dom-class",
  "dojo/topic",
  "dojo/dom-construct",
  "dojo/Deferred",
  "dojo/promise/all"
],
function(declare, lang, on, udc, z1, loader, BaseWidget, templateStr, Res,
  ModuleDefineCtx, MCtrl_TriggerLocations,
  domClass, topic, domConstruct,
  Deferred, all
)
{

  /**
   * Creates a widget to edit Define Trigger.
   */
  return declare(
  BaseWidget,
  {
    templateString: templateStr,
    Res: Res,

    /**
     * Creates an instance of this page.
     * 
     * @param {Object}
     *  {
     *    ctrlCtx.field: system:location:field - also params[i].name
     *  }
     */
    constructor: function(params)
    {
      this.inherited(arguments);
      params = params || {};


      // Example payload
      // payload.Rule and payload.Params
      // payload.params[..{name:userLoc,..},..] contains
      // definition of the control for
      // payload.rule[...{field: 'userLoc',..},..]
      //params: [
      //  ..
      //  {
      //    name: "userLoc",
      //    type: "",
      //    description: "User's last known location matches one of the entered zipcodes",
      //    display: "User's last known location",
      //    control: "{"control":"system:rule","controlOption":"{\"edit\":\"false\"}","isRequired":"true"}",
      //    type: "",
      //    value: "valueOf(zipcodes)"
      //  }
      //],
      //rule: [
      //  ..
      //  {
      //    type: "z1_template",
      //    field: "userLoc",
      //    operator: "in",
      //    useContext: false,
      //    value: "?{userLoc}"
      //  },
      //  ..
      //]

    },

    // ////////////////////////////////////////////////////////////////////////////
    // Implementations

    postCreate: function()
    {
      this.inherited(arguments);
    },

    // .....................................................
    startup: function()
    {
      this.inherited(arguments);

      this._d = ModuleDefineCtx.mData.getData();

      this._templateData = ModuleDefineCtx.mData._templateData;

      this._showSystemLocation();

      this._setEvents();
      this._subscribe();
    },

    //...............................
    // control: 'system:location'
    _showSystemLocation: function()
    {
      domConstruct.empty(this._locationsCont);

      var pObj = {
        _parent: this,
        param: this.param
      };

      var _gfData = ModuleDefineCtx.mData._gfData;
      if (_gfData && _gfData.length)
      {
        pObj._gfData = _gfData;
      }

      this.triggerLocations = new MCtrl_TriggerLocations(pObj);
      this.triggerLocations.startup();
      domConstruct.place(this.triggerLocations.domNode, this._locationsCont);
      this.own(this.triggerLocations);
    },

    //....................................
    // Read form fields and set values into
    // this._d (ref ModuleDefineCtx.js:mData._d)
    // Currently the data changes are saved to _d
    // automatically
    getProperties: function()
    {
      // Data changes are saved into ModuleDefineCtx > _d
    },

    //...................................
    // Subscribe to events.
    _subscribe: function()
    {
      this.own(
        topic.subscribe("/trigger/saved", lang.hitch(this, function(ctx)
        {
        }))
      );
    },
   
    //...............................
    _setEvents: function()
    {
      //this.own(
      //);
    },

    // .....................................................
    destroy: function()
    {
      this.inherited(arguments);

    },

    _end: 0
  });

});
