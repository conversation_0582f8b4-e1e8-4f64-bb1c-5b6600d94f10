define(
{
  root :
  {
    triggersForTitle1: "Triggers created for event",
    allTriggersTitle: "All Triggers",
    locationTitle: "Location based Triggers",
    usersTitle: "Segments based Triggers",
    activitiesTitle: "Activity based Triggers",

    configureSignalPaletteTitle: "Triggers",
    currentSignalPalette: "Current Triggers",
    createSignalPalette: "Create New Trigger",
    deleteDlgMsg: "Do you want to delete Trigger?",
    deleteDlgTitle: "Delete Trigger",
    alertDelSigTitle: "Delete failed.",
    alertDelSigMsg: "Trigger is being used in one or more Interactions. To delete the Trigger, first remove it from all Campaigns.",
    emptyDescription: "No items exist. Click below button to create new items.",
    sigDesc: "Create triggers from incoming events to initiate real-time actions in Interactions",

    eventNameLbl: "Event: ",

    paramDescr1: "Incoming event",
    paramDescr2: "comes with the following parameters. You can select specific values of the parameters and/or the device properties that are the source of this event to create trigger.",
    paramsListTitle: "Event Parameters",
    infoTitle: "Information",
    
    triggerDisabledNotice: "System has been restricted from using this event as a trigger.",

    _end : 0
  }

// de: true,
// "de-at" : true
});