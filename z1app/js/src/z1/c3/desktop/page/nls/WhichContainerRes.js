define(
{
  root :
  {
    paletteTitle : "Triggers",
    customerGrpTitle: "Customer Group",
    activityTitle: "Time Period",
    savingText: "Saving...",
    addSignalBtnLabel : "Add Signal",
    newSignal: "Create New Signal Group",
    untitledSignal: "Untitled Signal",
    untitledSignalGroup: "Untitled Signal Group",
    goalDialogSignalGroup: "Create New Signal",
    goalSignalGroupHint: "Enter a Signal Group Name",
    inputLabel: "Name: ",
    newSignalGroupBtn: "New Group",
    deleteSignalGroupBtn: "Delete Group",
    renameSignalGroup: "Rename Signal Group",
    deleteGroupTitle: "Delete Signal Group",
    deleteGroupMsg: "Do you want to delete the existing signal group ",
    newLabel: "New Group: ",
    clear: "Clear: ",
    save: "Save: ",
    addSignal: "Add Signal: ",
    newToolTip: "Drag and drop Customer Group, Time Period and Signal(s) from right hand side "+
    			"to the middle canvas section.",
  	saveToolTip:"A Signal Group will be saved automatically as items are dropped on canvas. ",
  	clearToolTip:"Clears (erases) all the content dropped inside each 'Signal'. " ,
  	addSignalToolTip:"Creates an additional 'Signal'. Signal Group is made up of multiple Signal(s).",
    deleteToolTip:"Deletes selected Signal Group.",
    newToolTipLbl: "New: ",
    saveToolTipLbl: "Save: ",
    addSignalToolTipLbl: "Add Signal: ",
    deleteToolTipLbl: "Delete: ",
    gettingStarted: "Getting Started",
    defineActions: "Define Actions",
    alertDuplicateSG: "Same name Signal Group already exists.",
    _end : 0
  }

// de: true,
// "de-at" : true
});