define({
  root: {
    title: "Ad Hoc Query",
    desc: "Deep dive into the data that has been captured and stored in the system",
    
    untitledQuery: "Untitled Query", 
    queryTitle: "Query",
	  queryHint: "Enter Query Name",
	  inputLabel: "Provide a Unique Name: ",
	  queryDesc: "Enter description",
	
	  queryName: "Query Name: ",
	
	  errorNameTxt: "Error: Invalid name - valid characters in name: [alpha-numeric, _, -, space] or it is too long",
	  errorDescriptionTxt: "Error: Invalid description - using invalid characters or it is too long",
    	
    activity: "Activity",
    trend: "Trend",
    query: "Query",
    filter: "Filters",
    groupBy: "Group By",
    
    1: "1",
    2: "2",
    3: "3",
    4: "4",
    5: "5",
    
    undefinedQuery: "Undefined Query",
    baseDesc: "Define your query to aggregate an event across different dimensions, start by selecting the time range and an activity...",
    query: "Query ",
    save: "Save",
    close: "Cancel",
    and: "and ",
    groupedBy: "grouped by ",
    where: "where ",
    to: " to ",
    
    aggDescriptor: "and aggregate the counts of each parameter ",
    computeDescriptor: "to compute ",
    dateDescriptor: "for the date range ", 
    
    step1Desc: "Select the desired time range",
    step2Desc: "Select a specific activity to query",
    step3Desc: "Aggregate the activity stream across various parameters",
    step4Desc: "Enhance your query",
    step5Desc: "Select a query qualifier",
    
    what: "Define data to be aggregated: ",
    selectGroup: "If desired, select a criteria to group by:",
    selectGroupByTime: "If desired, group by time:",
    boundaries: "Define filters: ",
    timeRange: "Select a time range",
    
    selectActivity: "Select an available activity",

    optional: "(Optional)",

    addGroupBySelectorBtnHint: "Add multiple Group By criteria",

    opNoneTime: "none",
    opMonth: "per month",
    opDay: "per day",
    opHour: "per hour",
    opMinute: "per minute",
    opSecond: "per second",
    
    none: "None Defined",

    noActivities: "There are no activities in this system",
    noActDataFound: "There is no data currently availabe for this activity",

    infoTxtQueryEnhancer: "Group the query results by time or event attributes and funnel the results using filters on event attributes, this is an optional feature but it can only be used if all prior steps are completed.",
    infoTxtQueryQualifier: "Apply query qualifiers to further investigate the results. Qualifiers can only be used if the query has been enhanced.",
    
    _end: 0
  }

// de: true,
// "de-at" : true
});