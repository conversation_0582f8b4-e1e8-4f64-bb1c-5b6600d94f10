define(
{
  root :
  {
    dndHereHint : "Drag and drop a Signal here.",
    dndHereHintSegment : "Drop Customer Group",
    dndHereHintActivity : "Drop Time Period",
    dndHereHintSource : "Signal sources",
    lastNDaysTitle: "Enter last N days value",
    inputDaysLabel: "Last N days: ",
    defaultValueDays: "30",
    lastNHoursTitle: "Enter last N hours value",
    inputHoursLabel: "Last N hours: ",
    defaultValueHours: "6",
    lastNMinutesTitle: "Enter last N minutes value",
    inputMinutesLabel: "Last N minutes: ",
    defaultValueMinutes: "30",
    alertTitle:"Multiple Items Dropped",
    alertMsg1: "Only one item of type ",
    alertMsg2: " can be added at a time.",
    deleteSignal: "Delete this Signal",
    clearSignalTitle: "Delete Signal",
    clearSignalMsg: "Delete Signal canvas and associated data?",
    alertTSEmptyTitle: "Time Period is empty",
    alertTSEmptyMsg: "Time Period must be specified to add more than one signal.",
    alertOneSSOnlyTtl: "Information",
    alertOneSSOnlyMsg: "Only one item of type Signal Source can be added to Signal.",
    vMsgNotNumber: "Enter number value.",
    vMsgInvalidRange: "Value is out of range.",
    _end : 0
  }

// de: true,
// "de-at" : true
});