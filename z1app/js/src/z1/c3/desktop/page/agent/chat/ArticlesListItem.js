/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 * 
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define([
  "dojo/_base/declare", 
  "dojo/_base/lang", 
  "udc/udc",
  "udc/core/commons/loader",
  "z1/c3/desktop/start",
  "udc/core/rt/widgets/_ComplexBaseWidget",
  "dojo/text!z1/c3/desktop/page/templates/agent/chat/ArticlesListItem.html",
  "dojo/i18n!z1/c3/desktop/page/nls/ArticlesListItemRes",
  "dojo/dom-class",
  "dojo/dom-construct",
  "dojo/topic"
  ], 
  function(declare, lang, udc, loader, z1, _ComplexBaseWidget,
    templateStr, Res, domClass, domConstruct, topic
  )
{

  /**
   * Creates conversation list widget.
   */
  return declare(_ComplexBaseWidget, {
    templateString: templateStr,

    Res: Res,

    /**
     * Create Articles list pane.
     * 
     * @param {Object}
     * @param {Object}
     *          parentNode
     */
    constructor: function(params, parentNode)
    {

      this.inherited(arguments);
      this._item = params;
      
      // NOTE: message 'id' is changed to 'itemId'

      if (!params || !params.title)
      {
        this.title = " ";
      }
      if (!params || !params.description)
      {
        this.description = " ";
      }

      if(params && params.parentWidget)
      {
        this._parent = params.parentWidget;
      }

    },

    postCreate: function()
    {
      this.inherited(arguments);

    },

    startup: function()
    {
      this.inherited(arguments);
      this._subscribe();
    },

    //...........................
    setData: function(data)
    {
      this._data = data;
    },

    //...........................
    // Subscribe to events.
    _subscribe: function ()
    {
    },


    //..........................
    destroy: function()
    {
      //this._emptyOnExitWidgets();
      //this._gridData = null;
      this.inherited(arguments);
    },

    _end: 0
  });

});
