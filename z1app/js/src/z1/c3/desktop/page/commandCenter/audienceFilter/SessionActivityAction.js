/*
 * Copyright 2013 by ZineOne Inc. All rights reserved.
 *
 * This software is the confidential and proprietary information of ZineOne Inc.
 */
define(["dojo/_base/declare", "dojo/_base/lang", "dojo/on",
  "dojo/dom-construct", "udc/udc",
  "dojo/topic",
  "udc/core/rt/widgets/_BaseWidget",
  "dojo/text!z1/c3/desktop/page/templates/commandCenter/audienceFilter/SessionActivityAction.html",
  "dojo/i18n!z1/c3/desktop/page/nls/SessionActivityActionRes",
  "z1/c3/desktop/start",
  "dojo/promise/all",
  "z1/c3/desktop/page/commandCenter/audienceFilter/SessionActivityActionItem"
],
function(declare, lang, on, domConstruct,
  udc, topic, BaseWidget, templateStr, Res, z1, all,
  SessionActivityActionItem
)
{
  return declare(BaseWidget, {
    templateString: templateStr,
    Res: Res,
    actionItemDataChangeStatusUri: "/segments/actionItem/ctx/status",
    actionOperatorChangeUri: "/segments/actionItem/operator/change",
    actionItemDeleteUri: "/segments/actionItem/delete",
    operatorMap: {
      ">=": "at least",
      "<=": "1 to",
      "==": "exactly",
    },

    constructor: function(params, parentNode)
    {
      this.inherited(arguments);
      params.cmd = params.cmd || 'new';
      params.currentIndex = 0;
      params.sessionActivityActionItems = {};
    },

    postCreate: function()
    {
      this.inherited(arguments);
    },

    // .....................................................
    startup: function()
    {
      this.inherited(arguments);

      this.populate();
      this._setEvents();
      this._subscribe();
    },

    populate: function()
    {
      this.toggleOperator();
      all([
        this.ctx.fetchEvents(),
        this.ctx.fetchEventsNestedParams()
      ]).then(lang.hitch(this, function() {
        this.initActionItem();
        this.initDescription();
      }));
      this.addActionItem();
      this.toggleActionItem(true);

      if(this.cmd === 'new') this._onEdit.classList.add('c3_hide');
    },

    initDescription: function()
    {
      domConstruct.empty(this._actionDescription);
      this.generateDescription();
    },

    generateDescription: function()
    {
      const data = this.ctx.currData.behavior;
      const noOfItems = data.length;
      if(noOfItems < 1) return;

      const wrapper = document.createElement('div');
      wrapper.classList.add('c3_cols_nowrap', 'c3_gp2', 'c3_fs14', 'c3_pd_24l');

      data.forEach((item, index) => {
        const { timeSelector, event, attributes } = item;
        const startingText = event.operator === "<" && event.value === "1" ? "Did Not" : "Did";

        const eventName = this.ctx.eventList.find(e => e.name === event.field)?.displayName ?? event.field;
        let firstLine = `${startingText} ${eventName || ""} `;

        if (startingText === 'Did') {
            const operatorTxt = this.operatorMap[event.operator] || "";
            const valueTxt = event.value;
            firstLine += `${operatorTxt} ${valueTxt} time(s) `;
        }

        firstLine += "this session";
        const firstDiv = document.createElement('div');
        firstDiv.classList.add('c3_lh20');
        firstDiv.textContent = firstLine;
        wrapper.append(firstDiv);

        if (attributes.length > 0) {
          const attributeWrapper = document.createElement('div');
          attributeWrapper.classList.add('c3_pd_26l');

          attributes.forEach((attr, attrIndex) => {
            let { field, operator, value } = attr;

            if (field !== '' && operator !== '' && value !== '') {
              if (attrIndex === 0) {
                const firstAttrDiv = document.createElement('div');
                firstAttrDiv.classList.add('c3_lh20');
                firstAttrDiv.textContent = "IF";
                attributeWrapper.append(firstAttrDiv);
              }

              if (operator === 'isNull' || operator === 'notNull') value = "";

              field = this.getDataFrom(field, 'name', index)
              const lineTxt = `${field} ${this.ctx.reverseOperatorMap[operator]} ${value}`;
              const criteriaDiv = document.createElement('div');
              criteriaDiv.classList.add('c3_lh20');
              criteriaDiv.textContent = lineTxt;
              attributeWrapper.append(criteriaDiv);

              if (attrIndex < attributes.length - 1) {
                const criteriaOpDiv = document.createElement('div');
                criteriaOpDiv.classList.add('c3_lh20');
                criteriaOpDiv.textContent = "AND";
                attributeWrapper.append(criteriaOpDiv);
              }
            }
          });
          wrapper.append(attributeWrapper);
        }

        if (index < noOfItems - 1) {
            const OpDiv = document.createElement('div');
            OpDiv.classList.add('c3_lh20');
            OpDiv.textContent = this.ctx.currData.orOp.includes('activity') ? 'OR' : 'AND';
            wrapper.append(OpDiv);
        }
      });

      this._actionDescription.append(wrapper);
    },

    getDataFrom: function(value, comparedKey = 'displayName', index)
    {
      let eventParams = this.sessionActivityActionItems[index + 1]?.eventParams
      if(eventParams)
      {
        for (let item of eventParams) {
            if (item[comparedKey] === value) {
                return item.displayName
            }
        }
      }
      return value;
    },

    initActionItem: function()
    {
      this.currentIndex = 0;
      domConstruct.empty(this._actionItemBody);
      if(this.ctx.currData.behavior.length > 0)
      {
        this.ctx.currData.behavior.forEach((existingData) => {
          this.addActionItem(existingData)
        })
      }
    },

    addActionItem: function(existingData = null)
    {
      this.currentIndex = this.currentIndex + 1;
      const sessionActivityActionItem = new SessionActivityActionItem({
        cmd: this.cmd,
        existingData: existingData,
        currentIndex: this.currentIndex,
        dataChangeStatusUri: this.actionItemDataChangeStatusUri,
        ctx: this.ctx,
        actionOperatorChangeUri: this.actionOperatorChangeUri,
        actionItemDeleteUri: this.actionItemDeleteUri,
        updateActionBehavior: (data) => {
          this.updateBehaviorItem(data);
        }
      })
      sessionActivityActionItem.startup();
      this.sessionActivityActionItems[this.currentIndex] = sessionActivityActionItem;

      domConstruct.place(this.sessionActivityActionItems[this.currentIndex].domNode, this._actionItemBody, 'last');
      this.own(this.sessionActivityActionItems[this.currentIndex])
    },

    toggleOperator: function()
    {
      this.ctx._parent.andOperator.checked = true;
      this.ctx._parent._andOperatorWrp.classList.add('c3_fw600');
      this.ctx._parent._orOperatorWrp.classList.remove('c3_fw600');
      if(this.ctx.currData.orOp.includes('activity'))
      {
        this.ctx._parent.orOperator.checked = true;
        this.ctx._parent._orOperatorWrp.classList.add('c3_fw600');
        this.ctx._parent._andOperatorWrp.classList.remove('c3_fw600');
      }
    },

    toggleActionItem: function(show = false)
    {
      if(this._actionItemBody.classList.contains('c3_hide') || show)
      {
        this._dropDownCont.setAttribute('open', true)
        this._actionItemBody.classList.remove('c3_hide')
        this._onEdit.classList.add('c3_hide')
        this._actionDescription.classList.add('c3_hide')
      }
      else
      {
        this._dropDownCont.removeAttribute('open')
        this._actionItemBody.classList.add('c3_hide')
        if(this.cmd === 'edit') this._onEdit.classList.remove('c3_hide')
        this._actionDescription.classList.remove('c3_hide')
      }
    },

    setOperator: function(targetVal)
    {
      let currValue = this.ctx.currData.orOp.split(',') || [];
      if(targetVal === 'or' && !currValue.includes('activity'))
      {
        currValue.push('activity')
      }
      else if(targetVal === 'and')
      {
        currValue = currValue.filter(item => item !== 'activity')
      }
      currValue = currValue.filter(item => item.trim() !== '');
      this.ctx.setCurrData({ orOp: currValue.join(',') })
      this.initDescription();
      topic.publish(this.actionOperatorChangeUri, targetVal)
    },

    updateBehaviorItem: function(data)
    {
      let behavior = [...this.ctx.currData.behavior];
      const index = behavior.findIndex(item => item.unqId === data.unqId);
      if (index !== -1) {
        // If unqId exists, update the corresponding object
        behavior[index] = {...data};
      } else {
        // If unqId doesn't exist, add a new object to the array
        behavior.push({...data});
      }

      this.ctx.setCurrData({ behavior: behavior })
      this.initDescription();
    },

    removeBehaviorItem: function(unqId)
    {
      let behavior = [...this.ctx.currData.behavior];
      behavior = behavior.filter(item => item.unqId !== unqId);
      this.ctx.setCurrData({ behavior: behavior });
      this.initActionItem();
      this.initDescription();
    },

    //..............................
    _setEvents: function()
    {
      this.own(
        // on(this._dropDown, "click", lang.hitch(this, function(e) {
        //   if(e.target.tagName !== 'INPUT'
        //       && e.target.tagName !== 'LABEL')
        //   {
        //     this.toggleActionItem();
        //   }
        // })),
        on(this._onEdit, "click", lang.hitch(this, function(e) {
          e.stopPropagation();
          this.toggleActionItem();
        })),
        on(this._addActionBtn, "click", lang.hitch(this, function(e) {
          this.addActionItem();
          this.toggleActionItem(true);
        }))
      )
    },

    //.........................
    _subscribe: function()
    {
      this.own(
        topic.subscribe(this.actionItemDataChangeStatusUri, lang.hitch(this, function(ctx) {
          this.updateBehaviorItem(ctx.data);
        })),
        topic.subscribe(this.actionItemDeleteUri, lang.hitch(this, function(ctx) {
          this.removeBehaviorItem(ctx.unqId)
        })),
        topic.subscribe("segments/action/openDropDown", lang.hitch(this, function() {
          this.toggleActionItem(true);
        })),
        topic.subscribe("segments/action/setOperator", lang.hitch(this, function(targetVal) {
          this.setOperator(targetVal);
        }))
      );
    },

    // .....................................................
    destroy: function()
    {
      this.inherited(arguments);
    },

    _end: 0
  });

});
