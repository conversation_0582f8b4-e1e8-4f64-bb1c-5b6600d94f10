define(
["udc/udc", "dojo/_base/lang", "dojo/Deferred",
  "z1/c3/desktop/data/experiences/ExperiencesData",
  "z1/c3/desktop/data/segments/SegmentsData",
  "z1/c3/desktop/data/triggers/TriggersData",
  "z1/c3/desktop/data/metrics/DashboardsData",
  "z1/c3/desktop/data/charts/ChartsData",
  "z1/c3/desktop/page/machineLearning/MLInstancesData"
],
function(udc, lang, Deferred, ExperiencesData, SegmentsData, TriggersData,
  DashboardsData, ChartsData, MLInstancesData
)
{

  var InstancesData = {

    api: {

      fetchAll : function(ctx = {})
      {
        var deferr = new Deferred();

        if (ctx.type == "segment")
        {
          SegmentsData.api.fetchAll(ctx).then(lang.hitch(this, function (data){
            deferr.resolve(data);
          }))
          return deferr.promise;
        }
        else if (ctx.type == "experience")
        {
          ExperiencesData.api.fetchAll(ctx).then(lang.hitch(this, function (data){
            deferr.resolve(data);
          }))
          return deferr.promise;
        }
        else if (ctx.type == "signals")
        {
          TriggersData.api.fetchAll(ctx).then(lang.hitch(this, function(data){
            deferr.resolve(data);
          }))
          return deferr.promise;
        }
        else if (ctx.type == "ml")
        {
          MLInstancesData.fetchAllModelsData(ctx).then(lang.hitch(this, function(data){
            deferr.resolve(data);
          }))
          return deferr.promise;
        }
        else if (ctx.type == "dashboard")
        {
          DashboardsData.api.fetchAll(ctx).then(lang.hitch(this, function(data){
            deferr.resolve(data);
          }))
          return deferr.promise;
        }
        else if (ctx.type == "sharedWithMeDashboard")
        {
          DashboardsData.api.fetchShared(ctx).then(lang.hitch(this, function(data){
            deferr.resolve(data);
          }))
          return deferr.promise;
        }
        else if (ctx.type == "sharedDashboard")
        {
          DashboardsData.api.fetchShared(ctx).then(lang.hitch(this, function(data){
            var retData = [];
            if (data && Array.isArray(data) && data.length)
            {
              for (var i = 0; i < data.length; i++)
              {
                if (data[i].payload2)
                {
                  var pl = JSON.parse(data[i].payload2);
                  if (pl.sharedDisplay && pl.sharedDisplay == true)
                  {
                    retData.push(data[i]);
                  }
                }
                else
                {
                  retData.push(data[i]);
                }
              }
            }
            deferr.resolve(retData);
          }));
          return deferr.promise;
        }
        else if (ctx.type == "industryDashboard" || ctx.type == "useCaseDashboard" || ctx.type == "modelDashboard")
        {
          var category = {
            "industryDashboard": "industry",
            "useCaseDashboard": "usecase",
            "modelDashboard": "model"
          };
          DashboardsData.api.fetchShared(ctx).then(lang.hitch(this, function(data){
            var retData = [];
            if (data && Array.isArray(data) && data.length)
            {
              for (var i = 0; i < data.length; i++)
              {
                if (data[i].payload2)
                {
                  var pl = JSON.parse(data[i].payload2);
                  if (pl.sharedDisplay && pl.category && pl.sharedDisplay == true && pl.category == category[ctx.type])
                  {
                    retData.push(data[i]);
                  }
                }
              }
            }
            deferr.resolve(retData);
          }));
          return deferr.promise;
        }
        else if (ctx.type == "chart")
        {
          ChartsData.api.fetchAll(ctx).then(lang.hitch(this, function(data){
            deferr.resolve(data);
          }))
          return deferr.promise;
        } 
        deferr.resolve([]);
        return deferr.promise;
      },
      
      fetchStatsData: function(ctx = {}, mData)
      {
        var deferr = new Deferred();
        if(ctx.type == "segment")
        {
          SegmentsData.api.fetchStatsData(ctx, mData).then(lang.hitch(this, function (data){
            deferr.resolve(data);
          }))
          return deferr.promise;
        }
        else if(ctx.type == "experience")
        {
          if (ctx.journeyType == "c1")
          {
            // do not fetch the Sent data for Scheduled Interactions - ZMOB-16570
            /*ExperiencesData.api.fetchAllCampaignonceStats(ctx).then(lang.hitch(this, function(data){
              deferr.resolve(data)
            }))*/
          }
          else if (ctx.journeyType == "campaign")
          {
            ExperiencesData.api.fetchAllCampaignStats(ctx).then(lang.hitch(this, function(data){
              deferr.resolve(data)
            }))
          }
          return deferr.promise;
        }
        else if (ctx.type == "signals")
        {
          udc.log("Signals Stats Call Not Implemented");
        }
        
        deferr.resolve([]);
        return deferr.promise;
      },

      //........................................
      // table type map
      getTableMap: function (ctx = {})
      {
        if (ctx.type == "experience")
        {
          return ExperiencesData.api.getTableMap(ctx);
        }
        else if (ctx.type == "segment")
        {
          return SegmentsData.api.getTableMap(ctx);
        }
        else if (ctx.type == "signals")
        {
          return TriggersData.api.getTableMap(ctx);
        }
        else if (ctx.type == "ml")
        {
          return MLInstancesData.getTableMap(ctx);
        }
        else if (ctx.type == "dashboard")
        {
          return DashboardsData.api.getTableMap(ctx);
        }
        else if (ctx.type == "sharedDashboard" || ctx.type == "sharedWithMeDashboard"
          || ctx.type == "industryDashboard" || ctx.type == "useCaseDashboard" || ctx.type == "modelDashboard")
        {
          return DashboardsData.api.getSharedTableMap(ctx);
        }
        else if (ctx.type == "chart")
        {
          return ChartsData.api.getTableMap(ctx);
        }
        
        return {};
      },

      //........................................
      // @param {
      //   type - segment|experience|trigger
      //   data - data from fetch all
      // }
      prepareDataForTable: function (ctx = {})
      {
        if (ctx.type == "experience")
        {
          // ~ change to ExperiencesData
          return ExperiencesData.api.prepareDataForTable(ctx);
        }
        else if (ctx.type == "segment")
        {
          return SegmentsData.api.prepareDataForTable(ctx);
        }
        else if (ctx.type == "signals")
        {
          return TriggersData.api.prepareDataForTable(ctx);
        }
        
        return ctx.data;
      },
      
      prepareStatsDataForTable: function (ctx = {})
      {
        if(ctx.type == "experience")
        {
          return ExperiencesData.api.prepareStatsDataForTable(ctx);
        }
        else if(ctx.type == "segment")
        {
          return SegmentsData.api.prepareStatsDataForTable(ctx);
        }
        else if (ctx.type == "signals")
        {
          udc.log("Signals Stats Data prep is not implemented");
        }
        
        return ctx.data;
      },
      
      mergeStats: function(ctx = {})
      {
        if (ctx.type == "experience")
        {
          return ExperiencesData.api.mergeStats(ctx);
        }
        else
        {
          // for all other types
          var _mergeStats = function(i, s)
          {
            let si = s.findIndex((st) => {return st.id === ctx.mData[i].id;});
            if (si !== -1)
            {
              Object.assign(ctx.mData[i], s[si]);
            }
          };
          
          if (ctx.mData && Array.isArray(ctx.mData))
          {
            ctx.mData.forEach((m, i) => {
              _mergeStats(i, ctx.statsData);
            });
          }
        }
      },

      _end: 0
    },

    _end: 0
  };

  return InstancesData;
});
