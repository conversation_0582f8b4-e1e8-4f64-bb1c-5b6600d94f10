{"endpoints": {"downloadAllData": {"url": "/c3/data/query/download?queryName=", "type": "POST"}, "fetchDownloadStatus": {"url": "/c3/data/query/stats?queryName=", "type": "GET"}, "clearAllStatus": {"url": "/c3/data/query/deleteStats?queryName=", "type": "POST"}, "deleteStatusItem": {"url": "/c3/data/query/deleteOneStats/", "type": "POST"}}, "visibleOptions": {"showFetchAllData": true, "showFetchDownloadStatus": true, "showDownloadAllData": true, "showDownloadTableData": true, "showClearAllStatus": true, "showDeleteStatusItem": true}, "additionalRes": {"deleteAllStatusConfirmTitle": "Clear Statuses", "deleteAllStatusConfirmMsg": "Do you want to delete all download statuses for this query?", "deleteStatusItemConfirmTitle": "Clear Status Record", "deleteStatusItemConfirmMsg": "Do you want to delete this status record for this query?"}}