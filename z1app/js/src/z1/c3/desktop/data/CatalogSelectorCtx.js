define([
  "dojo/_base/declare", "udc/core/commons/loader",
  "udc/udc", "dojo/_base/lang",
  "dojo/topic",
  "dojo/Deferred",
  "dojo/promise/all",
  "z1/common/C3Util"
],
function(declare, loader, udc, lang, topic, Deferred, all,
  C3Util)
{
  const RULE_FIELD = "z1_pdp_top_category_vector";
  const OPERATOR = "intersects";
  const SEP = ",";

  const fetchCatalog = (caller) => {
    var defer = new Deferred();

    loader.GET("/c3/data/commandcenter/categories").then(data => {
      if (data?.status === "fail")
      {
        defer.resolve(processCategories(caller, []));
        return;
      }

      defer.resolve(processCategories(caller, data));
    }, err => {
      defer.resolve(processCategories(caller, []));
    });

    return defer.promise;
  }

  const processCategories = (caller, data) => {
    if (!data) return [];

    let cData = [];
    let categores = getSelCatInCatologFormat(caller, data, getRuleObject(caller));

    cData = data?.map && data.map(d => {
      let value = categores.find(c => c.id === d.id )

      return {
        id: d.id,
        displayName: d.displayName,
        values: d.values,
        isSelected: value ? true : false
      };
    });

    return cData;
  }

  const getRuleObject = (caller) => {
    return caller?.segmentCtx?.currData?.rule;
  }

  /**
   * get selected categories in rule format.
   * e.g.
   * catalog data format = [
   *  {
   *    id: "men",
   *    displayName: "Men",
   *    value: [ "men" ],
   *    isSelected: true
   *  },
   *  {
   *    id: "kids",
   *    displayName: "Kids",
   *    value: [ "men" ],
   *    isSelected: false
   *  },
   *  {
   *    id: "women",
   *    displayName: "Women",
   *    value: [ "women", "women's" ],    // this is a corner case. most of the time there's only one item.
   *    isSelected: true
   *  },
   *  ...
   * ]
   * 
   *  to
   * 
   *  rule data format = [
   *  {
   *    field: "z1_pdp_top_category_vector",
   *    operator: "intersects",
   *    value: "men|women|women's"      // NOTE: kids is not selected
   *  },
   *  ...
   * ]
   * @param {Object} caller
   * @param {Object} catalog
   */
  const getSelCatInRuleFormat = (caller, catalog) => {
    if (!catalog?.length) return undefined;

    let rValue = "";
    let sep = "";

    catalog.forEach(c => {
      if (c.isSelected)
      {
        rValue += sep + c.values.join(SEP);
        sep = SEP;
      }
    });

    return rValue === ""
    ? undefined
    : { field: RULE_FIELD, operator: OPERATOR, value: rValue };
  }

  /**
   * get selected categories in catolog format
   * e.g.
   * rule data format = [
   *  {
   *    field: "z1_pdp_top_category_vector",
   *    operator: "intersects",
   *    value: "men|women|women's"
   *  },
   *  ...
   * ]
   * 
   * to 
   * 
   * catalog data format = [
   *  {
   *    id: "men",
   *    displayName: "Men",
   *    value: [ "men" ],
   *    isSelected: true
   *  },
   *  {
   *    id: "women",
   *    displayName: "Women",
   *    value: [ "women", "women's" ],    // this is a corner case. most of the time there's only one item.
   *    isSelected: true
   *  },
   *  ...
   * ]
   * 
   * 
   * 
   * @param {*} caller 
   * @param {*} rule 
   */
  const getSelCatInCatologFormat = (caller, catalog, rule) => {
    if (!rule) return [];

    let ruleCats = rule.find(r => r.field === RULE_FIELD);
    if (!ruleCats) return [];

    let selectedCats = [];

    catalog.forEach(cat => {
      let testValue = ruleCats.value + SEP;
      if (testValue.indexOf(cat.values.join(SEP)+SEP) >= 0)
      {
        selectedCats.push({ ...cat, isSelected: true });
      }
    });

    return selectedCats;
  }

  /**
   * update segement rule to add or remove the category field
   * from rule
   * 
   * @param {object} caller 
   * @param {object} catalog - category object
   */
  const updateSegmentRule = (caller, catalog) => {
    let rule = getSelCatInRuleFormat(caller, catalog);

    // handle at least one category is selecte
    if (rule)
    {
      let cRule = getRuleObject(caller).filter(item => 
        item.field === RULE_FIELD
      );

      // if rule existed, update it's value
      if (cRule.length) cRule[0].value = rule.value;
      // if rule not existed, create a new rule.
      else caller.segmentCtx.currData.rule.push(rule);
    }
    // handle no category is selected
    else
    {
      caller.segmentCtx.currData.rule = 
        caller.segmentCtx.currData.rule.filter(item =>
          item.field !== RULE_FIELD
        );
    }

    topic.publish(caller.dataChangeStatusUri, {
      isChanged: true,
      data: getRuleObject(caller),
      unqId: caller.segmentCtx.unqId
    });
  }

  const unselectAllCategories = (caller) => {
    caller.setDataPrev();
    caller.d.map(cat => {
      cat.inst._setValue(false);
      cat.isSelected = false;
    });

    let rules = getRuleObject(caller);
    caller.segmentCtx.currData.rule = rules.filter(r => 
      r.field !== RULE_FIELD
    );

    caller.segmentCtx.setCurrData({ rule: caller.segmentCtx.currData.rule });
  }

  const resetUnselectAllCategories = (caller) => {
    let selectedRule = {};
    let sep = "";

    caller.d.map(cat => {
      let prevCat = caller.dPrev.find(p => p.id === cat.id);

      if (prevCat)
      {
        cat.inst._setValue(prevCat.isSelected);
        cat.isSelected = prevCat.isSelected;

        if (prevCat.isSelected)
        {
          selectedRule.value += sep + cat.values.join(SEP);
          sep = SEP;
        }
      }
    });

    if (selectedRule.value)
    {
      let rules = getRuleObject(caller);
      rules = rules.filter(r => r.field !== RULE_FIELD);

      rules.push({
        field: RULE_FIELD,
        operator: OPERATOR,
        value: selectedRule.value,
      });

      caller.segmentCtx.setCurrData({ rule: rules });
    }
  }

  /**
   * State + Context for Audience Filter Catalog Selector.
   * This class manages the state for Audience Filter Caltalog editor.
   * - Create an instance of this class in the Caltalog Selector  editor.
   * - State changes are broadcasted. If needed Catalog Selector editor can subscribe
   * to the state changes.
   * Usage:
   * this.catalogSelectorCtx = new catalogSelectorCtx({
   *   _parent: this,
   *   dataChangeStatusUri: [optional] {string} [Default: "/catalogSelector/change"]
   *      Subscription url.
   *      This helps to track the changes and get details of whether user
   *      has made any changes or not.
   *      Note: Make sure you subscribe to check the changes
   *      published using subUri
   *   segmentCtx: segmentCtx instance
   * });
   * this.busOutcomesCtx.startup();
   * this.own(this.busOutcomesCtx);
  **/
  return declare(null, {

    constructor: function(params)
    {
      this.inherited(arguments);

      params.subUri = params.subUri || "/catalogSelector/change";

      params.unqId = params.unqId || "_" + Math.ceil(Math.random()*1000);

      lang.mixin(this, params);   // keep this line at last
    },

    startup: function()
    {
      let deferr = new Deferred();

      fetchCatalog(this).then(data => {
        this.setData(data, true);
        this.setDataPrev();

        deferr.resolve(true);
      }, err => {
        deferr.resolve(false);
      });

      return deferr.promise;
    },

    getUnqId: function()
    {
      return this.unqId;
    },

    resetToInitialData: function(data)
    {
      this.d = JSON.parse(JSON.stringify(data))
      this.dPrev = JSON.parse(JSON.stringify(data))
      this.isDataChanged();
    },

    getData: function()
    {
      return this.d || [];
    },

    setData: function(data, isSilent=false)
    {
      // let newObj = JSON.parse(JSON.stringify(this.d))
      // let key = Object.keys(data)[0]
      // newObj[key] = data[key];
      // this.d = JSON.parse(JSON.stringify(newObj))
      this.d = JSON.parse(JSON.stringify(data));
      if (!isSilent) this.isDataChanged(isSilent);
    },

    /**
     * Update the value (check/uncheck) of provided category
     * from context data e.q (this.d)
     * 
     * @param {object} caller 
     * @param {object} category - a category object
     */
    updateCategory: function(category)
    {
      let catObj = this.d.find(d => d.id === category.id);

      if (!catObj) return;

      catObj.isSelected = category.isChecked;

      updateSegmentRule(this, this.d);

      if (this.dataChangeStatusUri)
      {
        topic.publish(this.dataChangeStatusUri+"catalog", {
          reason: "catalogChanged",
          category
        })
      }
    },

    unselectAll: function(isUnselected)
    {
      if (isUnselected) unselectAllCategories(this);
      else resetUnselectAllCategories(this);
    },

    /**
     * Given a list of categores and segment rule
     * return a list of selected categores
     * 
     * @param {Array} list of categories
     * @param {Object} rule 
     * @returns 
     */
    getSelectedInCategories: function(rule)
    {
      return getSelCatInCatologFormat(this, this.d, rule);
    },

    /**
     * Given a list of categores and segment rule
     * return a list of selected categores
     * 
     * @param {Array} list of categories
     * @param {Object} rule 
     * @returns 
     */
    getSelectedInRule: function(catalog)
    {
      return getSelCatInRuleFormat(this, catalog);
    },

    /**
     * Maintain copy of data to detect edit / changes.
     * - Initialize at startup
     * - Set after a save to backend operation is performed.
     */
    setDataPrev: function()
    {
      this.dPrev = this.d.map(d => {
        return {
          ...d,
          inst: undefined
        }
      })
    },

    isDataChanged: function(isSilent=false)
    {
      const removeInst = (data) => {
        let rData =  !isArray(data) 
        ? "" : data.map(d => {
          return {
            ...d, catInst: undefined
          }
        });

        return JSON.stringify(rData);
      }

      //////////////////////////////////
      let curData = removeInst(this.d);
      let prevData = removeInst(this.dPrev);
      let isChanged = curData !== prevData;

      if (!isSilent && isChanged)
      {
        topic.publish(this.subUri, { 
          isChanged: true,
          data: this.d 
        });
      }

      return isChanged;
    },

    /**
     * @returns {string} "new"|"edit"
     */
    getMode: function()
    {
      return "new";
    },


    /**
     * Is a new offer being edited or an
     * existing one.
     * @returns {boolean}
     */
    isNew: function()
    {
      return this.getMode() === "new";
    },

    subscribe: function()
    {
    },

    // .................................
    destroy: function ()
    {
      this.inherited(arguments);
    },

    _end: 0
  });

})
