[{"name": "<PERSON><PERSON><PERSON> log on Event Tracer", "code": "System.print(\"Hello World\");\n"}, {"name": "Get attribute from current User's Profile", "code": "var val = Context.get(\"attributeName\");\n"}, {"name": "Set attribute to current User's Profile", "code": "var val = \"some-value\"; \nContext.put(\"attributeName\", val);\n"}, {"name": "Save current User's Profile", "code": "Context.save();\n"}, {"name": "Fire a new Event", "code": "var data = { attr1: \"val1\", attr2: 5 };\nSystem.fireEvent(\"myEvent1\", data);\n"}, {"name": "Get profile ID from a given key", "code": "// The key names can be \"id\", \"email\", \"phone\" or any other custom key used\nvar profileId = System.getProfileId(\"id\", \"customer-id\");\n"}, {"name": "Get attribute value from the system context", "code": "var val = System.getAttribute(\"attributeName\");\n"}, {"name": "Set attribute value to the system context", "code": "var val = \"some-value\"; \nSystem.putAttribute(\"attributeName\", \"val\");\n"}, {"name": "Get attribute value from a Profile", "code": "var val = System.getProfileAttribute(\"profileId\", \"attributeName\");\n"}, {"name": "Set attribute value to a Profile", "code": "var val = \"some-value\"; \nSystem.putProfileAttribute(\"profileId\", \"attributeName\", \"val\");\n"}, {"name": "Get attribute value from the system cache", "code": "var val = System.getFromCache(\"attributeName\");\n"}, {"name": "Set attribute value to the system cache", "code": "var val = \"some-value\"; \nSystem.putToCache(\"attributeName\", \"val\");\n"}, {"name": "Fire an Event for a profile", "code": "var data = { attr1: \"val1\", attr2: 5 };\nSystem.fireEvent(\"profileId\", \"myEvent1\", data);\n"}, {"name": "Convert a string data to JSON object", "code": "var obj = JSON.parse(response);\n"}, {"name": "Get the predicted score from a pipeline", "code": "var ML = System.ml();\nif(ML) var prediction = ML.getPrediction(\"pipelineName\");"}, {"name": "Get the Price Sensitivity score", "code": "var psScore = Context.getPriceSensitivityScore();\n"}, {"name": "Get the value of a parameter stored in a list of properties", "code": "var paramValue = System.getListData(\"listName\", \"paramName\");\n"}, {"name": "Get the entire list of properties and its values", "code": "var lopObject = System.getListData(\"listName\");\n"}, {"name": "Update the list of property", "code": "System.updateLOP(\"property list name\",\"key\",\"value\");"}]